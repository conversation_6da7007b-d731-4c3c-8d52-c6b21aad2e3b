// API Utility for Evidence Protection System
// Handles all API communications with proper error handling and authentication

class ApiClient {
    constructor() {
        this.baseUrl = '/api';
        this.token = null;
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
        
        this.init();
    }

    init() {
        // Get token from storage
        this.token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
        
        // Setup request interceptor
        this.setupInterceptors();
    }

    setupInterceptors() {
        // Add token to requests if available
        const originalFetch = window.fetch;
        
        window.fetch = async (url, options = {}) => {
            // Add base URL if relative
            if (url.startsWith('/')) {
                url = this.baseUrl + url;
            }
            
            // Add default headers
            options.headers = {
                ...this.defaultHeaders,
                ...options.headers
            };
            
            // Add authorization header if token exists
            if (this.token) {
                options.headers['Authorization'] = `Bearer ${this.token}`;
            }
            
            try {
                const response = await originalFetch(url, options);
                
                // Handle authentication errors
                if (response.status === 401) {
                    this.handleAuthError();
                    throw new Error('Authentication required');
                }
                
                return response;
            } catch (error) {
                console.error('API request failed:', error);
                throw error;
            }
        };
    }

    setToken(token) {
        this.token = token;
    }

    clearToken() {
        this.token = null;
    }

    handleAuthError() {
        // Clear stored tokens
        localStorage.removeItem('authToken');
        localStorage.removeItem('currentUser');
        sessionStorage.removeItem('authToken');
        sessionStorage.removeItem('currentUser');
        
        // Redirect to login
        if (window.router) {
            window.router.navigate('/login');
        } else {
            window.location.href = '/public/login.html';
        }
    }

    async request(method, endpoint, data = null, options = {}) {
        const config = {
            method: method.toUpperCase(),
            ...options
        };

        if (data) {
            if (data instanceof FormData) {
                config.body = data;
                // Remove Content-Type header for FormData (browser will set it with boundary)
                delete config.headers?.['Content-Type'];
            } else {
                config.body = JSON.stringify(data);
            }
        }

        try {
            const response = await fetch(endpoint, config);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    errorData.message || `HTTP ${response.status}: ${response.statusText}`,
                    response.status,
                    errorData
                );
            }

            // Handle empty responses
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            } else {
                return await response.text();
            }
        } catch (error) {
            if (error instanceof ApiError) {
                throw error;
            }
            
            // Network or other errors
            throw new ApiError(
                error.message || 'Network error occurred',
                0,
                { originalError: error }
            );
        }
    }

    // HTTP method shortcuts
    async get(endpoint, options = {}) {
        return this.request('GET', endpoint, null, options);
    }

    async post(endpoint, data, options = {}) {
        return this.request('POST', endpoint, data, options);
    }

    async put(endpoint, data, options = {}) {
        return this.request('PUT', endpoint, data, options);
    }

    async patch(endpoint, data, options = {}) {
        return this.request('PATCH', endpoint, data, options);
    }

    async delete(endpoint, options = {}) {
        return this.request('DELETE', endpoint, null, options);
    }

    // Authentication endpoints
    async login(credentials) {
        return this.post('/auth/login', credentials);
    }

    async logout() {
        return this.post('/auth/logout');
    }

    async validateToken() {
        return this.post('/auth/validate');
    }

    async refreshToken() {
        return this.post('/auth/refresh');
    }

    // Evidence endpoints
    async uploadEvidence(formData, onProgress = null) {
        const config = {
            headers: {} // Let browser set Content-Type for FormData
        };

        // Add progress tracking if supported
        if (onProgress && typeof onProgress === 'function') {
            // Note: Progress tracking would require XMLHttpRequest or a fetch wrapper
            // This is a simplified version
            config.onUploadProgress = onProgress;
        }

        return this.post('/evidence/upload', formData, config);
    }

    async getEvidence(filters = {}) {
        const queryParams = new URLSearchParams(filters).toString();
        const endpoint = queryParams ? `/evidence?${queryParams}` : '/evidence';
        return this.get(endpoint);
    }

    async getEvidenceById(id) {
        return this.get(`/evidence/${id}`);
    }

    async verifyEvidence(id) {
        return this.post(`/evidence/${id}/verify`);
    }

    async downloadEvidence(id) {
        const response = await fetch(`${this.baseUrl}/evidence/${id}/download`, {
            headers: {
                'Authorization': `Bearer ${this.token}`
            }
        });

        if (!response.ok) {
            throw new ApiError('Download failed', response.status);
        }

        return response.blob();
    }

    // User endpoints
    async getCurrentUser() {
        return this.get('/user/profile');
    }

    async updateProfile(userData) {
        return this.put('/user/profile', userData);
    }

    async changePassword(passwordData) {
        return this.post('/user/change-password', passwordData);
    }

    async getUserActivity(filters = {}) {
        const queryParams = new URLSearchParams(filters).toString();
        const endpoint = queryParams ? `/user/activity?${queryParams}` : '/user/activity';
        return this.get(endpoint);
    }

    // Admin endpoints
    async getUsers(filters = {}) {
        const queryParams = new URLSearchParams(filters).toString();
        const endpoint = queryParams ? `/admin/users?${queryParams}` : '/admin/users';
        return this.get(endpoint);
    }

    async createUser(userData) {
        return this.post('/admin/users', userData);
    }

    async updateUser(id, userData) {
        return this.put(`/admin/users/${id}`, userData);
    }

    async deleteUser(id) {
        return this.delete(`/admin/users/${id}`);
    }

    async getSystemStats() {
        return this.get('/admin/stats');
    }

    async getSystemLogs(filters = {}) {
        const queryParams = new URLSearchParams(filters).toString();
        const endpoint = queryParams ? `/admin/logs?${queryParams}` : '/admin/logs';
        return this.get(endpoint);
    }

    async getBlockchainStatus() {
        return this.get('/admin/blockchain/status');
    }

    async exportData(options = {}) {
        return this.post('/admin/export', options);
    }

    // Utility methods
    async healthCheck() {
        return this.get('/health');
    }

    async getSystemInfo() {
        return this.get('/system/info');
    }

    // File upload with progress
    uploadWithProgress(endpoint, formData, onProgress) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();

            // Setup progress handler
            if (onProgress) {
                xhr.upload.addEventListener('progress', (event) => {
                    if (event.lengthComputable) {
                        const percentComplete = (event.loaded / event.total) * 100;
                        onProgress(percentComplete, event.loaded, event.total);
                    }
                });
            }

            // Setup completion handlers
            xhr.addEventListener('load', () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (error) {
                        resolve(xhr.responseText);
                    }
                } else {
                    reject(new ApiError(`HTTP ${xhr.status}: ${xhr.statusText}`, xhr.status));
                }
            });

            xhr.addEventListener('error', () => {
                reject(new ApiError('Network error occurred', 0));
            });

            xhr.addEventListener('abort', () => {
                reject(new ApiError('Upload aborted', 0));
            });

            // Setup request
            xhr.open('POST', `${this.baseUrl}${endpoint}`);
            
            // Add authorization header
            if (this.token) {
                xhr.setRequestHeader('Authorization', `Bearer ${this.token}`);
            }

            // Send request
            xhr.send(formData);
        });
    }
}

// Custom API Error class
class ApiError extends Error {
    constructor(message, status, data = {}) {
        super(message);
        this.name = 'ApiError';
        this.status = status;
        this.data = data;
    }

    isNetworkError() {
        return this.status === 0;
    }

    isAuthError() {
        return this.status === 401;
    }

    isForbiddenError() {
        return this.status === 403;
    }

    isNotFoundError() {
        return this.status === 404;
    }

    isServerError() {
        return this.status >= 500;
    }

    isClientError() {
        return this.status >= 400 && this.status < 500;
    }
}

// Create global API instance
const api = new ApiClient();

// Make API available globally
if (typeof window !== 'undefined') {
    window.api = api;
    window.ApiError = ApiError;
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ApiClient, ApiError, api };
}

// API helper functions for common operations
const apiHelpers = {
    // Handle API errors with user-friendly messages
    handleApiError(error, defaultMessage = 'An error occurred') {
        let message = defaultMessage;
        
        if (error instanceof ApiError) {
            if (error.isNetworkError()) {
                message = 'Network connection error. Please check your internet connection.';
            } else if (error.isAuthError()) {
                message = 'Authentication required. Please log in again.';
            } else if (error.isForbiddenError()) {
                message = 'You do not have permission to perform this action.';
            } else if (error.isNotFoundError()) {
                message = 'The requested resource was not found.';
            } else if (error.isServerError()) {
                message = 'Server error occurred. Please try again later.';
            } else {
                message = error.message || defaultMessage;
            }
        } else {
            message = error.message || defaultMessage;
        }
        
        if (typeof showToast === 'function') {
            showToast(message, 'error');
        } else {
            console.error('API Error:', message);
        }
        
        return message;
    },

    // Retry API calls with exponential backoff
    async retryApiCall(apiCall, maxRetries = 3, baseDelay = 1000) {
        let lastError;
        
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return await apiCall();
            } catch (error) {
                lastError = error;
                
                // Don't retry client errors (4xx) except 429 (rate limit)
                if (error instanceof ApiError && error.isClientError() && error.status !== 429) {
                    throw error;
                }
                
                // Don't retry on last attempt
                if (attempt === maxRetries) {
                    throw error;
                }
                
                // Calculate delay with exponential backoff
                const delay = baseDelay * Math.pow(2, attempt);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        
        throw lastError;
    }
};

// Make helpers available globally
if (typeof window !== 'undefined') {
    window.apiHelpers = apiHelpers;
}
