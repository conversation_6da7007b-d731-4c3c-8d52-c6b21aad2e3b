<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Evidence Protection System</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/auth-nav.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Meta tags for security -->
    <meta name="description" content="Evidence Protection System Dashboard - Secure evidence management overview">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Security headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com;">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/favicon.ico">
</head>
<body class="auth-layout nav-state-authenticated">
    <!-- Minimal Navigation Container -->
    <nav class="navbar auth-navbar" id="navbar">
        <div class="nav-container" id="nav-container">
            <!-- Navigation will be loaded here by NavigationManager -->
        </div>
    </nav>

    <!-- Admin Sidebar (for admin users) -->
    <div id="admin-sidebar-wrapper" class="admin-sidebar-wrapper" style="display: none;">
        <div class="admin-sidebar" id="admin-sidebar">
            <!-- Admin sidebar will be loaded here for admin users -->
        </div>
    </div>

    <!-- Main Content Area -->
    <main class="main-content auth-main" id="main-content">
        <!-- Dashboard Content -->
        <div class="dashboard-container">
            <!-- Page Header -->
            <div class="page-header">
                <div class="header-content">
                    <h1><i class="fas fa-chart-line"></i> Dashboard</h1>
                    <p>Evidence Protection System Overview</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-secondary" id="refresh-dashboard">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                    <div class="last-updated">
                        Last updated: <span id="last-updated-time">--</span>
                    </div>
                </div>
            </div>

            <!-- Statistics Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-folder-tree"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="total-evidence">--</h3>
                        <p>Total Evidence</p>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up"></i>
                            <span>+12% this month</span>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-certificate"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="verified-evidence">--</h3>
                        <p>Verified Evidence</p>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up"></i>
                            <span>+8% this month</span>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock-rotate-left"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="recent-uploads">--</h3>
                        <p>Recent Uploads</p>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up"></i>
                            <span>+5 today</span>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-cubes"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="blockchain-status">Checking...</h3>
                        <p>Blockchain Status</p>
                        <div class="stat-trend">
                            <i class="fas fa-check-circle"></i>
                            <span>Operational</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dashboard Content Grid -->
            <div class="dashboard-content-grid">
                <!-- Recent Activity -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h2><i class="fas fa-clipboard-list"></i> Recent Activity</h2>
                        <button class="btn btn-sm btn-outline" id="view-all-activity">
                            View All
                        </button>
                    </div>
                    <div class="card-content">
                        <div class="activity-list" id="activity-list">
                            <div class="loading-placeholder">
                                <i class="fas fa-spinner fa-spin"></i>
                                Loading recent activity...
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h2><i class="fas fa-bolt"></i> Quick Actions</h2>
                    </div>
                    <div class="card-content">
                        <div class="quick-actions-grid">
                            <button class="quick-action-btn" id="quick-upload" style="display: none;">
                                <i class="fas fa-file-upload"></i>
                                <span>Upload Evidence</span>
                            </button>
                            <button class="quick-action-btn" id="quick-search">
                                <i class="fas fa-search"></i>
                                <span>Search Evidence</span>
                            </button>
                            <button class="quick-action-btn" id="quick-verify">
                                <i class="fas fa-certificate"></i>
                                <span>Verify Evidence</span>
                            </button>
                            <button class="quick-action-btn" id="quick-profile">
                                <i class="fas fa-user-circle"></i>
                                <span>Profile Settings</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- System Status -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h2><i class="fas fa-heartbeat"></i> System Status</h2>
                        <div class="status-indicator" id="system-status">
                            <span class="status-dot status-good"></span>
                            All Systems Operational
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="system-metrics">
                            <div class="metric">
                                <span class="metric-label">API Response Time</span>
                                <span class="metric-value" id="api-response-time">--ms</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Database Status</span>
                                <span class="metric-value" id="database-status">--</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Blockchain Sync</span>
                                <span class="metric-value" id="blockchain-sync">--</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Storage Usage</span>
                                <span class="metric-value" id="storage-usage">--</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Evidence -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h2><i class="fas fa-folder-open"></i> Recent Evidence</h2>
                        <button class="btn btn-sm btn-outline" id="view-all-evidence">
                            View Repository
                        </button>
                    </div>
                    <div class="card-content">
                        <div class="evidence-preview" id="evidence-preview">
                            <div class="loading-placeholder">
                                <i class="fas fa-spinner fa-spin"></i>
                                Loading recent evidence...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Toast Notification Container -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading dashboard...</p>
        </div>
    </div>

    <!-- Core JavaScript -->
    <script src="../js/core/template-loader.js"></script>
    <script src="../js/core/nav-manager.js"></script>
    <script src="../js/core/router.js"></script>
    <script src="../js/core/auth-manager.js"></script>
    
    <!-- Utility Scripts -->
    <script src="../js/utils/utils.js"></script>
    <script src="../js/utils/api.js"></script>
    
    <!-- Application Scripts -->
    <script src="../js/app/app-core.js"></script>
    <script src="../js/dashboard.js"></script>

    <!-- Page-specific JavaScript -->
    <script>
        // Initialize dashboard page
        function initializePage() {
            console.log('Initializing dashboard page');
            
            // Setup quick action handlers
            setupQuickActions();
            
            // Setup refresh handlers
            setupRefreshHandlers();
            
            // Load dashboard data
            loadDashboardData();
            
            // Setup auto-refresh
            setupAutoRefresh();
            
            // Update role-based visibility
            updateRoleBasedVisibility();
        }

        function setupQuickActions() {
            // Upload Evidence
            const quickUpload = document.getElementById('quick-upload');
            if (quickUpload) {
                quickUpload.addEventListener('click', () => {
                    if (window.router) {
                        window.router.navigate('/upload');
                    } else {
                        window.location.href = 'upload.html';
                    }
                });
            }

            // Search Evidence
            const quickSearch = document.getElementById('quick-search');
            if (quickSearch) {
                quickSearch.addEventListener('click', () => {
                    if (window.router) {
                        window.router.navigate('/evidence');
                    } else {
                        window.location.href = 'evidence.html';
                    }
                });
            }

            // Verify Evidence
            const quickVerify = document.getElementById('quick-verify');
            if (quickVerify) {
                quickVerify.addEventListener('click', () => {
                    if (window.router) {
                        window.router.navigate('/verify');
                    } else {
                        window.location.href = 'verify.html';
                    }
                });
            }

            // Profile Settings
            const quickProfile = document.getElementById('quick-profile');
            if (quickProfile) {
                quickProfile.addEventListener('click', () => {
                    if (window.router) {
                        window.router.navigate('/profile');
                    } else {
                        window.location.href = 'profile.html';
                    }
                });
            }

            // View All buttons
            const viewAllActivity = document.getElementById('view-all-activity');
            if (viewAllActivity) {
                viewAllActivity.addEventListener('click', () => {
                    if (typeof showToast === 'function') {
                        showToast('Activity log feature coming soon!', 'info');
                    }
                });
            }

            const viewAllEvidence = document.getElementById('view-all-evidence');
            if (viewAllEvidence) {
                viewAllEvidence.addEventListener('click', () => {
                    if (window.router) {
                        window.router.navigate('/evidence');
                    } else {
                        window.location.href = 'evidence.html';
                    }
                });
            }
        }

        function setupRefreshHandlers() {
            const refreshBtn = document.getElementById('refresh-dashboard');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', () => {
                    loadDashboardData();
                });
            }
        }

        async function loadDashboardData() {
            try {
                // Show loading state
                showLoadingState();
                
                // Load dashboard statistics
                await loadStatistics();
                
                // Load recent activity
                await loadRecentActivity();
                
                // Load recent evidence
                await loadRecentEvidence();
                
                // Load system status
                await loadSystemStatus();
                
                // Update last updated time
                updateLastUpdatedTime();
                
                // Hide loading state
                hideLoadingState();
                
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                if (typeof showToast === 'function') {
                    showToast('Error loading dashboard data', 'error');
                }
                hideLoadingState();
            }
        }

        async function loadStatistics() {
            // Simulate API call for now
            setTimeout(() => {
                document.getElementById('total-evidence').textContent = '1,247';
                document.getElementById('verified-evidence').textContent = '1,198';
                document.getElementById('recent-uploads').textContent = '23';
                document.getElementById('blockchain-status').textContent = 'Healthy';
            }, 500);
        }

        async function loadRecentActivity() {
            const activityList = document.getElementById('activity-list');
            
            // Simulate loading recent activity
            setTimeout(() => {
                activityList.innerHTML = `
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-file-upload"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">Evidence uploaded</div>
                            <div class="activity-meta">Case #2024-001 • 2 hours ago</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-certificate"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">Evidence verified</div>
                            <div class="activity-meta">Case #2024-002 • 4 hours ago</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">New user registered</div>
                            <div class="activity-meta">Officer Johnson • 6 hours ago</div>
                        </div>
                    </div>
                `;
            }, 700);
        }

        async function loadRecentEvidence() {
            const evidencePreview = document.getElementById('evidence-preview');
            
            // Simulate loading recent evidence
            setTimeout(() => {
                evidencePreview.innerHTML = `
                    <div class="evidence-item">
                        <div class="evidence-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="evidence-info">
                            <div class="evidence-name">crime_scene_photo_001.jpg</div>
                            <div class="evidence-meta">Case #2024-001 • Verified</div>
                        </div>
                    </div>
                    <div class="evidence-item">
                        <div class="evidence-icon">
                            <i class="fas fa-video"></i>
                        </div>
                        <div class="evidence-info">
                            <div class="evidence-name">security_footage_main.mp4</div>
                            <div class="evidence-meta">Case #2024-002 • Pending</div>
                        </div>
                    </div>
                    <div class="evidence-item">
                        <div class="evidence-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="evidence-info">
                            <div class="evidence-name">witness_statement.pdf</div>
                            <div class="evidence-meta">Case #2024-003 • Verified</div>
                        </div>
                    </div>
                `;
            }, 900);
        }

        async function loadSystemStatus() {
            // Simulate loading system metrics
            setTimeout(() => {
                document.getElementById('api-response-time').textContent = '45ms';
                document.getElementById('database-status').textContent = 'Online';
                document.getElementById('blockchain-sync').textContent = '100%';
                document.getElementById('storage-usage').textContent = '67%';
            }, 600);
        }

        function updateRoleBasedVisibility() {
            if (window.authManager && window.authManager.canUploadEvidence()) {
                const quickUpload = document.getElementById('quick-upload');
                if (quickUpload) {
                    quickUpload.style.display = 'flex';
                }
            }
        }

        function showLoadingState() {
            // Add loading classes to cards
            document.querySelectorAll('.dashboard-card').forEach(card => {
                card.classList.add('loading');
            });
        }

        function hideLoadingState() {
            // Remove loading classes from cards
            document.querySelectorAll('.dashboard-card').forEach(card => {
                card.classList.remove('loading');
            });
        }

        function updateLastUpdatedTime() {
            const lastUpdatedElement = document.getElementById('last-updated-time');
            if (lastUpdatedElement) {
                lastUpdatedElement.textContent = new Date().toLocaleTimeString();
            }
        }

        function setupAutoRefresh() {
            // Refresh dashboard data every 5 minutes
            setInterval(() => {
                loadDashboardData();
            }, 5 * 60 * 1000);
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // Check authentication first
                if (!window.authManager || !window.authManager.isAuthenticated()) {
                    console.warn('User not authenticated, redirecting to login');
                    if (window.router) {
                        window.router.navigate('/login');
                    } else {
                        window.location.href = '../public/login.html';
                    }
                    return;
                }

                // Initialize template loader and load common components
                if (window.templateLoader) {
                    await window.templateLoader.setupPage('authenticated', 'Dashboard - Evidence Protection System');
                }

                // Initialize page-specific functionality
                initializePage();

                // Hide loading overlay
                const loadingOverlay = document.getElementById('loading-overlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
            } catch (error) {
                console.error('Error initializing dashboard page:', error);
                
                if (typeof showToast === 'function') {
                    showToast('Error loading dashboard. Please refresh and try again.', 'error');
                }
            }
        });
    </script>
</body>
</html>
