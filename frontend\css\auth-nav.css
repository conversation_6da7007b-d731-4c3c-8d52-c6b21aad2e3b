/* Authenticated Navigation Styles */
/* Minimal navigation for post-login state */

.auth-layout .navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 0.5rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s ease;
    height: 60px;
}

.auth-layout .nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
}

/* Minimal Brand Styling */
.auth-layout .nav-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #2c3e50;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.auth-layout .nav-brand:hover {
    opacity: 0.8;
}

.auth-layout .brand-icon {
    font-size: 1.8rem;
    color: #3498db;
}

.auth-layout .brand-name {
    font-size: 1.2rem;
    font-weight: 700;
    letter-spacing: -0.5px;
}

/* Streamlined Navigation Menu */
.auth-layout .nav-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.auth-layout .nav-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    color: #5a6c7d;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.2s ease;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    position: relative;
}

.auth-layout .nav-item:hover {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

.auth-layout .nav-item.active {
    background: rgba(52, 152, 219, 0.15);
    color: #3498db;
}

.auth-layout .nav-item.active::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background: #3498db;
    border-radius: 50%;
}

.auth-layout .nav-item i {
    font-size: 1rem;
}

.auth-layout .nav-item span {
    font-weight: 500;
}

/* Navigation Actions */
.auth-layout .nav-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.auth-layout .nav-profile {
    background: rgba(46, 204, 113, 0.1);
    color: #27ae60;
}

.auth-layout .nav-profile:hover {
    background: rgba(46, 204, 113, 0.2);
}

.auth-layout .nav-logout {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.auth-layout .nav-logout:hover {
    background: rgba(231, 76, 60, 0.2);
}

/* User Info */
.auth-layout .user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 1rem;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 25px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.auth-layout .user-avatar {
    font-size: 1.5rem;
    color: #3498db;
}

.auth-layout .user-details {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.auth-layout .user-name {
    font-size: 0.85rem;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1;
}

.auth-layout .user-role {
    font-size: 0.7rem;
    padding: 0.1rem 0.5rem;
    border-radius: 10px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.2rem;
}

.auth-layout .user-role.role-admin {
    background: rgba(155, 89, 182, 0.2);
    color: #9b59b6;
}

.auth-layout .user-role.role-police {
    background: rgba(52, 152, 219, 0.2);
    color: #3498db;
}

.auth-layout .user-role.role-lawyer {
    background: rgba(46, 204, 113, 0.2);
    color: #27ae60;
}

/* Mobile Toggle */
.auth-layout .nav-mobile-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
    gap: 3px;
}

.auth-layout .nav-mobile-toggle span {
    width: 20px;
    height: 2px;
    background: #5a6c7d;
    border-radius: 1px;
    transition: all 0.3s ease;
}

/* Mobile Menu Overlay */
.auth-layout .nav-mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    z-index: 9999;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    overflow-y: auto;
}

.auth-layout .nav-mobile-overlay.active {
    transform: translateX(0);
}

.auth-layout .nav-mobile-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.auth-layout .nav-mobile-close {
    background: none;
    border: none;
    color: #5a6c7d;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.auth-layout .nav-mobile-close:hover {
    background: rgba(0, 0, 0, 0.05);
}

.auth-layout .nav-mobile-user {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(52, 152, 219, 0.05);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.auth-layout .nav-mobile-user .user-avatar {
    font-size: 2.5rem;
}

.auth-layout .nav-mobile-user .user-name {
    font-size: 1.1rem;
}

.auth-layout .nav-mobile-menu {
    padding: 1rem;
}

.auth-layout .nav-mobile-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    color: #5a6c7d;
    text-decoration: none;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.auth-layout .nav-mobile-item:hover {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

.auth-layout .nav-mobile-item i {
    font-size: 1.2rem;
    width: 25px;
    text-align: center;
}

.auth-layout .nav-mobile-divider {
    height: 1px;
    background: rgba(0, 0, 0, 0.1);
    margin: 1rem 0;
}

.auth-layout .nav-mobile-logout {
    color: #e74c3c;
}

.auth-layout .nav-mobile-logout:hover {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-layout .nav-container {
        padding: 0 1rem;
    }
    
    .auth-layout .nav-menu,
    .auth-layout .nav-actions,
    .auth-layout .user-info {
        display: none;
    }
    
    .auth-layout .nav-mobile-toggle {
        display: flex;
    }
    
    .auth-layout .brand-name {
        font-size: 1rem;
    }
    
    .auth-layout .brand-icon {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .auth-layout .nav-container {
        padding: 0 0.5rem;
    }
    
    .auth-layout .nav-mobile-menu {
        padding: 0.5rem;
    }
    
    .auth-layout .nav-mobile-user {
        padding: 1rem;
    }
}

/* Animation for mobile toggle */
.nav-mobile-open .nav-mobile-toggle span:nth-child(1) {
    transform: rotate(45deg) translate(4px, 4px);
}

.nav-mobile-open .nav-mobile-toggle span:nth-child(2) {
    opacity: 0;
}

.nav-mobile-open .nav-mobile-toggle span:nth-child(3) {
    transform: rotate(-45deg) translate(5px, -5px);
}

/* Main content adjustment for fixed navbar */
.auth-layout .main-content {
    margin-top: 60px;
    min-height: calc(100vh - 60px);
}

/* Admin sidebar layout adjustments */
.auth-layout.has-admin-sidebar .main-content {
    margin-left: 250px;
    transition: margin-left 0.3s ease;
}

.auth-layout.has-admin-sidebar.sidebar-collapsed .main-content {
    margin-left: 60px;
}

@media (max-width: 768px) {
    .auth-layout.has-admin-sidebar .main-content {
        margin-left: 0;
    }
}

/* Compact mode for smaller screens */
@media (max-width: 1024px) {
    .auth-layout .nav-item span {
        display: none;
    }
    
    .auth-layout .nav-item {
        padding: 0.5rem;
        min-width: 40px;
        justify-content: center;
    }
    
    .auth-layout .nav-profile .nav-profile-text,
    .auth-layout .nav-logout .nav-logout-text {
        display: none;
    }
}

/* Focus states for accessibility */
.auth-layout .nav-item:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

.auth-layout .nav-mobile-toggle:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

/* Smooth transitions */
.auth-layout * {
    transition: all 0.2s ease;
}

/* Prevent body scroll when mobile menu is open */
.nav-mobile-open {
    overflow: hidden;
}
