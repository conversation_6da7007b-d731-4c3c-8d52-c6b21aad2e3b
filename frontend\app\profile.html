<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Settings - Evidence Protection System</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/auth-nav.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Meta tags for security -->
    <meta name="description" content="Profile Settings - Manage your account settings and preferences">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Security headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/favicon.ico">
</head>
<body class="auth-layout nav-state-authenticated">
    <!-- Navigation Container -->
    <nav class="navbar auth-navbar" id="navbar">
        <div class="nav-container" id="nav-container">
            <!-- Navigation will be loaded here by NavigationManager -->
        </div>
    </nav>

    <!-- Admin Sidebar (for admin users) -->
    <div id="admin-sidebar-wrapper" class="admin-sidebar-wrapper" style="display: none;">
        <div class="admin-sidebar" id="admin-sidebar">
            <!-- Admin sidebar will be loaded here for admin users -->
        </div>
    </div>

    <!-- Main Content Area -->
    <main class="main-content auth-main" id="main-content">
        <div class="profile-container">
            <!-- Page Header -->
            <div class="page-header">
                <h1><i class="fas fa-user-circle"></i> Profile Settings</h1>
                <p>Manage your account settings and preferences</p>
            </div>

            <div class="profile-content">
                <!-- Profile Sidebar -->
                <div class="profile-sidebar">
                    <div class="profile-avatar">
                        <div class="avatar-circle">
                            <i class="fas fa-user"></i>
                        </div>
                        <button class="btn btn-sm btn-outline" id="change-avatar">
                            <i class="fas fa-camera"></i>
                            Change Photo
                        </button>
                    </div>
                    
                    <div class="profile-nav">
                        <button class="profile-nav-item active" data-section="personal">
                            <i class="fas fa-user"></i>
                            Personal Information
                        </button>
                        <button class="profile-nav-item" data-section="security">
                            <i class="fas fa-shield-alt"></i>
                            Security Settings
                        </button>
                        <button class="profile-nav-item" data-section="preferences">
                            <i class="fas fa-cog"></i>
                            Preferences
                        </button>
                        <button class="profile-nav-item" data-section="activity">
                            <i class="fas fa-history"></i>
                            Activity Log
                        </button>
                    </div>
                </div>

                <!-- Profile Main Content -->
                <div class="profile-main">
                    <!-- Personal Information Section -->
                    <div class="profile-section active" id="personal-section">
                        <div class="section-header">
                            <h2>Personal Information</h2>
                            <p>Update your personal details and contact information</p>
                        </div>
                        
                        <form class="profile-form" id="personal-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="first-name">First Name</label>
                                    <input type="text" id="first-name" name="firstName" required>
                                </div>
                                <div class="form-group">
                                    <label for="last-name">Last Name</label>
                                    <input type="text" id="last-name" name="lastName" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="email">Email Address</label>
                                <input type="email" id="email" name="email" required>
                                <small>This email is used for notifications and account recovery</small>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <input type="tel" id="phone" name="phone">
                                </div>
                                <div class="form-group">
                                    <label for="badge-number">Badge/ID Number</label>
                                    <input type="text" id="badge-number" name="badgeNumber">
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="department">Department</label>
                                    <input type="text" id="department" name="department">
                                </div>
                                <div class="form-group">
                                    <label for="position">Position/Rank</label>
                                    <input type="text" id="position" name="position">
                                </div>
                            </div>
                            
                            <div class="form-actions">
                                <button type="button" class="btn btn-secondary" id="reset-personal">
                                    <i class="fas fa-undo"></i>
                                    Reset
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Save Changes
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Security Settings Section -->
                    <div class="profile-section" id="security-section">
                        <div class="section-header">
                            <h2>Security Settings</h2>
                            <p>Manage your password and security preferences</p>
                        </div>
                        
                        <!-- Change Password -->
                        <div class="security-card">
                            <h3><i class="fas fa-key"></i> Change Password</h3>
                            <form class="security-form" id="password-form">
                                <div class="form-group">
                                    <label for="current-password">Current Password</label>
                                    <input type="password" id="current-password" name="currentPassword" required>
                                </div>
                                <div class="form-group">
                                    <label for="new-password">New Password</label>
                                    <input type="password" id="new-password" name="newPassword" required>
                                    <div class="password-strength" id="password-strength"></div>
                                </div>
                                <div class="form-group">
                                    <label for="confirm-password">Confirm New Password</label>
                                    <input type="password" id="confirm-password" name="confirmPassword" required>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-key"></i>
                                    Update Password
                                </button>
                            </form>
                        </div>

                        <!-- Two-Factor Authentication -->
                        <div class="security-card">
                            <h3><i class="fas fa-mobile-alt"></i> Two-Factor Authentication</h3>
                            <div class="security-option">
                                <div class="option-info">
                                    <h4>SMS Authentication</h4>
                                    <p>Receive verification codes via SMS</p>
                                </div>
                                <div class="option-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="sms-2fa">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                            <div class="security-option">
                                <div class="option-info">
                                    <h4>Email Authentication</h4>
                                    <p>Receive verification codes via email</p>
                                </div>
                                <div class="option-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="email-2fa">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Session Management -->
                        <div class="security-card">
                            <h3><i class="fas fa-clock"></i> Session Management</h3>
                            <div class="session-info">
                                <p><strong>Current Session:</strong> Started at <span id="session-start">--</span></p>
                                <p><strong>Last Activity:</strong> <span id="last-activity">--</span></p>
                            </div>
                            <div class="session-actions">
                                <button class="btn btn-outline" id="logout-all-devices">
                                    <i class="fas fa-sign-out-alt"></i>
                                    Logout All Devices
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Preferences Section -->
                    <div class="profile-section" id="preferences-section">
                        <div class="section-header">
                            <h2>Preferences</h2>
                            <p>Customize your application experience</p>
                        </div>
                        
                        <form class="preferences-form" id="preferences-form">
                            <!-- Notification Preferences -->
                            <div class="preference-group">
                                <h3><i class="fas fa-bell"></i> Notifications</h3>
                                <div class="preference-option">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="email-notifications" checked>
                                        <span class="checkmark"></span>
                                        Email notifications for evidence uploads
                                    </label>
                                </div>
                                <div class="preference-option">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="verification-notifications" checked>
                                        <span class="checkmark"></span>
                                        Notifications for verification requests
                                    </label>
                                </div>
                                <div class="preference-option">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="system-notifications">
                                        <span class="checkmark"></span>
                                        System maintenance notifications
                                    </label>
                                </div>
                            </div>

                            <!-- Display Preferences -->
                            <div class="preference-group">
                                <h3><i class="fas fa-palette"></i> Display</h3>
                                <div class="preference-option">
                                    <label for="theme">Theme</label>
                                    <select id="theme" name="theme">
                                        <option value="light">Light</option>
                                        <option value="dark">Dark</option>
                                        <option value="auto">Auto (System)</option>
                                    </select>
                                </div>
                                <div class="preference-option">
                                    <label for="language">Language</label>
                                    <select id="language" name="language">
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                    </select>
                                </div>
                                <div class="preference-option">
                                    <label for="timezone">Timezone</label>
                                    <select id="timezone" name="timezone">
                                        <option value="UTC">UTC</option>
                                        <option value="America/New_York">Eastern Time</option>
                                        <option value="America/Chicago">Central Time</option>
                                        <option value="America/Denver">Mountain Time</option>
                                        <option value="America/Los_Angeles">Pacific Time</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Save Preferences
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Activity Log Section -->
                    <div class="profile-section" id="activity-section">
                        <div class="section-header">
                            <h2>Activity Log</h2>
                            <p>View your recent account activity</p>
                        </div>
                        
                        <div class="activity-filters">
                            <select id="activity-filter">
                                <option value="all">All Activities</option>
                                <option value="login">Login Events</option>
                                <option value="upload">Evidence Uploads</option>
                                <option value="verification">Verifications</option>
                                <option value="settings">Settings Changes</option>
                            </select>
                            <button class="btn btn-secondary" id="refresh-activity">
                                <i class="fas fa-sync-alt"></i>
                                Refresh
                            </button>
                        </div>
                        
                        <div class="activity-log" id="activity-log">
                            <!-- Activity items will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Toast Notification Container -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Updating profile...</p>
        </div>
    </div>

    <!-- Core JavaScript -->
    <script src="../js/core/template-loader.js"></script>
    <script src="../js/core/nav-manager.js"></script>
    <script src="../js/core/router.js"></script>
    <script src="../js/core/auth-manager.js"></script>
    
    <!-- Utility Scripts -->
    <script src="../js/utils/utils.js"></script>
    <script src="../js/utils/api.js"></script>
    
    <!-- Application Scripts -->
    <script src="../js/app/app-core.js"></script>

    <!-- Page-specific JavaScript -->
    <script>
        let currentUser = null;

        function initializePage() {
            console.log('Initializing profile page');
            
            currentUser = window.authManager.getCurrentUser();
            if (!currentUser) {
                if (window.router) {
                    window.router.navigate('/login');
                } else {
                    window.location.href = '../public/login.html';
                }
                return;
            }
            
            setupNavigation();
            setupEventHandlers();
            loadUserData();
            loadActivityLog();
        }

        function setupNavigation() {
            const navItems = document.querySelectorAll('.profile-nav-item');
            
            navItems.forEach(item => {
                item.addEventListener('click', () => {
                    const section = item.dataset.section;
                    switchSection(section);
                });
            });
        }

        function switchSection(sectionName) {
            // Update navigation
            document.querySelectorAll('.profile-nav-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');
            
            // Update content
            document.querySelectorAll('.profile-section').forEach(section => {
                section.classList.remove('active');
            });
            document.getElementById(`${sectionName}-section`).classList.add('active');
        }

        function setupEventHandlers() {
            // Personal information form
            document.getElementById('personal-form').addEventListener('submit', handlePersonalFormSubmit);
            document.getElementById('reset-personal').addEventListener('click', loadUserData);
            
            // Password form
            document.getElementById('password-form').addEventListener('submit', handlePasswordFormSubmit);
            document.getElementById('new-password').addEventListener('input', checkPasswordStrength);
            
            // Preferences form
            document.getElementById('preferences-form').addEventListener('submit', handlePreferencesFormSubmit);
            
            // Security options
            document.getElementById('logout-all-devices').addEventListener('click', logoutAllDevices);
            
            // Activity log
            document.getElementById('refresh-activity').addEventListener('click', loadActivityLog);
            document.getElementById('activity-filter').addEventListener('change', loadActivityLog);
            
            // Avatar change
            document.getElementById('change-avatar').addEventListener('click', () => {
                if (typeof showToast === 'function') {
                    showToast('Avatar change feature coming soon!', 'info');
                }
            });
        }

        function loadUserData() {
            if (!currentUser) return;
            
            // Load personal information
            document.getElementById('first-name').value = currentUser.firstName || '';
            document.getElementById('last-name').value = currentUser.lastName || '';
            document.getElementById('email').value = currentUser.email || '';
            document.getElementById('phone').value = currentUser.phone || '';
            document.getElementById('badge-number').value = currentUser.badgeNumber || '';
            document.getElementById('department').value = currentUser.department || '';
            document.getElementById('position').value = currentUser.position || '';
            
            // Load session information
            document.getElementById('session-start').textContent = new Date().toLocaleString();
            document.getElementById('last-activity').textContent = 'Just now';
        }

        async function handlePersonalFormSubmit(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const userData = Object.fromEntries(formData);
            
            try {
                showLoadingOverlay('Updating profile...');
                
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Update current user data
                Object.assign(currentUser, userData);
                
                if (typeof showToast === 'function') {
                    showToast('Profile updated successfully!', 'success');
                }
                
                hideLoadingOverlay();
                
            } catch (error) {
                console.error('Error updating profile:', error);
                if (typeof showToast === 'function') {
                    showToast('Error updating profile', 'error');
                }
                hideLoadingOverlay();
            }
        }

        async function handlePasswordFormSubmit(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const currentPassword = formData.get('currentPassword');
            const newPassword = formData.get('newPassword');
            const confirmPassword = formData.get('confirmPassword');
            
            // Validate passwords
            if (newPassword !== confirmPassword) {
                if (typeof showToast === 'function') {
                    showToast('New passwords do not match', 'error');
                }
                return;
            }
            
            if (newPassword.length < 8) {
                if (typeof showToast === 'function') {
                    showToast('Password must be at least 8 characters long', 'error');
                }
                return;
            }
            
            try {
                showLoadingOverlay('Updating password...');
                
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                if (typeof showToast === 'function') {
                    showToast('Password updated successfully!', 'success');
                }
                
                // Reset form
                event.target.reset();
                
                hideLoadingOverlay();
                
            } catch (error) {
                console.error('Error updating password:', error);
                if (typeof showToast === 'function') {
                    showToast('Error updating password', 'error');
                }
                hideLoadingOverlay();
            }
        }

        function checkPasswordStrength() {
            const password = document.getElementById('new-password').value;
            const strengthIndicator = document.getElementById('password-strength');
            
            let strength = 0;
            let feedback = [];
            
            if (password.length >= 8) strength++;
            else feedback.push('At least 8 characters');
            
            if (/[a-z]/.test(password)) strength++;
            else feedback.push('Lowercase letter');
            
            if (/[A-Z]/.test(password)) strength++;
            else feedback.push('Uppercase letter');
            
            if (/[0-9]/.test(password)) strength++;
            else feedback.push('Number');
            
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            else feedback.push('Special character');
            
            const strengthLevels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
            const strengthColors = ['#ff4757', '#ff6b6b', '#ffa502', '#2ed573', '#20bf6b'];
            
            strengthIndicator.innerHTML = `
                <div class="strength-bar">
                    <div class="strength-fill" style="width: ${(strength / 5) * 100}%; background: ${strengthColors[strength - 1] || '#ff4757'}"></div>
                </div>
                <div class="strength-text">
                    <span>Strength: ${strengthLevels[strength - 1] || 'Very Weak'}</span>
                    ${feedback.length > 0 ? `<span class="strength-feedback">Missing: ${feedback.join(', ')}</span>` : ''}
                </div>
            `;
        }

        async function handlePreferencesFormSubmit(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const preferences = Object.fromEntries(formData);
            
            try {
                showLoadingOverlay('Saving preferences...');
                
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                if (typeof showToast === 'function') {
                    showToast('Preferences saved successfully!', 'success');
                }
                
                hideLoadingOverlay();
                
            } catch (error) {
                console.error('Error saving preferences:', error);
                if (typeof showToast === 'function') {
                    showToast('Error saving preferences', 'error');
                }
                hideLoadingOverlay();
            }
        }

        async function logoutAllDevices() {
            if (!confirm('Are you sure you want to logout from all devices? You will need to login again.')) {
                return;
            }
            
            try {
                showLoadingOverlay('Logging out from all devices...');
                
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                if (typeof showToast === 'function') {
                    showToast('Logged out from all devices successfully', 'success');
                }
                
                // Logout current session
                setTimeout(() => {
                    if (window.authManager) {
                        window.authManager.logout();
                    }
                }, 2000);
                
                hideLoadingOverlay();
                
            } catch (error) {
                console.error('Error logging out from all devices:', error);
                if (typeof showToast === 'function') {
                    showToast('Error logging out from all devices', 'error');
                }
                hideLoadingOverlay();
            }
        }

        async function loadActivityLog() {
            const activityLog = document.getElementById('activity-log');
            const filter = document.getElementById('activity-filter').value;
            
            try {
                activityLog.innerHTML = '<div class="loading-placeholder"><i class="fas fa-spinner fa-spin"></i> Loading activity...</div>';
                
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Mock activity data
                const activities = [
                    {
                        type: 'login',
                        description: 'Logged in from Chrome on Windows',
                        timestamp: new Date('2024-01-15T10:30:00Z'),
                        ip: '*************'
                    },
                    {
                        type: 'upload',
                        description: 'Uploaded evidence: crime_scene_photo_001.jpg',
                        timestamp: new Date('2024-01-15T09:15:00Z'),
                        details: 'Case: CASE-2024-001'
                    },
                    {
                        type: 'verification',
                        description: 'Verified evidence: EV001',
                        timestamp: new Date('2024-01-14T16:45:00Z'),
                        details: 'Blockchain verification successful'
                    },
                    {
                        type: 'settings',
                        description: 'Updated profile information',
                        timestamp: new Date('2024-01-14T14:20:00Z'),
                        details: 'Changed email address'
                    }
                ];
                
                const filteredActivities = filter === 'all' ? activities : activities.filter(a => a.type === filter);
                
                if (filteredActivities.length === 0) {
                    activityLog.innerHTML = '<div class="no-activity"><i class="fas fa-history"></i><p>No activity found</p></div>';
                    return;
                }
                
                const activityHTML = filteredActivities.map(activity => `
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-${getActivityIcon(activity.type)}"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-description">${activity.description}</div>
                            <div class="activity-meta">
                                <span class="activity-time">${activity.timestamp.toLocaleString()}</span>
                                ${activity.ip ? `<span class="activity-ip">IP: ${activity.ip}</span>` : ''}
                                ${activity.details ? `<span class="activity-details">${activity.details}</span>` : ''}
                            </div>
                        </div>
                    </div>
                `).join('');
                
                activityLog.innerHTML = activityHTML;
                
            } catch (error) {
                console.error('Error loading activity log:', error);
                activityLog.innerHTML = '<div class="error-message">Error loading activity log</div>';
            }
        }

        function getActivityIcon(type) {
            const icons = {
                login: 'sign-in-alt',
                upload: 'file-upload',
                verification: 'certificate',
                settings: 'cog'
            };
            return icons[type] || 'circle';
        }

        function showLoadingOverlay(message) {
            const overlay = document.getElementById('loading-overlay');
            const loadingText = overlay.querySelector('p');
            loadingText.textContent = message;
            overlay.style.display = 'block';
        }

        function hideLoadingOverlay() {
            document.getElementById('loading-overlay').style.display = 'none';
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // Check authentication first
                if (!window.authManager || !window.authManager.isAuthenticated()) {
                    if (window.router) {
                        window.router.navigate('/login');
                    } else {
                        window.location.href = '../public/login.html';
                    }
                    return;
                }

                // Initialize template loader and load common components
                if (window.templateLoader) {
                    await window.templateLoader.setupPage('authenticated', 'Profile Settings - Evidence Protection System');
                }

                // Initialize page-specific functionality
                initializePage();

                // Hide loading overlay
                const loadingOverlay = document.getElementById('loading-overlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
            } catch (error) {
                console.error('Error initializing profile page:', error);
                
                if (typeof showToast === 'function') {
                    showToast('Error loading page. Please refresh and try again.', 'error');
                }
            }
        });
    </script>
</body>
</html>
