<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Evidence - Evidence Protection System</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/auth-nav.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Meta tags for security -->
    <meta name="description" content="Verify Evidence - Blockchain verification of evidence integrity">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Security headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/favicon.ico">
</head>
<body class="auth-layout nav-state-authenticated">
    <!-- Navigation Container -->
    <nav class="navbar auth-navbar" id="navbar">
        <div class="nav-container" id="nav-container">
            <!-- Navigation will be loaded here by NavigationManager -->
        </div>
    </nav>

    <!-- Admin Sidebar (for admin users) -->
    <div id="admin-sidebar-wrapper" class="admin-sidebar-wrapper" style="display: none;">
        <div class="admin-sidebar" id="admin-sidebar">
            <!-- Admin sidebar will be loaded here for admin users -->
        </div>
    </div>

    <!-- Main Content Area -->
    <main class="main-content auth-main" id="main-content">
        <div class="verify-container">
            <!-- Page Header -->
            <div class="page-header">
                <h1><i class="fas fa-certificate"></i> Verify Evidence</h1>
                <p>Verify evidence integrity using blockchain technology</p>
            </div>

            <!-- Verification Form -->
            <div class="verify-form-section">
                <div class="verify-form-card">
                    <h2>Evidence Verification</h2>
                    <p>Enter an Evidence ID to verify its integrity and authenticity using blockchain records.</p>
                    
                    <form class="verify-form" id="verify-form">
                        <div class="form-group">
                            <label for="verify-evidence-id">Evidence ID</label>
                            <div class="input-with-button">
                                <input type="text" id="verify-evidence-id" placeholder="Enter evidence ID (e.g., EV001)" required>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-magnifying-glass"></i>
                                    Verify
                                </button>
                            </div>
                            <small>Evidence IDs are provided when evidence is uploaded to the system</small>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Verification Result -->
            <div class="verification-result" id="verification-result" style="display: none;">
                <div class="result-card">
                    <div class="result-header" id="result-header">
                        <!-- Result status will be populated here -->
                    </div>
                    <div class="result-content" id="result-content">
                        <!-- Result details will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Unverified Evidence Section -->
            <div class="unverified-evidence-section">
                <div class="section-header">
                    <h2><i class="fas fa-triangle-exclamation"></i> Unverified Evidence</h2>
                    <p>Click on any evidence item to verify it</p>
                    <button class="btn btn-secondary" id="refresh-unverified">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                </div>

                <div class="loading-spinner" id="unverified-loading" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i> Loading unverified evidence...
                </div>

                <div class="unverified-evidence-list" id="unverified-evidence-list">
                    <!-- Unverified evidence items will be loaded here -->
                </div>
            </div>

            <!-- Verification History -->
            <div class="verification-history-section">
                <div class="section-header">
                    <h2><i class="fas fa-history"></i> Recent Verifications</h2>
                    <p>Your recent verification activities</p>
                </div>

                <div class="verification-history" id="verification-history">
                    <!-- Verification history will be loaded here -->
                </div>
            </div>
        </div>
    </main>

    <!-- Toast Notification Container -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Verifying evidence...</p>
        </div>
    </div>

    <!-- Core JavaScript -->
    <script src="../js/core/template-loader.js"></script>
    <script src="../js/core/nav-manager.js"></script>
    <script src="../js/core/router.js"></script>
    <script src="../js/core/auth-manager.js"></script>
    
    <!-- Utility Scripts -->
    <script src="../js/utils/utils.js"></script>
    <script src="../js/utils/api.js"></script>
    
    <!-- Application Scripts -->
    <script src="../js/app/app-core.js"></script>

    <!-- Page-specific JavaScript -->
    <script>
        let unverifiedEvidence = [];
        let verificationHistory = [];

        function initializePage() {
            console.log('Initializing verify page');
            
            setupEventHandlers();
            loadUnverifiedEvidence();
            loadVerificationHistory();
        }

        function setupEventHandlers() {
            // Verification form
            const verifyForm = document.getElementById('verify-form');
            verifyForm.addEventListener('submit', handleVerification);

            // Refresh unverified evidence
            document.getElementById('refresh-unverified').addEventListener('click', loadUnverifiedEvidence);

            // Handle URL parameters for direct verification
            const urlParams = new URLSearchParams(window.location.search);
            const evidenceId = urlParams.get('id');
            if (evidenceId) {
                document.getElementById('verify-evidence-id').value = evidenceId;
                setTimeout(() => verifyEvidence(evidenceId), 500);
            }
        }

        async function handleVerification(event) {
            event.preventDefault();
            
            const evidenceId = document.getElementById('verify-evidence-id').value.trim();
            if (!evidenceId) {
                if (typeof showToast === 'function') {
                    showToast('Please enter an Evidence ID', 'error');
                }
                return;
            }

            await verifyEvidence(evidenceId);
        }

        async function verifyEvidence(evidenceId) {
            try {
                showVerificationLoading();
                
                // Simulate blockchain verification process
                await simulateVerification(evidenceId);
                
                // Mock verification result
                const verificationResult = await getVerificationResult(evidenceId);
                
                displayVerificationResult(verificationResult);
                
                // Add to verification history
                addToVerificationHistory(evidenceId, verificationResult);
                
                hideVerificationLoading();
                
            } catch (error) {
                console.error('Verification error:', error);
                
                const errorResult = {
                    success: false,
                    evidenceId: evidenceId,
                    error: error.message || 'Verification failed'
                };
                
                displayVerificationResult(errorResult);
                hideVerificationLoading();
            }
        }

        async function simulateVerification(evidenceId) {
            // Simulate the verification process steps
            const steps = [
                'Connecting to blockchain network...',
                'Retrieving evidence record...',
                'Validating blockchain hash...',
                'Checking integrity signatures...',
                'Verifying timestamp...',
                'Completing verification...'
            ];

            const loadingOverlay = document.getElementById('loading-overlay');
            const loadingText = loadingOverlay.querySelector('p');

            for (let i = 0; i < steps.length; i++) {
                loadingText.textContent = steps[i];
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        async function getVerificationResult(evidenceId) {
            // Mock verification results based on evidence ID
            const mockResults = {
                'EV001': {
                    success: true,
                    evidenceId: 'EV001',
                    fileName: 'crime_scene_photo_001.jpg',
                    caseId: 'CASE-2024-001',
                    blockchainHash: '0x1a2b3c4d5e6f7890abcdef1234567890abcdef1234567890abcdef1234567890',
                    timestamp: new Date('2024-01-15T10:30:00Z'),
                    uploadedBy: 'Officer Johnson',
                    fileHash: 'sha256:a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890',
                    verified: true,
                    blockNumber: 12345,
                    transactionHash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890'
                },
                'EV002': {
                    success: true,
                    evidenceId: 'EV002',
                    fileName: 'security_footage.mp4',
                    caseId: 'CASE-2024-002',
                    blockchainHash: '0x2b3c4d5e6f7890abcdef1234567890abcdef1234567890abcdef1234567890ab',
                    timestamp: new Date('2024-01-14T14:20:00Z'),
                    uploadedBy: 'Detective Smith',
                    fileHash: 'sha256:b2c3d4e5f6789012345678901234567890123456789012345678901234567890a1',
                    verified: false,
                    blockNumber: 12344,
                    transactionHash: '0xbcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abc'
                }
            };

            // Simulate network delay
            await new Promise(resolve => setTimeout(resolve, 1000));

            if (mockResults[evidenceId]) {
                return mockResults[evidenceId];
            } else {
                throw new Error('Evidence ID not found in blockchain records');
            }
        }

        function displayVerificationResult(result) {
            const resultContainer = document.getElementById('verification-result');
            const resultHeader = document.getElementById('result-header');
            const resultContent = document.getElementById('result-content');

            if (result.success) {
                resultHeader.innerHTML = `
                    <div class="result-status success">
                        <i class="fas fa-check-circle"></i>
                        <h3>Verification Successful</h3>
                        <p>Evidence integrity confirmed on blockchain</p>
                    </div>
                `;

                resultContent.innerHTML = `
                    <div class="verification-details">
                        <div class="detail-section">
                            <h4>Evidence Information</h4>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <label>Evidence ID:</label>
                                    <span class="evidence-id">${result.evidenceId}</span>
                                </div>
                                <div class="detail-item">
                                    <label>File Name:</label>
                                    <span>${result.fileName}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Case ID:</label>
                                    <span>${result.caseId}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Uploaded By:</label>
                                    <span>${result.uploadedBy}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Upload Time:</label>
                                    <span>${result.timestamp.toLocaleString()}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Status:</label>
                                    <span class="status-badge ${result.verified ? 'verified' : 'pending'}">
                                        ${result.verified ? 'Verified' : 'Pending Verification'}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h4>Blockchain Verification</h4>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <label>Blockchain Hash:</label>
                                    <span class="hash-value">${result.blockchainHash}</span>
                                </div>
                                <div class="detail-item">
                                    <label>File Hash:</label>
                                    <span class="hash-value">${result.fileHash}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Block Number:</label>
                                    <span>${result.blockNumber}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Transaction Hash:</label>
                                    <span class="hash-value">${result.transactionHash}</span>
                                </div>
                            </div>
                        </div>

                        <div class="verification-actions">
                            <button class="btn btn-secondary" onclick="downloadVerificationReport('${result.evidenceId}')">
                                <i class="fas fa-download"></i>
                                Download Report
                            </button>
                            <button class="btn btn-outline" onclick="copyVerificationLink('${result.evidenceId}')">
                                <i class="fas fa-link"></i>
                                Copy Verification Link
                            </button>
                        </div>
                    </div>
                `;
            } else {
                resultHeader.innerHTML = `
                    <div class="result-status error">
                        <i class="fas fa-times-circle"></i>
                        <h3>Verification Failed</h3>
                        <p>${result.error}</p>
                    </div>
                `;

                resultContent.innerHTML = `
                    <div class="verification-error">
                        <div class="error-details">
                            <h4>Possible Reasons:</h4>
                            <ul>
                                <li>Evidence ID does not exist in the system</li>
                                <li>Evidence has not been uploaded to blockchain yet</li>
                                <li>Network connectivity issues</li>
                                <li>Invalid Evidence ID format</li>
                            </ul>
                        </div>
                        <div class="error-actions">
                            <button class="btn btn-secondary" onclick="clearVerificationResult()">
                                <i class="fas fa-redo"></i>
                                Try Again
                            </button>
                            <button class="btn btn-outline" onclick="contactSupport()">
                                <i class="fas fa-headset"></i>
                                Contact Support
                            </button>
                        </div>
                    </div>
                `;
            }

            resultContainer.style.display = 'block';
            resultContainer.scrollIntoView({ behavior: 'smooth' });
        }

        async function loadUnverifiedEvidence() {
            const loadingSpinner = document.getElementById('unverified-loading');
            const evidenceList = document.getElementById('unverified-evidence-list');

            try {
                loadingSpinner.style.display = 'block';
                evidenceList.innerHTML = '';

                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Mock unverified evidence
                unverifiedEvidence = [
                    {
                        id: 'EV002',
                        fileName: 'security_footage.mp4',
                        caseId: 'CASE-2024-002',
                        uploadDate: new Date('2024-01-14'),
                        uploadedBy: 'Detective Smith',
                        type: 'video'
                    },
                    {
                        id: 'EV004',
                        fileName: 'fingerprint_scan.jpg',
                        caseId: 'CASE-2024-003',
                        uploadDate: new Date('2024-01-13'),
                        uploadedBy: 'Forensic Tech',
                        type: 'photo'
                    }
                ];

                renderUnverifiedEvidence();
                loadingSpinner.style.display = 'none';

            } catch (error) {
                console.error('Error loading unverified evidence:', error);
                loadingSpinner.style.display = 'none';
                evidenceList.innerHTML = '<p class="error-message">Error loading unverified evidence</p>';
            }
        }

        function renderUnverifiedEvidence() {
            const evidenceList = document.getElementById('unverified-evidence-list');

            if (unverifiedEvidence.length === 0) {
                evidenceList.innerHTML = `
                    <div class="no-unverified">
                        <i class="fas fa-check-circle"></i>
                        <h3>All Evidence Verified</h3>
                        <p>There is no unverified evidence at this time.</p>
                    </div>
                `;
                return;
            }

            const evidenceHTML = unverifiedEvidence.map(evidence => `
                <div class="unverified-item" onclick="verifyEvidence('${evidence.id}')">
                    <div class="evidence-icon">
                        <i class="${getTypeIcon(evidence.type)}"></i>
                    </div>
                    <div class="evidence-info">
                        <h4>${evidence.fileName}</h4>
                        <p>Case: ${evidence.caseId}</p>
                        <div class="evidence-meta">
                            <span>ID: ${evidence.id}</span>
                            <span>Uploaded: ${evidence.uploadDate.toLocaleDateString()}</span>
                            <span>By: ${evidence.uploadedBy}</span>
                        </div>
                    </div>
                    <div class="verify-action">
                        <i class="fas fa-certificate"></i>
                        <span>Click to Verify</span>
                    </div>
                </div>
            `).join('');

            evidenceList.innerHTML = evidenceHTML;
        }

        function loadVerificationHistory() {
            // Mock verification history
            verificationHistory = [
                {
                    evidenceId: 'EV001',
                    fileName: 'crime_scene_photo_001.jpg',
                    verificationTime: new Date('2024-01-15T11:00:00Z'),
                    result: 'success'
                }
            ];

            renderVerificationHistory();
        }

        function renderVerificationHistory() {
            const historyContainer = document.getElementById('verification-history');

            if (verificationHistory.length === 0) {
                historyContainer.innerHTML = `
                    <div class="no-history">
                        <i class="fas fa-history"></i>
                        <p>No verification history yet</p>
                    </div>
                `;
                return;
            }

            const historyHTML = verificationHistory.map(item => `
                <div class="history-item">
                    <div class="history-icon">
                        <i class="fas fa-${item.result === 'success' ? 'check-circle' : 'times-circle'}"></i>
                    </div>
                    <div class="history-info">
                        <h4>${item.fileName}</h4>
                        <p>Evidence ID: ${item.evidenceId}</p>
                        <span class="history-time">${item.verificationTime.toLocaleString()}</span>
                    </div>
                    <div class="history-status">
                        <span class="status-badge ${item.result}">${item.result === 'success' ? 'Verified' : 'Failed'}</span>
                    </div>
                </div>
            `).join('');

            historyContainer.innerHTML = historyHTML;
        }

        function addToVerificationHistory(evidenceId, result) {
            const historyItem = {
                evidenceId: evidenceId,
                fileName: result.fileName || 'Unknown',
                verificationTime: new Date(),
                result: result.success ? 'success' : 'failed'
            };

            verificationHistory.unshift(historyItem);
            renderVerificationHistory();
        }

        function getTypeIcon(type) {
            const icons = {
                photo: 'fas fa-image',
                video: 'fas fa-video',
                audio: 'fas fa-volume-up',
                document: 'fas fa-file-alt',
                digital: 'fas fa-hdd'
            };
            return icons[type] || 'fas fa-file';
        }

        function showVerificationLoading() {
            document.getElementById('loading-overlay').style.display = 'block';
        }

        function hideVerificationLoading() {
            document.getElementById('loading-overlay').style.display = 'none';
        }

        function clearVerificationResult() {
            document.getElementById('verification-result').style.display = 'none';
            document.getElementById('verify-evidence-id').value = '';
            document.getElementById('verify-evidence-id').focus();
        }

        function downloadVerificationReport(evidenceId) {
            if (typeof showToast === 'function') {
                showToast('Verification report download feature coming soon!', 'info');
            }
        }

        function copyVerificationLink(evidenceId) {
            const link = `${window.location.origin}${window.location.pathname}?id=${evidenceId}`;
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(link).then(() => {
                    if (typeof showToast === 'function') {
                        showToast('Verification link copied to clipboard!', 'success');
                    }
                });
            } else {
                if (typeof showToast === 'function') {
                    showToast('Copy feature not supported in this browser', 'warning');
                }
            }
        }

        function contactSupport() {
            if (typeof showToast === 'function') {
                showToast('Support contact feature coming soon!', 'info');
            }
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // Check authentication first
                if (!window.authManager || !window.authManager.isAuthenticated()) {
                    if (window.router) {
                        window.router.navigate('/login');
                    } else {
                        window.location.href = '../public/login.html';
                    }
                    return;
                }

                // Initialize template loader and load common components
                if (window.templateLoader) {
                    await window.templateLoader.setupPage('authenticated', 'Verify Evidence - Evidence Protection System');
                }

                // Initialize page-specific functionality
                initializePage();

                // Hide loading overlay
                const loadingOverlay = document.getElementById('loading-overlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
            } catch (error) {
                console.error('Error initializing verify page:', error);
                
                if (typeof showToast === 'function') {
                    showToast('Error loading page. Please refresh and try again.', 'error');
                }
            }
        });
    </script>
</body>
</html>
