// Application Core for Evidence Protection System
// Core functionality for authenticated application pages

class AppCore {
    constructor() {
        this.isInitialized = false;
        this.currentUser = null;
        this.systemStatus = {
            api: 'unknown',
            database: 'unknown',
            blockchain: 'unknown'
        };
        
        this.init();
    }

    async init() {
        try {
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }

            // Initialize core systems
            await this.initializeCoreSystems();
            
            // Setup global event handlers
            this.setupGlobalEventHandlers();
            
            // Setup periodic tasks
            this.setupPeriodicTasks();
            
            this.isInitialized = true;
            console.log('AppCore initialized successfully');
            
        } catch (error) {
            console.error('Error initializing AppCore:', error);
        }
    }

    async initializeCoreSystems() {
        // Check authentication
        if (window.authManager && window.authManager.isAuthenticated()) {
            this.currentUser = window.authManager.getCurrentUser();
        }

        // Initialize API client
        if (window.api && this.currentUser) {
            const token = window.authManager.getAuthToken();
            window.api.setToken(token);
        }

        // Check system status
        await this.checkSystemStatus();
    }

    setupGlobalEventHandlers() {
        // Handle authentication state changes
        if (window.authManager) {
            window.authManager.onLogin((user) => {
                this.currentUser = user;
                this.onUserLogin(user);
            });

            window.authManager.onLogout(() => {
                this.currentUser = null;
                this.onUserLogout();
            });
        }

        // Handle window focus/blur for session management
        window.addEventListener('focus', () => {
            this.onWindowFocus();
        });

        window.addEventListener('blur', () => {
            this.onWindowBlur();
        });

        // Handle online/offline status
        window.addEventListener('online', () => {
            this.onOnline();
        });

        window.addEventListener('offline', () => {
            this.onOffline();
        });

        // Handle unload for cleanup
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });

        // Global keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            this.handleGlobalKeyboard(event);
        });

        // Global error handling
        window.addEventListener('error', (event) => {
            this.handleGlobalError(event);
        });

        window.addEventListener('unhandledrejection', (event) => {
            this.handleUnhandledRejection(event);
        });
    }

    setupPeriodicTasks() {
        // Check system status every 5 minutes
        setInterval(() => {
            this.checkSystemStatus();
        }, 5 * 60 * 1000);

        // Refresh authentication token every 30 minutes
        setInterval(() => {
            if (window.authManager && window.authManager.isAuthenticated()) {
                window.authManager.refreshToken();
            }
        }, 30 * 60 * 1000);

        // Update last activity timestamp every minute
        setInterval(() => {
            this.updateLastActivity();
        }, 60 * 1000);
    }

    async checkSystemStatus() {
        try {
            if (!window.api) return;

            // Check API health
            try {
                await window.api.healthCheck();
                this.systemStatus.api = 'healthy';
            } catch (error) {
                this.systemStatus.api = 'error';
            }

            // Check blockchain status (if admin)
            if (this.currentUser && this.currentUser.role === 'admin') {
                try {
                    const blockchainStatus = await window.api.getBlockchainStatus();
                    this.systemStatus.blockchain = blockchainStatus.status || 'unknown';
                } catch (error) {
                    this.systemStatus.blockchain = 'error';
                }
            }

            // Update UI indicators
            this.updateSystemStatusUI();

        } catch (error) {
            console.error('Error checking system status:', error);
        }
    }

    updateSystemStatusUI() {
        // Update system status indicators in the UI
        const statusIndicators = document.querySelectorAll('.system-status-indicator');
        
        statusIndicators.forEach(indicator => {
            const statusType = indicator.dataset.status;
            const status = this.systemStatus[statusType];
            
            indicator.className = `system-status-indicator status-${status}`;
            
            const statusText = indicator.querySelector('.status-text');
            if (statusText) {
                statusText.textContent = this.getStatusText(status);
            }
        });
    }

    getStatusText(status) {
        const statusTexts = {
            healthy: 'Operational',
            warning: 'Warning',
            error: 'Error',
            unknown: 'Unknown'
        };
        return statusTexts[status] || 'Unknown';
    }

    onUserLogin(user) {
        console.log('AppCore: User logged in:', user.username);
        
        // Update UI for authenticated user
        this.updateUserUI(user);
        
        // Load user preferences
        this.loadUserPreferences(user);
        
        // Track login event
        this.trackEvent('user_login', { userId: user.id, role: user.role });
    }

    onUserLogout() {
        console.log('AppCore: User logged out');
        
        // Clear user-specific data
        this.clearUserData();
        
        // Track logout event
        this.trackEvent('user_logout');
    }

    updateUserUI(user) {
        // Update user name displays
        const userNameElements = document.querySelectorAll('.user-name');
        userNameElements.forEach(element => {
            element.textContent = user.fullName || user.username;
        });

        // Update user role displays
        const userRoleElements = document.querySelectorAll('.user-role');
        userRoleElements.forEach(element => {
            element.textContent = (user.role || 'user').toUpperCase();
            element.className = `user-role role-${user.role || 'user'}`;
        });

        // Update role-based visibility
        this.updateRoleBasedVisibility(user.role);
    }

    updateRoleBasedVisibility(role) {
        // Show/hide elements based on user role
        const roleElements = document.querySelectorAll('[data-role]');
        
        roleElements.forEach(element => {
            const requiredRoles = element.dataset.role.split(',').map(r => r.trim());
            const hasAccess = requiredRoles.includes(role) || requiredRoles.includes('all');
            
            element.style.display = hasAccess ? '' : 'none';
        });

        // Special handling for upload functionality
        const uploadElements = document.querySelectorAll('.upload-only');
        const canUpload = role === 'police' || role === 'admin';
        
        uploadElements.forEach(element => {
            element.style.display = canUpload ? '' : 'none';
        });

        // Special handling for admin functionality
        const adminElements = document.querySelectorAll('.admin-only');
        const isAdmin = role === 'admin';
        
        adminElements.forEach(element => {
            element.style.display = isAdmin ? '' : 'none';
        });
    }

    loadUserPreferences(user) {
        // Load user preferences from storage or API
        const preferences = this.getStoredPreferences(user.id);
        
        if (preferences) {
            this.applyUserPreferences(preferences);
        }
    }

    getStoredPreferences(userId) {
        try {
            const key = `user_preferences_${userId}`;
            const stored = localStorage.getItem(key);
            return stored ? JSON.parse(stored) : null;
        } catch (error) {
            console.error('Error loading user preferences:', error);
            return null;
        }
    }

    applyUserPreferences(preferences) {
        // Apply theme
        if (preferences.theme) {
            this.setTheme(preferences.theme);
        }

        // Apply language
        if (preferences.language) {
            this.setLanguage(preferences.language);
        }

        // Apply other preferences
        // ... additional preference applications
    }

    setTheme(theme) {
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add(`theme-${theme}`);
    }

    setLanguage(language) {
        document.documentElement.lang = language;
        // Additional language switching logic would go here
    }

    clearUserData() {
        // Clear user-specific cached data
        this.currentUser = null;
        
        // Clear API token
        if (window.api) {
            window.api.clearToken();
        }
    }

    onWindowFocus() {
        // Check if user is still authenticated when window gains focus
        if (window.authManager && !window.authManager.isAuthenticated()) {
            // User was logged out in another tab
            window.location.reload();
        }
    }

    onWindowBlur() {
        // Save any pending changes when window loses focus
        this.savePendingChanges();
    }

    onOnline() {
        console.log('AppCore: Connection restored');
        
        if (typeof showToast === 'function') {
            showToast('Connection restored', 'success');
        }
        
        // Sync any pending data
        this.syncPendingData();
    }

    onOffline() {
        console.log('AppCore: Connection lost');
        
        if (typeof showToast === 'function') {
            showToast('Connection lost - working offline', 'warning');
        }
    }

    handleGlobalKeyboard(event) {
        // Global keyboard shortcuts
        if (event.ctrlKey || event.metaKey) {
            switch (event.key) {
                case 'k':
                    // Ctrl+K for search
                    event.preventDefault();
                    this.openGlobalSearch();
                    break;
                case '/':
                    // Ctrl+/ for help
                    event.preventDefault();
                    this.showKeyboardShortcuts();
                    break;
            }
        }

        // Escape key handling
        if (event.key === 'Escape') {
            this.handleEscapeKey();
        }
    }

    handleGlobalError(event) {
        console.error('Global error:', event.error);
        
        // Track error
        this.trackEvent('global_error', {
            message: event.error?.message,
            filename: event.filename,
            lineno: event.lineno
        });
        
        // Show user-friendly error message
        if (typeof showToast === 'function') {
            showToast('An unexpected error occurred', 'error');
        }
    }

    handleUnhandledRejection(event) {
        console.error('Unhandled promise rejection:', event.reason);
        
        // Track error
        this.trackEvent('unhandled_rejection', {
            reason: event.reason?.message || String(event.reason)
        });
        
        // Handle authentication errors
        if (event.reason?.status === 401) {
            if (window.authManager) {
                window.authManager.logout();
            }
        }
    }

    openGlobalSearch() {
        // Open global search functionality
        if (typeof showToast === 'function') {
            showToast('Global search feature coming soon!', 'info');
        }
    }

    showKeyboardShortcuts() {
        // Show keyboard shortcuts help
        if (typeof showModal === 'function') {
            const shortcuts = `
                <div class="keyboard-shortcuts">
                    <h4>Keyboard Shortcuts</h4>
                    <div class="shortcut-list">
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>K</kbd>
                            <span>Global Search</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>/</kbd>
                            <span>Show Shortcuts</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Esc</kbd>
                            <span>Close Modals</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>L</kbd>
                            <span>Logout</span>
                        </div>
                    </div>
                </div>
            `;
            
            showModal('Keyboard Shortcuts', shortcuts);
        }
    }

    handleEscapeKey() {
        // Close any open modals or overlays
        const modal = document.getElementById('modal');
        const confirmDialog = document.getElementById('confirm-dialog');
        
        if (modal && modal.style.display !== 'none') {
            if (typeof hideModal === 'function') {
                hideModal();
            }
        } else if (confirmDialog && confirmDialog.style.display !== 'none') {
            confirmDialog.style.display = 'none';
        }
    }

    updateLastActivity() {
        if (this.currentUser) {
            const timestamp = new Date().toISOString();
            sessionStorage.setItem('lastActivity', timestamp);
        }
    }

    savePendingChanges() {
        // Save any pending form changes or data
        // This would be implemented based on specific application needs
    }

    syncPendingData() {
        // Sync any data that was queued while offline
        // This would be implemented based on specific application needs
    }

    trackEvent(eventName, data = {}) {
        // Track events for analytics (implement based on analytics service)
        console.log('Event tracked:', eventName, data);
    }

    cleanup() {
        // Cleanup resources before page unload
        console.log('AppCore: Cleaning up resources');
    }

    // Utility methods
    getCurrentUser() {
        return this.currentUser;
    }

    getSystemStatus() {
        return this.systemStatus;
    }

    isInitialized() {
        return this.isInitialized;
    }
}

// Initialize AppCore when DOM is ready
if (typeof window !== 'undefined') {
    window.addEventListener('DOMContentLoaded', () => {
        if (!window.appCore) {
            window.appCore = new AppCore();
        }
    });
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppCore;
}
