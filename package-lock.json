{"name": "evidence-protection-system", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "evidence-protection-system", "version": "1.0.0", "license": "MIT", "dependencies": {"@truffle/contract": "^4.6.31", "archiver": "^7.0.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "web3": "^4.1.1"}, "devDependencies": {"@openzeppelin/contracts": "^4.9.3", "ganache-cli": "^6.12.2", "jest": "^29.6.4", "nodemon": "^3.0.1", "truffle": "^5.11.5"}}, "node_modules/@adraffy/ens-normalize": {"version": "1.11.0", "license": "MIT"}, "node_modules/@ampproject/remapping": {"version": "2.3.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@apollo/protobufjs": {"version": "1.2.7", "dev": true, "hasInstallScript": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/long": "^4.0.0", "long": "^4.0.0"}, "bin": {"apollo-pbjs": "bin/pbjs", "apollo-pbts": "bin/pbts"}}, "node_modules/@apollo/usage-reporting-protobuf": {"version": "4.1.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@apollo/protobufjs": "1.2.7"}}, "node_modules/@apollo/utils.dropunuseddefinitions": {"version": "1.1.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=12.13.0"}, "peerDependencies": {"graphql": "14.x || 15.x || 16.x"}}, "node_modules/@apollo/utils.keyvaluecache": {"version": "1.0.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@apollo/utils.logger": "^1.0.0", "lru-cache": "7.10.1 - 7.13.1"}}, "node_modules/@apollo/utils.keyvaluecache/node_modules/lru-cache": {"version": "7.13.1", "dev": true, "license": "ISC", "optional": true, "engines": {"node": ">=12"}}, "node_modules/@apollo/utils.logger": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/@apollo/utils.printwithreducedwhitespace": {"version": "1.1.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=12.13.0"}, "peerDependencies": {"graphql": "14.x || 15.x || 16.x"}}, "node_modules/@apollo/utils.removealiases": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=12.13.0"}, "peerDependencies": {"graphql": "14.x || 15.x || 16.x"}}, "node_modules/@apollo/utils.sortast": {"version": "1.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"lodash.sortby": "^4.7.0"}, "engines": {"node": ">=12.13.0"}, "peerDependencies": {"graphql": "14.x || 15.x || 16.x"}}, "node_modules/@apollo/utils.stripsensitiveliterals": {"version": "1.2.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=12.13.0"}, "peerDependencies": {"graphql": "14.x || 15.x || 16.x"}}, "node_modules/@apollo/utils.usagereporting": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@apollo/usage-reporting-protobuf": "^4.0.0", "@apollo/utils.dropunuseddefinitions": "^1.1.0", "@apollo/utils.printwithreducedwhitespace": "^1.1.0", "@apollo/utils.removealiases": "1.0.0", "@apollo/utils.sortast": "^1.1.0", "@apollo/utils.stripsensitiveliterals": "^1.2.0"}, "engines": {"node": ">=12.13.0"}, "peerDependencies": {"graphql": "14.x || 15.x || 16.x"}}, "node_modules/@apollographql/apollo-tools": {"version": "0.5.4", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=8", "npm": ">=6"}, "peerDependencies": {"graphql": "^14.2.1 || ^15.0.0 || ^16.0.0"}}, "node_modules/@apollographql/graphql-playground-html": {"version": "1.6.29", "dev": true, "license": "MIT", "optional": true, "dependencies": {"xss": "^1.0.8"}}, "node_modules/@babel/code-frame": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.27.5", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.27.4", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.27.3", "@babel/helpers": "^7.27.4", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/traverse": "^7.27.4", "@babel/types": "^7.27.3", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/core/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/generator": {"version": "7.27.5", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.27.2", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-module-imports": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.27.3", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.27.1", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.27.1", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.27.6", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.27.6"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.27.5", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.27.3"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-syntax-async-generators": {"version": "7.8.4", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-bigint": {"version": "7.8.3", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-class-properties": {"version": "7.12.13", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-class-static-block": {"version": "7.14.5", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-attributes": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-meta": {"version": "7.10.4", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-json-strings": {"version": "7.8.3", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-jsx": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-private-property-in-object": {"version": "7.14.5", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-top-level-await": {"version": "7.14.5", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-typescript": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/runtime": {"version": "7.27.6", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.27.2", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.27.4", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.27.6", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@bcoe/v8-coverage": {"version": "0.2.3", "dev": true, "license": "MIT"}, "node_modules/@ensdomains/address-encoder": {"version": "0.1.9", "license": "BSD", "dependencies": {"bech32": "^1.1.3", "blakejs": "^1.1.0", "bn.js": "^4.11.8", "bs58": "^4.0.1", "crypto-addr-codec": "^0.1.7", "nano-base32": "^1.0.1", "ripemd160": "^2.0.2"}}, "node_modules/@ensdomains/ens": {"version": "0.4.5", "license": "CC0-1.0", "dependencies": {"bluebird": "^3.5.2", "eth-ens-namehash": "^2.0.8", "solc": "^0.4.20", "testrpc": "0.0.1", "web3-utils": "^1.0.0-beta.31"}}, "node_modules/@ensdomains/ensjs": {"version": "2.1.0", "license": "ISC", "dependencies": {"@babel/runtime": "^7.4.4", "@ensdomains/address-encoder": "^0.1.7", "@ensdomains/ens": "0.4.5", "@ensdomains/resolver": "0.2.4", "content-hash": "^2.5.2", "eth-ens-namehash": "^2.0.8", "ethers": "^5.0.13", "js-sha3": "^0.8.0"}}, "node_modules/@ensdomains/ensjs/node_modules/ethers": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abi": "5.8.0", "@ethersproject/abstract-provider": "5.8.0", "@ethersproject/abstract-signer": "5.8.0", "@ethersproject/address": "5.8.0", "@ethersproject/base64": "5.8.0", "@ethersproject/basex": "5.8.0", "@ethersproject/bignumber": "5.8.0", "@ethersproject/bytes": "5.8.0", "@ethersproject/constants": "5.8.0", "@ethersproject/contracts": "5.8.0", "@ethersproject/hash": "5.8.0", "@ethersproject/hdnode": "5.8.0", "@ethersproject/json-wallets": "5.8.0", "@ethersproject/keccak256": "5.8.0", "@ethersproject/logger": "5.8.0", "@ethersproject/networks": "5.8.0", "@ethersproject/pbkdf2": "5.8.0", "@ethersproject/properties": "5.8.0", "@ethersproject/providers": "5.8.0", "@ethersproject/random": "5.8.0", "@ethersproject/rlp": "5.8.0", "@ethersproject/sha2": "5.8.0", "@ethersproject/signing-key": "5.8.0", "@ethersproject/solidity": "5.8.0", "@ethersproject/strings": "5.8.0", "@ethersproject/transactions": "5.8.0", "@ethersproject/units": "5.8.0", "@ethersproject/wallet": "5.8.0", "@ethersproject/web": "5.8.0", "@ethersproject/wordlists": "5.8.0"}}, "node_modules/@ensdomains/resolver": {"version": "0.2.4"}, "node_modules/@ethereumjs/common": {"version": "2.5.0", "license": "MIT", "dependencies": {"crc-32": "^1.2.0", "ethereumjs-util": "^7.1.1"}}, "node_modules/@ethereumjs/rlp": {"version": "4.0.1", "license": "MPL-2.0", "bin": {"rlp": "bin/rlp"}, "engines": {"node": ">=14"}}, "node_modules/@ethereumjs/tx": {"version": "3.3.2", "license": "MPL-2.0", "dependencies": {"@ethereumjs/common": "^2.5.0", "ethereumjs-util": "^7.1.2"}}, "node_modules/@ethersproject/abi": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/address": "^5.8.0", "@ethersproject/bignumber": "^5.8.0", "@ethersproject/bytes": "^5.8.0", "@ethersproject/constants": "^5.8.0", "@ethersproject/hash": "^5.8.0", "@ethersproject/keccak256": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/properties": "^5.8.0", "@ethersproject/strings": "^5.8.0"}}, "node_modules/@ethersproject/abstract-provider": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.8.0", "@ethersproject/bytes": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/networks": "^5.8.0", "@ethersproject/properties": "^5.8.0", "@ethersproject/transactions": "^5.8.0", "@ethersproject/web": "^5.8.0"}}, "node_modules/@ethersproject/abstract-signer": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-provider": "^5.8.0", "@ethersproject/bignumber": "^5.8.0", "@ethersproject/bytes": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/properties": "^5.8.0"}}, "node_modules/@ethersproject/address": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.8.0", "@ethersproject/bytes": "^5.8.0", "@ethersproject/keccak256": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/rlp": "^5.8.0"}}, "node_modules/@ethersproject/base64": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.8.0"}}, "node_modules/@ethersproject/basex": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.8.0", "@ethersproject/properties": "^5.8.0"}}, "node_modules/@ethersproject/bignumber": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.8.0", "@ethersproject/logger": "^5.8.0", "bn.js": "^5.2.1"}}, "node_modules/@ethersproject/bignumber/node_modules/bn.js": {"version": "5.2.2", "license": "MIT"}, "node_modules/@ethersproject/bytes": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.8.0"}}, "node_modules/@ethersproject/constants": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.8.0"}}, "node_modules/@ethersproject/contracts": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abi": "^5.8.0", "@ethersproject/abstract-provider": "^5.8.0", "@ethersproject/abstract-signer": "^5.8.0", "@ethersproject/address": "^5.8.0", "@ethersproject/bignumber": "^5.8.0", "@ethersproject/bytes": "^5.8.0", "@ethersproject/constants": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/properties": "^5.8.0", "@ethersproject/transactions": "^5.8.0"}}, "node_modules/@ethersproject/hash": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-signer": "^5.8.0", "@ethersproject/address": "^5.8.0", "@ethersproject/base64": "^5.8.0", "@ethersproject/bignumber": "^5.8.0", "@ethersproject/bytes": "^5.8.0", "@ethersproject/keccak256": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/properties": "^5.8.0", "@ethersproject/strings": "^5.8.0"}}, "node_modules/@ethersproject/hdnode": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-signer": "^5.8.0", "@ethersproject/basex": "^5.8.0", "@ethersproject/bignumber": "^5.8.0", "@ethersproject/bytes": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/pbkdf2": "^5.8.0", "@ethersproject/properties": "^5.8.0", "@ethersproject/sha2": "^5.8.0", "@ethersproject/signing-key": "^5.8.0", "@ethersproject/strings": "^5.8.0", "@ethersproject/transactions": "^5.8.0", "@ethersproject/wordlists": "^5.8.0"}}, "node_modules/@ethersproject/json-wallets": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-signer": "^5.8.0", "@ethersproject/address": "^5.8.0", "@ethersproject/bytes": "^5.8.0", "@ethersproject/hdnode": "^5.8.0", "@ethersproject/keccak256": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/pbkdf2": "^5.8.0", "@ethersproject/properties": "^5.8.0", "@ethersproject/random": "^5.8.0", "@ethersproject/strings": "^5.8.0", "@ethersproject/transactions": "^5.8.0", "aes-js": "3.0.0", "scrypt-js": "3.0.1"}}, "node_modules/@ethersproject/json-wallets/node_modules/scrypt-js": {"version": "3.0.1", "license": "MIT"}, "node_modules/@ethersproject/keccak256": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.8.0", "js-sha3": "0.8.0"}}, "node_modules/@ethersproject/logger": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@ethersproject/networks": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.8.0"}}, "node_modules/@ethersproject/pbkdf2": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.8.0", "@ethersproject/sha2": "^5.8.0"}}, "node_modules/@ethersproject/properties": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.8.0"}}, "node_modules/@ethersproject/providers": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-provider": "^5.8.0", "@ethersproject/abstract-signer": "^5.8.0", "@ethersproject/address": "^5.8.0", "@ethersproject/base64": "^5.8.0", "@ethersproject/basex": "^5.8.0", "@ethersproject/bignumber": "^5.8.0", "@ethersproject/bytes": "^5.8.0", "@ethersproject/constants": "^5.8.0", "@ethersproject/hash": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/networks": "^5.8.0", "@ethersproject/properties": "^5.8.0", "@ethersproject/random": "^5.8.0", "@ethersproject/rlp": "^5.8.0", "@ethersproject/sha2": "^5.8.0", "@ethersproject/strings": "^5.8.0", "@ethersproject/transactions": "^5.8.0", "@ethersproject/web": "^5.8.0", "bech32": "1.1.4", "ws": "8.18.0"}}, "node_modules/@ethersproject/providers/node_modules/ws": {"version": "8.18.0", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/@ethersproject/random": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.8.0", "@ethersproject/logger": "^5.8.0"}}, "node_modules/@ethersproject/rlp": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.8.0", "@ethersproject/logger": "^5.8.0"}}, "node_modules/@ethersproject/sha2": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.8.0", "@ethersproject/logger": "^5.8.0", "hash.js": "1.1.7"}}, "node_modules/@ethersproject/sha2/node_modules/hash.js": {"version": "1.1.7", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "node_modules/@ethersproject/signing-key": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/properties": "^5.8.0", "bn.js": "^5.2.1", "elliptic": "6.6.1", "hash.js": "1.1.7"}}, "node_modules/@ethersproject/signing-key/node_modules/bn.js": {"version": "5.2.2", "license": "MIT"}, "node_modules/@ethersproject/signing-key/node_modules/elliptic": {"version": "6.6.1", "license": "MIT", "dependencies": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.1", "inherits": "^2.0.4", "minimalistic-assert": "^1.0.1", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/@ethersproject/signing-key/node_modules/elliptic/node_modules/bn.js": {"version": "4.12.2", "license": "MIT"}, "node_modules/@ethersproject/signing-key/node_modules/hash.js": {"version": "1.1.7", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "node_modules/@ethersproject/solidity": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.8.0", "@ethersproject/bytes": "^5.8.0", "@ethersproject/keccak256": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/sha2": "^5.8.0", "@ethersproject/strings": "^5.8.0"}}, "node_modules/@ethersproject/strings": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.8.0", "@ethersproject/constants": "^5.8.0", "@ethersproject/logger": "^5.8.0"}}, "node_modules/@ethersproject/transactions": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/address": "^5.8.0", "@ethersproject/bignumber": "^5.8.0", "@ethersproject/bytes": "^5.8.0", "@ethersproject/constants": "^5.8.0", "@ethersproject/keccak256": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/properties": "^5.8.0", "@ethersproject/rlp": "^5.8.0", "@ethersproject/signing-key": "^5.8.0"}}, "node_modules/@ethersproject/units": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.8.0", "@ethersproject/constants": "^5.8.0", "@ethersproject/logger": "^5.8.0"}}, "node_modules/@ethersproject/wallet": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-provider": "^5.8.0", "@ethersproject/abstract-signer": "^5.8.0", "@ethersproject/address": "^5.8.0", "@ethersproject/bignumber": "^5.8.0", "@ethersproject/bytes": "^5.8.0", "@ethersproject/hash": "^5.8.0", "@ethersproject/hdnode": "^5.8.0", "@ethersproject/json-wallets": "^5.8.0", "@ethersproject/keccak256": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/properties": "^5.8.0", "@ethersproject/random": "^5.8.0", "@ethersproject/signing-key": "^5.8.0", "@ethersproject/transactions": "^5.8.0", "@ethersproject/wordlists": "^5.8.0"}}, "node_modules/@ethersproject/web": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/base64": "^5.8.0", "@ethersproject/bytes": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/properties": "^5.8.0", "@ethersproject/strings": "^5.8.0"}}, "node_modules/@ethersproject/wordlists": {"version": "5.8.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.8.0", "@ethersproject/hash": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/properties": "^5.8.0", "@ethersproject/strings": "^5.8.0"}}, "node_modules/@graphql-tools/batch-execute": {"version": "8.5.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@graphql-tools/utils": "8.9.0", "dataloader": "2.1.0", "tslib": "^2.4.0", "value-or-promise": "1.0.11"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/delegate": {"version": "8.8.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@graphql-tools/batch-execute": "8.5.1", "@graphql-tools/schema": "8.5.1", "@graphql-tools/utils": "8.9.0", "dataloader": "2.1.0", "tslib": "~2.4.0", "value-or-promise": "1.0.11"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/merge": {"version": "8.3.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@graphql-tools/utils": "8.9.0", "tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/mock": {"version": "8.7.20", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@graphql-tools/schema": "^9.0.18", "@graphql-tools/utils": "^9.2.1", "fast-json-stable-stringify": "^2.1.0", "tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/mock/node_modules/@graphql-tools/merge": {"version": "8.4.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@graphql-tools/utils": "^9.2.1", "tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/mock/node_modules/@graphql-tools/schema": {"version": "9.0.19", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@graphql-tools/merge": "^8.4.1", "@graphql-tools/utils": "^9.2.1", "tslib": "^2.4.0", "value-or-promise": "^1.0.12"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/mock/node_modules/@graphql-tools/utils": {"version": "9.2.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@graphql-typed-document-node/core": "^3.1.1", "tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/mock/node_modules/value-or-promise": {"version": "1.0.12", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=12"}}, "node_modules/@graphql-tools/schema": {"version": "8.5.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@graphql-tools/merge": "8.3.1", "@graphql-tools/utils": "8.9.0", "tslib": "^2.4.0", "value-or-promise": "1.0.11"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/utils": {"version": "8.9.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-typed-document-node/core": {"version": "3.2.0", "dev": true, "license": "MIT", "optional": true, "peerDependencies": {"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@isaacs/cliui": {"version": "8.0.2", "license": "ISC", "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@isaacs/cliui/node_modules/ansi-regex": {"version": "6.1.0", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/ansi-styles": {"version": "6.2.1", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/emoji-regex": {"version": "9.2.2", "license": "MIT"}, "node_modules/@isaacs/cliui/node_modules/string-width": {"version": "5.1.2", "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@isaacs/cliui/node_modules/strip-ansi": {"version": "7.1.0", "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/wrap-ansi": {"version": "8.1.0", "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/@istanbuljs/load-nyc-config": {"version": "1.1.0", "dev": true, "license": "ISC", "dependencies": {"camelcase": "^5.3.1", "find-up": "^4.1.0", "get-package-type": "^0.1.0", "js-yaml": "^3.13.1", "resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@istanbuljs/load-nyc-config/node_modules/camelcase": {"version": "5.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@istanbuljs/schema": {"version": "0.1.3", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@jest/console": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/console/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@jest/console/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/@jest/console/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/@jest/console/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/@jest/console/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@jest/console/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@jest/core": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/console": "^29.7.0", "@jest/reporters": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "ci-info": "^3.2.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "jest-changed-files": "^29.7.0", "jest-config": "^29.7.0", "jest-haste-map": "^29.7.0", "jest-message-util": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-resolve": "^29.7.0", "jest-resolve-dependencies": "^29.7.0", "jest-runner": "^29.7.0", "jest-runtime": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "jest-watcher": "^29.7.0", "micromatch": "^4.0.4", "pretty-format": "^29.7.0", "slash": "^3.0.0", "strip-ansi": "^6.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/@jest/core/node_modules/ansi-regex": {"version": "5.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@jest/core/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@jest/core/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/@jest/core/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/@jest/core/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/@jest/core/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@jest/core/node_modules/strip-ansi": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/@jest/core/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@jest/environment": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/fake-timers": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-mock": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/expect": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"expect": "^29.7.0", "jest-snapshot": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/expect-utils": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"jest-get-type": "^29.6.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/fake-timers": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@sinonjs/fake-timers": "^10.0.2", "@types/node": "*", "jest-message-util": "^29.7.0", "jest-mock": "^29.7.0", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/globals": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/environment": "^29.7.0", "@jest/expect": "^29.7.0", "@jest/types": "^29.6.3", "jest-mock": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/reporters": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@bcoe/v8-coverage": "^0.2.3", "@jest/console": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.18", "@types/node": "*", "chalk": "^4.0.0", "collect-v8-coverage": "^1.0.0", "exit": "^0.1.2", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-instrument": "^6.0.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.0", "istanbul-reports": "^3.1.3", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0", "jest-worker": "^29.7.0", "slash": "^3.0.0", "string-length": "^4.0.1", "strip-ansi": "^6.0.0", "v8-to-istanbul": "^9.0.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/@jest/reporters/node_modules/ansi-regex": {"version": "5.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@jest/reporters/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@jest/reporters/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/@jest/reporters/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/@jest/reporters/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/@jest/reporters/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@jest/reporters/node_modules/strip-ansi": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/@jest/reporters/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@jest/schemas": {"version": "29.6.3", "dev": true, "license": "MIT", "dependencies": {"@sinclair/typebox": "^0.27.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/source-map": {"version": "29.6.3", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "^0.3.18", "callsites": "^3.0.0", "graceful-fs": "^4.2.9"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/test-result": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/console": "^29.7.0", "@jest/types": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "collect-v8-coverage": "^1.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/test-sequencer": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/test-result": "^29.7.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/transform": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.11.6", "@jest/types": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.18", "babel-plugin-istanbul": "^6.1.1", "chalk": "^4.0.0", "convert-source-map": "^2.0.0", "fast-json-stable-stringify": "^2.1.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "micromatch": "^4.0.4", "pirates": "^4.0.4", "slash": "^3.0.0", "write-file-atomic": "^4.0.2"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/transform/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@jest/transform/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/@jest/transform/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/@jest/transform/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/@jest/transform/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@jest/transform/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@jest/types": {"version": "29.6.3", "dev": true, "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/types/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@jest/types/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/@jest/types/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/@jest/types/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/@jest/types/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@jest/types/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@josephg/resolvable": {"version": "1.0.1", "dev": true, "license": "ISC", "optional": true}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.8", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "dev": true, "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@mongodb-js/saslprep": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/@mongodb-js/saslprep/-/saslprep-1.3.0.tgz", "integrity": "sha512-zlayKCsIjYb7/IdfqxorK5+xUMyi4vOKcFy10wKJYc63NSdKI8mNME+uJqfatkPmOSMMUiojrL58IePKBm3gvQ==", "license": "MIT", "optional": true, "dependencies": {"sparse-bitfield": "^3.0.3"}}, "node_modules/@noble/curves": {"version": "1.4.2", "license": "MIT", "dependencies": {"@noble/hashes": "1.4.0"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@noble/hashes": {"version": "1.4.0", "license": "MIT", "engines": {"node": ">= 16"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@openzeppelin/contracts": {"version": "4.9.6", "dev": true, "license": "MIT"}, "node_modules/@pkgjs/parseargs": {"version": "0.11.0", "license": "MIT", "optional": true, "engines": {"node": ">=14"}}, "node_modules/@protobufjs/aspromise": {"version": "1.1.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/@protobufjs/base64": {"version": "1.1.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/@protobufjs/codegen": {"version": "2.0.4", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/@protobufjs/eventemitter": {"version": "1.1.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/@protobufjs/fetch": {"version": "1.1.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"@protobufjs/aspromise": "^1.1.1", "@protobufjs/inquire": "^1.1.0"}}, "node_modules/@protobufjs/float": {"version": "1.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/@protobufjs/inquire": {"version": "1.1.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/@protobufjs/path": {"version": "1.1.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/@protobufjs/pool": {"version": "1.1.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/@protobufjs/utf8": {"version": "1.1.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/@redux-saga/core": {"version": "1.3.0", "dev": true, "license": "MIT", "dependencies": {"@babel/runtime": "^7.6.3", "@redux-saga/deferred": "^1.2.1", "@redux-saga/delay-p": "^1.2.1", "@redux-saga/is": "^1.1.3", "@redux-saga/symbols": "^1.1.3", "@redux-saga/types": "^1.2.1", "typescript-tuple": "^2.2.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/redux-saga"}}, "node_modules/@redux-saga/deferred": {"version": "1.2.1", "dev": true, "license": "MIT"}, "node_modules/@redux-saga/delay-p": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"@redux-saga/symbols": "^1.1.3"}}, "node_modules/@redux-saga/is": {"version": "1.1.3", "dev": true, "license": "MIT", "dependencies": {"@redux-saga/symbols": "^1.1.3", "@redux-saga/types": "^1.2.1"}}, "node_modules/@redux-saga/symbols": {"version": "1.1.3", "dev": true, "license": "MIT"}, "node_modules/@redux-saga/types": {"version": "1.2.1", "dev": true, "license": "MIT"}, "node_modules/@scure/base": {"version": "1.1.9", "license": "MIT", "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@scure/bip32": {"version": "1.4.0", "license": "MIT", "dependencies": {"@noble/curves": "~1.4.0", "@noble/hashes": "~1.4.0", "@scure/base": "~1.1.6"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@scure/bip39": {"version": "1.3.0", "license": "MIT", "dependencies": {"@noble/hashes": "~1.4.0", "@scure/base": "~1.1.6"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@sinclair/typebox": {"version": "0.27.8", "dev": true, "license": "MIT"}, "node_modules/@sindresorhus/is": {"version": "4.6.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/is?sponsor=1"}}, "node_modules/@sinonjs/commons": {"version": "3.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"type-detect": "4.0.8"}}, "node_modules/@sinonjs/fake-timers": {"version": "10.3.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sinonjs/commons": "^3.0.0"}}, "node_modules/@szmarczak/http-timer": {"version": "5.0.1", "license": "MIT", "dependencies": {"defer-to-connect": "^2.0.1"}, "engines": {"node": ">=14.16"}}, "node_modules/@truffle/abi-utils": {"version": "1.0.3", "license": "MIT", "dependencies": {"change-case": "3.0.2", "fast-check": "3.1.1", "web3-utils": "1.10.0"}, "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@truffle/blockchain-utils": {"version": "0.1.9", "license": "MIT", "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@truffle/code-utils": {"version": "3.0.4", "dev": true, "license": "MIT", "dependencies": {"cbor": "^5.2.0"}, "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@truffle/codec": {"version": "0.17.3", "license": "MIT", "dependencies": {"@truffle/abi-utils": "^1.0.3", "@truffle/compile-common": "^0.9.8", "big.js": "^6.0.3", "bn.js": "^5.1.3", "cbor": "^5.2.0", "debug": "^4.3.1", "lodash": "^4.17.21", "semver": "^7.5.4", "utf8": "^3.0.0", "web3-utils": "1.10.0"}, "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@truffle/codec/node_modules/bn.js": {"version": "5.2.2", "license": "MIT"}, "node_modules/@truffle/compile-common": {"version": "0.9.8", "license": "MIT", "dependencies": {"@truffle/error": "^0.2.2", "colors": "1.4.0"}, "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@truffle/config": {"version": "1.3.61", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@truffle/error": "^0.2.2", "@truffle/events": "^0.1.25", "@truffle/provider": "^0.3.13", "conf": "^10.1.2", "debug": "^4.3.1", "find-up": "^2.1.0", "lodash": "^4.17.21", "original-require": "^1.0.1"}, "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@truffle/config/node_modules/find-up": {"version": "2.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"locate-path": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@truffle/config/node_modules/locate-path": {"version": "2.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@truffle/config/node_modules/p-limit": {"version": "1.3.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"p-try": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@truffle/config/node_modules/p-locate": {"version": "2.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"p-limit": "^1.1.0"}, "engines": {"node": ">=4"}}, "node_modules/@truffle/config/node_modules/path-exists": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/@truffle/contract": {"version": "4.6.31", "license": "MIT", "dependencies": {"@ensdomains/ensjs": "^2.1.0", "@truffle/blockchain-utils": "^0.1.9", "@truffle/contract-schema": "^3.4.16", "@truffle/debug-utils": "^6.0.57", "@truffle/error": "^0.2.2", "@truffle/interface-adapter": "^0.5.37", "bignumber.js": "^7.2.1", "debug": "^4.3.1", "ethers": "^4.0.32", "web3": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-promievent": "1.10.0", "web3-eth-abi": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@truffle/contract-schema": {"version": "3.4.16", "license": "MIT", "dependencies": {"ajv": "^6.10.0", "debug": "^4.3.1"}, "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@truffle/contract/node_modules/@types/node": {"version": "12.20.55", "license": "MIT"}, "node_modules/@truffle/contract/node_modules/eth-lib": {"version": "0.2.8", "license": "MIT", "dependencies": {"bn.js": "^4.11.6", "elliptic": "^6.4.0", "xhr-request-promise": "^0.1.2"}}, "node_modules/@truffle/contract/node_modules/scrypt-js": {"version": "3.0.1", "license": "MIT"}, "node_modules/@truffle/contract/node_modules/uuid": {"version": "9.0.1", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/@truffle/contract/node_modules/web3": {"version": "1.10.0", "hasInstallScript": true, "license": "LGPL-3.0", "dependencies": {"web3-bzz": "1.10.0", "web3-core": "1.10.0", "web3-eth": "1.10.0", "web3-eth-personal": "1.10.0", "web3-net": "1.10.0", "web3-shh": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/contract/node_modules/web3-core": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"@types/bn.js": "^5.1.1", "@types/node": "^12.12.6", "bignumber.js": "^9.0.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-core-requestmanager": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/contract/node_modules/web3-core/node_modules/bignumber.js": {"version": "9.3.0", "license": "MIT", "engines": {"node": "*"}}, "node_modules/@truffle/contract/node_modules/web3-eth": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-core-subscriptions": "1.10.0", "web3-eth-abi": "1.10.0", "web3-eth-accounts": "1.10.0", "web3-eth-contract": "1.10.0", "web3-eth-ens": "1.10.0", "web3-eth-iban": "1.10.0", "web3-eth-personal": "1.10.0", "web3-net": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/contract/node_modules/web3-eth-accounts": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"@ethereumjs/common": "2.5.0", "@ethereumjs/tx": "3.3.2", "eth-lib": "0.2.8", "ethereumjs-util": "^7.1.5", "scrypt-js": "^3.0.1", "uuid": "^9.0.0", "web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/contract/node_modules/web3-eth-contract": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"@types/bn.js": "^5.1.1", "web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-core-promievent": "1.10.0", "web3-core-subscriptions": "1.10.0", "web3-eth-abi": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/contract/node_modules/web3-eth-ens": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"content-hash": "^2.5.2", "eth-ens-namehash": "2.0.8", "web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-promievent": "1.10.0", "web3-eth-abi": "1.10.0", "web3-eth-contract": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/contract/node_modules/web3-eth-iban": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"bn.js": "^5.2.1", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/contract/node_modules/web3-eth-iban/node_modules/bn.js": {"version": "5.2.2", "license": "MIT"}, "node_modules/@truffle/contract/node_modules/web3-eth-personal": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"@types/node": "^12.12.6", "web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-net": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/contract/node_modules/web3-net": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"web3-core": "1.10.0", "web3-core-method": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/dashboard-message-bus-client": {"version": "0.1.12", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@truffle/dashboard-message-bus-common": "^0.1.7", "@truffle/promise-tracker": "^0.1.7", "axios": "1.5.0", "debug": "^4.3.1", "delay": "^5.0.0", "isomorphic-ws": "^4.0.1", "node-abort-controller": "^3.0.1", "tiny-typed-emitter": "^2.1.0", "ws": "^7.2.0"}, "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@truffle/dashboard-message-bus-common": {"version": "0.1.7", "dev": true, "license": "MIT", "optional": true, "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@truffle/db": {"version": "2.0.36", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@graphql-tools/delegate": "^8.4.3", "@graphql-tools/schema": "^8.3.1", "@truffle/abi-utils": "^1.0.3", "@truffle/code-utils": "^3.0.4", "@truffle/config": "^1.3.61", "abstract-leveldown": "^7.2.0", "apollo-server": "^3.11.0", "debug": "^4.3.1", "fs-extra": "^9.1.0", "graphql": "^15.3.0", "graphql-tag": "^2.12.6", "json-stable-stringify": "^1.0.1", "pascal-case": "^2.0.1", "pluralize": "^8.0.0", "pouchdb": "7.3.0", "pouchdb-adapter-memory": "^7.1.1", "pouchdb-debug": "^7.1.1", "pouchdb-find": "^7.0.0", "web3-utils": "1.10.0"}, "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@truffle/db-loader": {"version": "0.2.36", "dev": true, "license": "MIT", "engines": {"node": "^16.20 || ^18.16 || >=20"}, "optionalDependencies": {"@truffle/db": "^2.0.36"}}, "node_modules/@truffle/db/node_modules/fs-extra": {"version": "9.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@truffle/db/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/@truffle/db/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">= 10.0.0"}}, "node_modules/@truffle/debug-utils": {"version": "6.0.57", "license": "MIT", "dependencies": {"@truffle/codec": "^0.17.3", "@trufflesuite/chromafi": "^3.0.0", "bn.js": "^5.1.3", "chalk": "^2.4.2", "debug": "^4.3.1", "highlightjs-solidity": "^2.0.6"}, "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@truffle/debug-utils/node_modules/bn.js": {"version": "5.2.2", "license": "MIT"}, "node_modules/@truffle/debugger": {"version": "12.1.5", "dev": true, "license": "MIT", "dependencies": {"@ensdomains/ensjs": "^2.1.0", "@truffle/abi-utils": "^1.0.3", "@truffle/codec": "^0.17.3", "@truffle/source-map-utils": "^1.3.119", "bn.js": "^5.1.3", "debug": "^4.3.1", "json-pointer": "^0.6.1", "json-stable-stringify": "^1.0.1", "lodash": "^4.17.21", "redux": "^3.7.2", "redux-saga": "1.0.0", "reselect-tree": "^1.3.7", "semver": "^7.5.4", "web3": "1.10.0", "web3-eth-abi": "1.10.0"}, "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@truffle/debugger/node_modules/@types/node": {"version": "12.20.55", "dev": true, "license": "MIT"}, "node_modules/@truffle/debugger/node_modules/bignumber.js": {"version": "9.3.0", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/@truffle/debugger/node_modules/bn.js": {"version": "5.2.2", "dev": true, "license": "MIT"}, "node_modules/@truffle/debugger/node_modules/eth-lib": {"version": "0.2.8", "dev": true, "license": "MIT", "dependencies": {"bn.js": "^4.11.6", "elliptic": "^6.4.0", "xhr-request-promise": "^0.1.2"}}, "node_modules/@truffle/debugger/node_modules/eth-lib/node_modules/bn.js": {"version": "4.12.2", "dev": true, "license": "MIT"}, "node_modules/@truffle/debugger/node_modules/scrypt-js": {"version": "3.0.1", "dev": true, "license": "MIT"}, "node_modules/@truffle/debugger/node_modules/uuid": {"version": "9.0.1", "dev": true, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/@truffle/debugger/node_modules/web3": {"version": "1.10.0", "dev": true, "hasInstallScript": true, "license": "LGPL-3.0", "dependencies": {"web3-bzz": "1.10.0", "web3-core": "1.10.0", "web3-eth": "1.10.0", "web3-eth-personal": "1.10.0", "web3-net": "1.10.0", "web3-shh": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/debugger/node_modules/web3-core": {"version": "1.10.0", "dev": true, "license": "LGPL-3.0", "dependencies": {"@types/bn.js": "^5.1.1", "@types/node": "^12.12.6", "bignumber.js": "^9.0.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-core-requestmanager": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/debugger/node_modules/web3-eth": {"version": "1.10.0", "dev": true, "license": "LGPL-3.0", "dependencies": {"web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-core-subscriptions": "1.10.0", "web3-eth-abi": "1.10.0", "web3-eth-accounts": "1.10.0", "web3-eth-contract": "1.10.0", "web3-eth-ens": "1.10.0", "web3-eth-iban": "1.10.0", "web3-eth-personal": "1.10.0", "web3-net": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/debugger/node_modules/web3-eth-accounts": {"version": "1.10.0", "dev": true, "license": "LGPL-3.0", "dependencies": {"@ethereumjs/common": "2.5.0", "@ethereumjs/tx": "3.3.2", "eth-lib": "0.2.8", "ethereumjs-util": "^7.1.5", "scrypt-js": "^3.0.1", "uuid": "^9.0.0", "web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/debugger/node_modules/web3-eth-contract": {"version": "1.10.0", "dev": true, "license": "LGPL-3.0", "dependencies": {"@types/bn.js": "^5.1.1", "web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-core-promievent": "1.10.0", "web3-core-subscriptions": "1.10.0", "web3-eth-abi": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/debugger/node_modules/web3-eth-ens": {"version": "1.10.0", "dev": true, "license": "LGPL-3.0", "dependencies": {"content-hash": "^2.5.2", "eth-ens-namehash": "2.0.8", "web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-promievent": "1.10.0", "web3-eth-abi": "1.10.0", "web3-eth-contract": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/debugger/node_modules/web3-eth-iban": {"version": "1.10.0", "dev": true, "license": "LGPL-3.0", "dependencies": {"bn.js": "^5.2.1", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/debugger/node_modules/web3-eth-personal": {"version": "1.10.0", "dev": true, "license": "LGPL-3.0", "dependencies": {"@types/node": "^12.12.6", "web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-net": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/debugger/node_modules/web3-net": {"version": "1.10.0", "dev": true, "license": "LGPL-3.0", "dependencies": {"web3-core": "1.10.0", "web3-core-method": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/error": {"version": "0.2.2", "license": "MIT", "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@truffle/events": {"version": "0.1.25", "dev": true, "license": "ISC", "optional": true, "dependencies": {"@truffle/dashboard-message-bus-client": "^0.1.12", "@truffle/spinners": "^0.2.5", "debug": "^4.3.1", "emittery": "^0.4.1", "web3-utils": "1.10.0"}, "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@truffle/events/node_modules/emittery": {"version": "0.4.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/@truffle/interface-adapter": {"version": "0.5.37", "license": "MIT", "dependencies": {"bn.js": "^5.1.3", "ethers": "^4.0.32", "web3": "1.10.0"}, "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@truffle/interface-adapter/node_modules/@types/node": {"version": "12.20.55", "license": "MIT"}, "node_modules/@truffle/interface-adapter/node_modules/bignumber.js": {"version": "9.3.0", "license": "MIT", "engines": {"node": "*"}}, "node_modules/@truffle/interface-adapter/node_modules/bn.js": {"version": "5.2.2", "license": "MIT"}, "node_modules/@truffle/interface-adapter/node_modules/eth-lib": {"version": "0.2.8", "license": "MIT", "dependencies": {"bn.js": "^4.11.6", "elliptic": "^6.4.0", "xhr-request-promise": "^0.1.2"}}, "node_modules/@truffle/interface-adapter/node_modules/eth-lib/node_modules/bn.js": {"version": "4.12.2", "license": "MIT"}, "node_modules/@truffle/interface-adapter/node_modules/scrypt-js": {"version": "3.0.1", "license": "MIT"}, "node_modules/@truffle/interface-adapter/node_modules/uuid": {"version": "9.0.1", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/@truffle/interface-adapter/node_modules/web3": {"version": "1.10.0", "hasInstallScript": true, "license": "LGPL-3.0", "dependencies": {"web3-bzz": "1.10.0", "web3-core": "1.10.0", "web3-eth": "1.10.0", "web3-eth-personal": "1.10.0", "web3-net": "1.10.0", "web3-shh": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/interface-adapter/node_modules/web3-core": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"@types/bn.js": "^5.1.1", "@types/node": "^12.12.6", "bignumber.js": "^9.0.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-core-requestmanager": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/interface-adapter/node_modules/web3-eth": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-core-subscriptions": "1.10.0", "web3-eth-abi": "1.10.0", "web3-eth-accounts": "1.10.0", "web3-eth-contract": "1.10.0", "web3-eth-ens": "1.10.0", "web3-eth-iban": "1.10.0", "web3-eth-personal": "1.10.0", "web3-net": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/interface-adapter/node_modules/web3-eth-accounts": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"@ethereumjs/common": "2.5.0", "@ethereumjs/tx": "3.3.2", "eth-lib": "0.2.8", "ethereumjs-util": "^7.1.5", "scrypt-js": "^3.0.1", "uuid": "^9.0.0", "web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/interface-adapter/node_modules/web3-eth-contract": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"@types/bn.js": "^5.1.1", "web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-core-promievent": "1.10.0", "web3-core-subscriptions": "1.10.0", "web3-eth-abi": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/interface-adapter/node_modules/web3-eth-ens": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"content-hash": "^2.5.2", "eth-ens-namehash": "2.0.8", "web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-promievent": "1.10.0", "web3-eth-abi": "1.10.0", "web3-eth-contract": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/interface-adapter/node_modules/web3-eth-iban": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"bn.js": "^5.2.1", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/interface-adapter/node_modules/web3-eth-personal": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"@types/node": "^12.12.6", "web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-net": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/interface-adapter/node_modules/web3-net": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"web3-core": "1.10.0", "web3-core-method": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/promise-tracker": {"version": "0.1.7", "dev": true, "license": "MIT", "optional": true, "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@truffle/provider": {"version": "0.3.13", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@truffle/error": "^0.2.2", "@truffle/interface-adapter": "^0.5.37", "debug": "^4.3.1", "web3": "1.10.0"}, "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@truffle/provider/node_modules/@types/node": {"version": "12.20.55", "dev": true, "license": "MIT", "optional": true}, "node_modules/@truffle/provider/node_modules/bignumber.js": {"version": "9.3.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": "*"}}, "node_modules/@truffle/provider/node_modules/eth-lib": {"version": "0.2.8", "dev": true, "license": "MIT", "optional": true, "dependencies": {"bn.js": "^4.11.6", "elliptic": "^6.4.0", "xhr-request-promise": "^0.1.2"}}, "node_modules/@truffle/provider/node_modules/scrypt-js": {"version": "3.0.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/@truffle/provider/node_modules/uuid": {"version": "9.0.1", "dev": true, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "optional": true, "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/@truffle/provider/node_modules/web3": {"version": "1.10.0", "dev": true, "hasInstallScript": true, "license": "LGPL-3.0", "optional": true, "dependencies": {"web3-bzz": "1.10.0", "web3-core": "1.10.0", "web3-eth": "1.10.0", "web3-eth-personal": "1.10.0", "web3-net": "1.10.0", "web3-shh": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/provider/node_modules/web3-core": {"version": "1.10.0", "dev": true, "license": "LGPL-3.0", "optional": true, "dependencies": {"@types/bn.js": "^5.1.1", "@types/node": "^12.12.6", "bignumber.js": "^9.0.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-core-requestmanager": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/provider/node_modules/web3-eth": {"version": "1.10.0", "dev": true, "license": "LGPL-3.0", "optional": true, "dependencies": {"web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-core-subscriptions": "1.10.0", "web3-eth-abi": "1.10.0", "web3-eth-accounts": "1.10.0", "web3-eth-contract": "1.10.0", "web3-eth-ens": "1.10.0", "web3-eth-iban": "1.10.0", "web3-eth-personal": "1.10.0", "web3-net": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/provider/node_modules/web3-eth-accounts": {"version": "1.10.0", "dev": true, "license": "LGPL-3.0", "optional": true, "dependencies": {"@ethereumjs/common": "2.5.0", "@ethereumjs/tx": "3.3.2", "eth-lib": "0.2.8", "ethereumjs-util": "^7.1.5", "scrypt-js": "^3.0.1", "uuid": "^9.0.0", "web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/provider/node_modules/web3-eth-contract": {"version": "1.10.0", "dev": true, "license": "LGPL-3.0", "optional": true, "dependencies": {"@types/bn.js": "^5.1.1", "web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-core-promievent": "1.10.0", "web3-core-subscriptions": "1.10.0", "web3-eth-abi": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/provider/node_modules/web3-eth-ens": {"version": "1.10.0", "dev": true, "license": "LGPL-3.0", "optional": true, "dependencies": {"content-hash": "^2.5.2", "eth-ens-namehash": "2.0.8", "web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-promievent": "1.10.0", "web3-eth-abi": "1.10.0", "web3-eth-contract": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/provider/node_modules/web3-eth-iban": {"version": "1.10.0", "dev": true, "license": "LGPL-3.0", "optional": true, "dependencies": {"bn.js": "^5.2.1", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/provider/node_modules/web3-eth-iban/node_modules/bn.js": {"version": "5.2.2", "dev": true, "license": "MIT", "optional": true}, "node_modules/@truffle/provider/node_modules/web3-eth-personal": {"version": "1.10.0", "dev": true, "license": "LGPL-3.0", "optional": true, "dependencies": {"@types/node": "^12.12.6", "web3-core": "1.10.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-net": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/provider/node_modules/web3-net": {"version": "1.10.0", "dev": true, "license": "LGPL-3.0", "optional": true, "dependencies": {"web3-core": "1.10.0", "web3-core-method": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@truffle/source-map-utils": {"version": "1.3.119", "dev": true, "license": "MIT", "dependencies": {"@truffle/code-utils": "^3.0.4", "@truffle/codec": "^0.17.3", "debug": "^4.3.1", "json-pointer": "^0.6.1", "node-interval-tree": "^1.3.3", "web3-utils": "1.10.0"}, "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@truffle/spinners": {"version": "0.2.5", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@trufflesuite/spinnies": "^0.1.1"}, "engines": {"node": "^16.20 || ^18.16 || >=20"}}, "node_modules/@trufflesuite/chromafi": {"version": "3.0.0", "license": "MIT", "dependencies": {"camelcase": "^4.1.0", "chalk": "^2.3.2", "cheerio": "^1.0.0-rc.2", "detect-indent": "^5.0.0", "highlight.js": "^10.4.1", "lodash.merge": "^4.6.2", "strip-ansi": "^4.0.0", "strip-indent": "^2.0.0"}}, "node_modules/@trufflesuite/spinnies": {"version": "0.1.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"chalk": "^4.1.2", "cli-cursor": "^3.1.0", "strip-ansi": "^6.0.0"}}, "node_modules/@trufflesuite/spinnies/node_modules/ansi-regex": {"version": "5.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=8"}}, "node_modules/@trufflesuite/spinnies/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@trufflesuite/spinnies/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/@trufflesuite/spinnies/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/@trufflesuite/spinnies/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT", "optional": true}, "node_modules/@trufflesuite/spinnies/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=8"}}, "node_modules/@trufflesuite/spinnies/node_modules/strip-ansi": {"version": "6.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/@trufflesuite/spinnies/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@types/accepts": {"version": "1.3.7", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@types/node": "*"}}, "node_modules/@types/babel__core": {"version": "7.20.5", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "node_modules/@types/babel__generator": {"version": "7.27.0", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@types/babel__template": {"version": "7.4.4", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@types/babel__traverse": {"version": "7.20.7", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.20.7"}}, "node_modules/@types/bn.js": {"version": "5.2.0", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/body-parser": {"version": "1.19.6", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/cacheable-request": {"version": "6.0.3", "license": "MIT", "dependencies": {"@types/http-cache-semantics": "*", "@types/keyv": "^3.1.4", "@types/node": "*", "@types/responselike": "^1.0.0"}}, "node_modules/@types/connect": {"version": "3.4.38", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@types/node": "*"}}, "node_modules/@types/cors": {"version": "2.8.12", "dev": true, "license": "MIT", "optional": true}, "node_modules/@types/express": {"version": "4.17.14", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.18", "@types/qs": "*", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "4.19.6", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/graceful-fs": {"version": "4.1.9", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/http-cache-semantics": {"version": "4.0.4", "license": "MIT"}, "node_modules/@types/http-errors": {"version": "2.0.5", "dev": true, "license": "MIT", "optional": true}, "node_modules/@types/istanbul-lib-coverage": {"version": "2.0.6", "dev": true, "license": "MIT"}, "node_modules/@types/istanbul-lib-report": {"version": "3.0.3", "dev": true, "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "*"}}, "node_modules/@types/istanbul-reports": {"version": "3.0.4", "dev": true, "license": "MIT", "dependencies": {"@types/istanbul-lib-report": "*"}}, "node_modules/@types/keyv": {"version": "3.1.4", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/long": {"version": "4.0.2", "dev": true, "license": "MIT", "optional": true}, "node_modules/@types/mime": {"version": "1.3.5", "dev": true, "license": "MIT", "optional": true}, "node_modules/@types/node": {"version": "24.0.4", "license": "MIT", "dependencies": {"undici-types": "~7.8.0"}}, "node_modules/@types/pbkdf2": {"version": "3.1.2", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/qs": {"version": "6.14.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/@types/range-parser": {"version": "1.2.7", "dev": true, "license": "MIT", "optional": true}, "node_modules/@types/responselike": {"version": "1.0.3", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/secp256k1": {"version": "4.0.6", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/send": {"version": "0.17.5", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/serve-static": {"version": "1.15.8", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}}, "node_modules/@types/stack-utils": {"version": "2.0.3", "dev": true, "license": "MIT"}, "node_modules/@types/webidl-conversions": {"version": "7.0.3", "resolved": "https://registry.npmjs.org/@types/webidl-conversions/-/webidl-conversions-7.0.3.tgz", "integrity": "sha512-CiJJvcRtIgzadHCYXw7dqEnMNRjhGZlYK05Mj9OyktqV8uVT8fD2BFOB7S1uwBE3Kj2Z+4UyPmFw/Ixgw/LAlA==", "license": "MIT"}, "node_modules/@types/whatwg-url": {"version": "8.2.2", "resolved": "https://registry.npmjs.org/@types/whatwg-url/-/whatwg-url-8.2.2.tgz", "integrity": "sha512-FtQu10RWgn3D9U4aazdwIE2yzphmTJREDqNdODHrbrZmmMqI0vMheC/6NE/J1Yveaj8H+ela+YwWTjq5PGmuhA==", "license": "MIT", "dependencies": {"@types/node": "*", "@types/webidl-conversions": "*"}}, "node_modules/@types/ws": {"version": "8.5.3", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/yargs": {"version": "17.0.33", "dev": true, "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/@types/yargs-parser": {"version": "21.0.3", "dev": true, "license": "MIT"}, "node_modules/abitype": {"version": "0.7.1", "license": "MIT", "peerDependencies": {"typescript": ">=4.9.4", "zod": "^3 >=3.19.1"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "node_modules/abort-controller": {"version": "3.0.0", "license": "MIT", "dependencies": {"event-target-shim": "^5.0.0"}, "engines": {"node": ">=6.5"}}, "node_modules/abortcontroller-polyfill": {"version": "1.7.8", "license": "MIT"}, "node_modules/abstract-leveldown": {"version": "7.2.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"buffer": "^6.0.3", "catering": "^2.0.0", "is-buffer": "^2.0.5", "level-concat-iterator": "^3.0.0", "level-supports": "^2.0.1", "queue-microtask": "^1.2.3"}, "engines": {"node": ">=10"}}, "node_modules/abstract-leveldown/node_modules/buffer": {"version": "6.0.3", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "optional": true, "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/accepts": {"version": "1.3.8", "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/aes-js": {"version": "3.0.0", "license": "MIT"}, "node_modules/ajv": {"version": "6.12.6", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-formats": {"version": "2.1.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"ajv": "^8.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "node_modules/ajv-formats/node_modules/ajv": {"version": "8.17.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-formats/node_modules/json-schema-traverse": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/ajv-formats/node_modules/require-from-string": {"version": "2.0.2", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/ansi-colors": {"version": "4.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ansi-escapes": {"version": "4.3.2", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-regex": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ansi-styles": {"version": "3.2.1", "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/anymatch": {"version": "3.1.3", "dev": true, "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/apollo-datasource": {"version": "3.3.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@apollo/utils.keyvaluecache": "^1.0.1", "apollo-server-env": "^4.2.1"}, "engines": {"node": ">=12.0"}}, "node_modules/apollo-reporting-protobuf": {"version": "3.4.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@apollo/protobufjs": "1.2.6"}}, "node_modules/apollo-reporting-protobuf/node_modules/@apollo/protobufjs": {"version": "1.2.6", "dev": true, "hasInstallScript": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/long": "^4.0.0", "@types/node": "^10.1.0", "long": "^4.0.0"}, "bin": {"apollo-pbjs": "bin/pbjs", "apollo-pbts": "bin/pbts"}}, "node_modules/apollo-reporting-protobuf/node_modules/@types/node": {"version": "10.17.60", "dev": true, "license": "MIT", "optional": true}, "node_modules/apollo-server": {"version": "3.13.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@types/express": "4.17.14", "apollo-server-core": "^3.13.0", "apollo-server-express": "^3.13.0", "express": "^4.17.1"}, "peerDependencies": {"graphql": "^15.3.0 || ^16.0.0"}}, "node_modules/apollo-server-core": {"version": "3.13.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@apollo/utils.keyvaluecache": "^1.0.1", "@apollo/utils.logger": "^1.0.0", "@apollo/utils.usagereporting": "^1.0.0", "@apollographql/apollo-tools": "^0.5.3", "@apollographql/graphql-playground-html": "1.6.29", "@graphql-tools/mock": "^8.1.2", "@graphql-tools/schema": "^8.0.0", "@josephg/resolvable": "^1.0.0", "apollo-datasource": "^3.3.2", "apollo-reporting-protobuf": "^3.4.0", "apollo-server-env": "^4.2.1", "apollo-server-errors": "^3.3.1", "apollo-server-plugin-base": "^3.7.2", "apollo-server-types": "^3.8.0", "async-retry": "^1.2.1", "fast-json-stable-stringify": "^2.1.0", "graphql-tag": "^2.11.0", "loglevel": "^1.6.8", "lru-cache": "^6.0.0", "node-abort-controller": "^3.0.1", "sha.js": "^2.4.11", "uuid": "^9.0.0", "whatwg-mimetype": "^3.0.0"}, "engines": {"node": ">=12.0"}, "peerDependencies": {"graphql": "^15.3.0 || ^16.0.0"}}, "node_modules/apollo-server-core/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "optional": true, "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/apollo-server-core/node_modules/uuid": {"version": "9.0.1", "dev": true, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "optional": true, "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/apollo-server-core/node_modules/whatwg-mimetype": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=12"}}, "node_modules/apollo-server-core/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC", "optional": true}, "node_modules/apollo-server-env": {"version": "4.2.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"node-fetch": "^2.6.7"}, "engines": {"node": ">=12.0"}}, "node_modules/apollo-server-env/node_modules/node-fetch": {"version": "2.7.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/apollo-server-env/node_modules/tr46": {"version": "0.0.3", "dev": true, "license": "MIT", "optional": true}, "node_modules/apollo-server-env/node_modules/webidl-conversions": {"version": "3.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "optional": true}, "node_modules/apollo-server-env/node_modules/whatwg-url": {"version": "5.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/apollo-server-errors": {"version": "3.3.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=12.0"}, "peerDependencies": {"graphql": "^15.3.0 || ^16.0.0"}}, "node_modules/apollo-server-express": {"version": "3.13.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@types/accepts": "^1.3.5", "@types/body-parser": "1.19.2", "@types/cors": "2.8.12", "@types/express": "4.17.14", "@types/express-serve-static-core": "4.17.31", "accepts": "^1.3.5", "apollo-server-core": "^3.13.0", "apollo-server-types": "^3.8.0", "body-parser": "^1.19.0", "cors": "^2.8.5", "parseurl": "^1.3.3"}, "engines": {"node": ">=12.0"}, "peerDependencies": {"express": "^4.17.1", "graphql": "^15.3.0 || ^16.0.0"}}, "node_modules/apollo-server-express/node_modules/@types/body-parser": {"version": "1.19.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/apollo-server-express/node_modules/@types/express-serve-static-core": {"version": "4.17.31", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*"}}, "node_modules/apollo-server-plugin-base": {"version": "3.7.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"apollo-server-types": "^3.8.0"}, "engines": {"node": ">=12.0"}, "peerDependencies": {"graphql": "^15.3.0 || ^16.0.0"}}, "node_modules/apollo-server-types": {"version": "3.8.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@apollo/utils.keyvaluecache": "^1.0.1", "@apollo/utils.logger": "^1.0.0", "apollo-reporting-protobuf": "^3.4.0", "apollo-server-env": "^4.2.1"}, "engines": {"node": ">=12.0"}, "peerDependencies": {"graphql": "^15.3.0 || ^16.0.0"}}, "node_modules/app-module-path": {"version": "2.2.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/append-field": {"version": "1.0.0", "license": "MIT"}, "node_modules/archiver": {"version": "7.0.1", "license": "MIT", "dependencies": {"archiver-utils": "^5.0.2", "async": "^3.2.4", "buffer-crc32": "^1.0.0", "readable-stream": "^4.0.0", "readdir-glob": "^1.1.2", "tar-stream": "^3.0.0", "zip-stream": "^6.0.1"}, "engines": {"node": ">= 14"}}, "node_modules/archiver-utils": {"version": "5.0.2", "license": "MIT", "dependencies": {"glob": "^10.0.0", "graceful-fs": "^4.2.0", "is-stream": "^2.0.1", "lazystream": "^1.0.0", "lodash": "^4.17.15", "normalize-path": "^3.0.0", "readable-stream": "^4.0.0"}, "engines": {"node": ">= 14"}}, "node_modules/archiver-utils/node_modules/brace-expansion": {"version": "2.0.2", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/archiver-utils/node_modules/buffer": {"version": "6.0.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/archiver-utils/node_modules/glob": {"version": "10.4.5", "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/archiver-utils/node_modules/minimatch": {"version": "9.0.5", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/archiver-utils/node_modules/minipass": {"version": "7.1.2", "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/archiver-utils/node_modules/readable-stream": {"version": "4.7.0", "license": "MIT", "dependencies": {"abort-controller": "^3.0.0", "buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "string_decoder": "^1.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/archiver-utils/node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/archiver/node_modules/buffer": {"version": "6.0.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/archiver/node_modules/readable-stream": {"version": "4.7.0", "license": "MIT", "dependencies": {"abort-controller": "^3.0.0", "buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "string_decoder": "^1.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/archiver/node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/argparse": {"version": "1.0.10", "dev": true, "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/argsarray": {"version": "0.0.1", "dev": true, "license": "WTFPL", "optional": true}, "node_modules/array-flatten": {"version": "1.1.1", "license": "MIT"}, "node_modules/asn1": {"version": "0.2.6", "license": "MIT", "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/assert-plus": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/async": {"version": "3.2.6", "license": "MIT"}, "node_modules/async-limiter": {"version": "1.0.1", "license": "MIT"}, "node_modules/async-retry": {"version": "1.3.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"retry": "0.13.1"}}, "node_modules/asynckit": {"version": "0.4.0", "license": "MIT"}, "node_modules/at-least-node": {"version": "1.0.0", "dev": true, "license": "ISC", "optional": true, "engines": {"node": ">= 4.0.0"}}, "node_modules/atomically": {"version": "1.7.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=10.12.0"}}, "node_modules/available-typed-arrays": {"version": "1.0.7", "license": "MIT", "dependencies": {"possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/aws-sign2": {"version": "0.7.0", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/aws4": {"version": "1.13.2", "license": "MIT"}, "node_modules/axios": {"version": "1.5.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"follow-redirects": "^1.15.0", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/b4a": {"version": "1.6.7", "license": "Apache-2.0"}, "node_modules/babel-jest": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/transform": "^29.7.0", "@types/babel__core": "^7.1.14", "babel-plugin-istanbul": "^6.1.1", "babel-preset-jest": "^29.6.3", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@babel/core": "^7.8.0"}}, "node_modules/babel-jest/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/babel-jest/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/babel-jest/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/babel-jest/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/babel-jest/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/babel-jest/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/babel-plugin-istanbul": {"version": "6.1.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^5.0.4", "test-exclude": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/babel-plugin-istanbul/node_modules/istanbul-lib-instrument": {"version": "5.2.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0", "semver": "^6.3.0"}, "engines": {"node": ">=8"}}, "node_modules/babel-plugin-istanbul/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/babel-plugin-jest-hoist": {"version": "29.6.3", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.3.3", "@babel/types": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/babel-preset-current-node-syntax": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-import-attributes": "^7.24.7", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/babel-preset-jest": {"version": "29.6.3", "dev": true, "license": "MIT", "dependencies": {"babel-plugin-jest-hoist": "^29.6.3", "babel-preset-current-node-syntax": "^1.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "license": "MIT"}, "node_modules/bare-events": {"version": "2.6.0", "license": "Apache-2.0", "optional": true}, "node_modules/base-x": {"version": "3.0.11", "license": "MIT", "dependencies": {"safe-buffer": "^5.0.1"}}, "node_modules/base64-js": {"version": "1.5.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/bcrypt-pbkdf": {"version": "1.0.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/bcryptjs": {"version": "2.4.3", "license": "MIT"}, "node_modules/bech32": {"version": "1.1.4", "license": "MIT"}, "node_modules/big-integer": {"version": "1.6.36", "license": "Unlicense", "engines": {"node": ">=0.6"}}, "node_modules/big.js": {"version": "6.2.2", "license": "MIT", "engines": {"node": "*"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/bigjs"}}, "node_modules/bignumber.js": {"version": "7.2.1", "license": "MIT", "engines": {"node": "*"}}, "node_modules/binary-extensions": {"version": "2.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/blakejs": {"version": "1.2.1", "license": "MIT"}, "node_modules/bluebird": {"version": "3.7.2", "license": "MIT"}, "node_modules/bn.js": {"version": "4.12.2", "license": "MIT"}, "node_modules/body-parser": {"version": "1.20.3", "license": "MIT", "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.5", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.13.0", "raw-body": "2.5.2", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/body-parser/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/body-parser/node_modules/iconv-lite": {"version": "0.4.24", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/body-parser/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/boolbase": {"version": "1.0.0", "license": "ISC"}, "node_modules/brace-expansion": {"version": "1.1.12", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.3", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/brorand": {"version": "1.1.0", "license": "MIT"}, "node_modules/browser-stdout": {"version": "1.3.1", "dev": true, "license": "ISC"}, "node_modules/browserify-aes": {"version": "1.2.0", "license": "MIT", "dependencies": {"buffer-xor": "^1.0.3", "cipher-base": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.3", "inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "node_modules/browserslist": {"version": "4.25.1", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001726", "electron-to-chromium": "^1.5.173", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/bs58": {"version": "4.0.1", "license": "MIT", "dependencies": {"base-x": "^3.0.2"}}, "node_modules/bs58check": {"version": "2.1.2", "license": "MIT", "dependencies": {"bs58": "^4.0.0", "create-hash": "^1.1.0", "safe-buffer": "^5.1.2"}}, "node_modules/bser": {"version": "2.1.1", "dev": true, "license": "Apache-2.0", "dependencies": {"node-int64": "^0.4.0"}}, "node_modules/bson": {"version": "5.5.1", "resolved": "https://registry.npmjs.org/bson/-/bson-5.5.1.tgz", "integrity": "sha512-ix0EwukN2EpC0SRWIj/7B5+A6uQMQy6KMREI9qQqvgpkV2frH63T0UDVd1SYedL6dNCmDBYB3QtXi4ISk9YT+g==", "license": "Apache-2.0", "engines": {"node": ">=14.20.1"}}, "node_modules/buffer": {"version": "5.7.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/buffer-crc32": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=8.0.0"}}, "node_modules/buffer-equal-constant-time": {"version": "1.0.1", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/buffer-from": {"version": "1.1.2", "license": "MIT"}, "node_modules/buffer-to-arraybuffer": {"version": "0.0.5", "license": "MIT"}, "node_modules/buffer-xor": {"version": "1.0.3", "license": "MIT"}, "node_modules/bufferutil": {"version": "4.0.9", "hasInstallScript": true, "license": "MIT", "dependencies": {"node-gyp-build": "^4.3.0"}, "engines": {"node": ">=6.14.2"}}, "node_modules/bufferutil/node_modules/node-gyp-build": {"version": "4.8.4", "license": "MIT", "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}}, "node_modules/busboy": {"version": "1.6.0", "dependencies": {"streamsearch": "^1.1.0"}, "engines": {"node": ">=10.16.0"}}, "node_modules/bytes": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/cacheable-lookup": {"version": "6.1.0", "license": "MIT", "engines": {"node": ">=10.6.0"}}, "node_modules/cacheable-request": {"version": "7.0.4", "license": "MIT", "dependencies": {"clone-response": "^1.0.2", "get-stream": "^5.1.0", "http-cache-semantics": "^4.0.0", "keyv": "^4.0.0", "lowercase-keys": "^2.0.0", "normalize-url": "^6.0.1", "responselike": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/cacheable-request/node_modules/get-stream": {"version": "5.2.0", "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cacheable-request/node_modules/lowercase-keys": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/call-bind": {"version": "1.0.8", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/camel-case": {"version": "3.0.0", "license": "MIT", "dependencies": {"no-case": "^2.2.0", "upper-case": "^1.1.1"}}, "node_modules/camelcase": {"version": "4.1.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/caniuse-lite": {"version": "1.0.30001726", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/caseless": {"version": "0.12.0", "license": "Apache-2.0"}, "node_modules/catering": {"version": "2.1.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/cbor": {"version": "5.2.0", "license": "MIT", "dependencies": {"bignumber.js": "^9.0.1", "nofilter": "^1.0.4"}, "engines": {"node": ">=6.0.0"}}, "node_modules/cbor/node_modules/bignumber.js": {"version": "9.3.0", "license": "MIT", "engines": {"node": "*"}}, "node_modules/chalk": {"version": "2.4.2", "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/change-case": {"version": "3.0.2", "license": "MIT", "dependencies": {"camel-case": "^3.0.0", "constant-case": "^2.0.0", "dot-case": "^2.1.0", "header-case": "^1.0.0", "is-lower-case": "^1.1.0", "is-upper-case": "^1.1.0", "lower-case": "^1.1.1", "lower-case-first": "^1.0.0", "no-case": "^2.3.2", "param-case": "^2.1.0", "pascal-case": "^2.0.0", "path-case": "^2.1.0", "sentence-case": "^2.1.0", "snake-case": "^2.1.0", "swap-case": "^1.1.0", "title-case": "^2.1.0", "upper-case": "^1.1.1", "upper-case-first": "^1.1.0"}}, "node_modules/char-regex": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/cheerio": {"version": "1.1.0", "license": "MIT", "dependencies": {"cheerio-select": "^2.1.0", "dom-serializer": "^2.0.0", "domhandler": "^5.0.3", "domutils": "^3.2.2", "encoding-sniffer": "^0.2.0", "htmlparser2": "^10.0.0", "parse5": "^7.3.0", "parse5-htmlparser2-tree-adapter": "^7.1.0", "parse5-parser-stream": "^7.1.2", "undici": "^7.10.0", "whatwg-mimetype": "^4.0.0"}, "engines": {"node": ">=18.17"}, "funding": {"url": "https://github.com/cheeriojs/cheerio?sponsor=1"}}, "node_modules/cheerio-select": {"version": "2.1.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0", "css-select": "^5.1.0", "css-what": "^6.1.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3", "domutils": "^3.0.1"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/chokidar": {"version": "3.6.0", "dev": true, "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "funding": {"url": "https://paulmillr.com/funding/"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chokidar/node_modules/fsevents": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/chownr": {"version": "1.1.4", "license": "ISC"}, "node_modules/ci-info": {"version": "3.9.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cids": {"version": "0.7.5", "license": "MIT", "dependencies": {"buffer": "^5.5.0", "class-is": "^1.1.0", "multibase": "~0.6.0", "multicodec": "^1.0.0", "multihashes": "~0.4.15"}, "engines": {"node": ">=4.0.0", "npm": ">=3.0.0"}}, "node_modules/cids/node_modules/multicodec": {"version": "1.0.4", "license": "MIT", "dependencies": {"buffer": "^5.6.0", "varint": "^5.0.0"}}, "node_modules/cipher-base": {"version": "1.0.6", "license": "MIT", "dependencies": {"inherits": "^2.0.4", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/cjs-module-lexer": {"version": "1.4.3", "dev": true, "license": "MIT"}, "node_modules/class-is": {"version": "1.1.0", "license": "MIT"}, "node_modules/cli-cursor": {"version": "3.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"restore-cursor": "^3.1.0"}, "engines": {"node": ">=8"}}, "node_modules/cliui": {"version": "8.0.1", "dev": true, "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/cliui/node_modules/ansi-regex": {"version": "5.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cliui/node_modules/strip-ansi": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/clone-buffer": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">= 0.10"}}, "node_modules/clone-response": {"version": "1.0.3", "license": "MIT", "dependencies": {"mimic-response": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/co": {"version": "4.6.0", "dev": true, "license": "MIT", "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}}, "node_modules/code-point-at": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/collect-v8-coverage": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/color-convert": {"version": "1.9.3", "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/color-name": {"version": "1.1.3", "license": "MIT"}, "node_modules/colors": {"version": "1.4.0", "license": "MIT", "engines": {"node": ">=0.1.90"}}, "node_modules/combined-stream": {"version": "1.0.8", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "2.20.3", "dev": true, "license": "MIT", "optional": true}, "node_modules/compress-commons": {"version": "6.0.2", "license": "MIT", "dependencies": {"crc-32": "^1.2.0", "crc32-stream": "^6.0.0", "is-stream": "^2.0.1", "normalize-path": "^3.0.0", "readable-stream": "^4.0.0"}, "engines": {"node": ">= 14"}}, "node_modules/compress-commons/node_modules/buffer": {"version": "6.0.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/compress-commons/node_modules/readable-stream": {"version": "4.7.0", "license": "MIT", "dependencies": {"abort-controller": "^3.0.0", "buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "string_decoder": "^1.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/compress-commons/node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/concat-map": {"version": "0.0.1", "license": "MIT"}, "node_modules/concat-stream": {"version": "1.6.2", "engines": ["node >= 0.8"], "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "node_modules/conf": {"version": "10.2.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"ajv": "^8.6.3", "ajv-formats": "^2.1.1", "atomically": "^1.7.0", "debounce-fn": "^4.0.0", "dot-prop": "^6.0.1", "env-paths": "^2.2.1", "json-schema-typed": "^7.0.3", "onetime": "^5.1.2", "pkg-up": "^3.1.0", "semver": "^7.3.5"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/conf/node_modules/ajv": {"version": "8.17.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/conf/node_modules/json-schema-traverse": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/conf/node_modules/require-from-string": {"version": "2.0.2", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/constant-case": {"version": "2.0.0", "license": "MIT", "dependencies": {"snake-case": "^2.1.0", "upper-case": "^1.1.1"}}, "node_modules/content-disposition": {"version": "0.5.4", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-hash": {"version": "2.5.2", "license": "ISC", "dependencies": {"cids": "^0.7.1", "multicodec": "^0.5.5", "multihashes": "^0.4.15"}}, "node_modules/content-type": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/convert-source-map": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/cookie": {"version": "0.7.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "license": "MIT"}, "node_modules/core-util-is": {"version": "1.0.3", "license": "MIT"}, "node_modules/cors": {"version": "2.8.5", "license": "MIT", "dependencies": {"object-assign": "^4", "vary": "^1"}, "engines": {"node": ">= 0.10"}}, "node_modules/crc-32": {"version": "1.2.2", "license": "Apache-2.0", "bin": {"crc32": "bin/crc32.njs"}, "engines": {"node": ">=0.8"}}, "node_modules/crc32-stream": {"version": "6.0.0", "license": "MIT", "dependencies": {"crc-32": "^1.2.0", "readable-stream": "^4.0.0"}, "engines": {"node": ">= 14"}}, "node_modules/crc32-stream/node_modules/buffer": {"version": "6.0.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/crc32-stream/node_modules/readable-stream": {"version": "4.7.0", "license": "MIT", "dependencies": {"abort-controller": "^3.0.0", "buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "string_decoder": "^1.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/crc32-stream/node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/create-hash": {"version": "1.2.0", "license": "MIT", "dependencies": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "md5.js": "^1.3.4", "ripemd160": "^2.0.1", "sha.js": "^2.4.0"}}, "node_modules/create-hmac": {"version": "1.1.7", "license": "MIT", "dependencies": {"cipher-base": "^1.0.3", "create-hash": "^1.1.0", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "node_modules/create-jest": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "chalk": "^4.0.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "jest-config": "^29.7.0", "jest-util": "^29.7.0", "prompts": "^2.0.1"}, "bin": {"create-jest": "bin/create-jest.js"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/create-jest/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/create-jest/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/create-jest/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/create-jest/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/create-jest/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/create-jest/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/cross-fetch": {"version": "4.1.0", "license": "MIT", "dependencies": {"node-fetch": "^2.7.0"}}, "node_modules/cross-fetch/node_modules/node-fetch": {"version": "2.7.0", "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/cross-fetch/node_modules/tr46": {"version": "0.0.3", "license": "MIT"}, "node_modules/cross-fetch/node_modules/webidl-conversions": {"version": "3.0.1", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/cross-fetch/node_modules/whatwg-url": {"version": "5.0.0", "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/cross-spawn": {"version": "7.0.6", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/crypto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/crypto/-/crypto-1.0.1.tgz", "integrity": "sha512-VxBKmeNcqQdiUQUW2Tzq0t377b54N2bMtXO/qiLa+6eRRmmC4qT3D4OnTGoT/U6O9aklQ/jTwbOtRMTTY8G0Ig==", "deprecated": "This package is no longer supported. It's now a built-in Node module. If you've depended on crypto, you should switch to the one that's built-in.", "license": "ISC"}, "node_modules/crypto-addr-codec": {"version": "0.1.8", "license": "MIT", "dependencies": {"base-x": "^3.0.8", "big-integer": "1.6.36", "blakejs": "^1.1.0", "bs58": "^4.0.1", "ripemd160-min": "0.0.6", "safe-buffer": "^5.2.0", "sha3": "^2.1.1"}}, "node_modules/css-select": {"version": "5.1.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0", "css-what": "^6.1.0", "domhandler": "^5.0.2", "domutils": "^3.0.1", "nth-check": "^2.0.1"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/css-what": {"version": "6.1.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">= 6"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/cssfilter": {"version": "0.0.10", "dev": true, "license": "MIT", "optional": true}, "node_modules/d": {"version": "1.0.2", "license": "ISC", "dependencies": {"es5-ext": "^0.10.64", "type": "^2.7.2"}, "engines": {"node": ">=0.12"}}, "node_modules/dashdash": {"version": "1.14.1", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/data-uri-to-buffer": {"version": "4.0.1", "license": "MIT", "engines": {"node": ">= 12"}}, "node_modules/dataloader": {"version": "2.1.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/debounce-fn": {"version": "4.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"mimic-fn": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/debounce-fn/node_modules/mimic-fn": {"version": "3.1.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=8"}}, "node_modules/debug": {"version": "4.4.1", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decamelize": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/decode-uri-component": {"version": "0.2.2", "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/decompress-response": {"version": "6.0.0", "license": "MIT", "dependencies": {"mimic-response": "^3.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/decompress-response/node_modules/mimic-response": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/dedent": {"version": "1.6.0", "dev": true, "license": "MIT", "peerDependencies": {"babel-plugin-macros": "^3.1.0"}, "peerDependenciesMeta": {"babel-plugin-macros": {"optional": true}}}, "node_modules/deepmerge": {"version": "4.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/defer-to-connect": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/deferred-leveldown": {"version": "5.3.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"abstract-leveldown": "~6.2.1", "inherits": "^2.0.3"}, "engines": {"node": ">=6"}}, "node_modules/deferred-leveldown/node_modules/abstract-leveldown": {"version": "6.2.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"buffer": "^5.5.0", "immediate": "^3.2.3", "level-concat-iterator": "~2.0.0", "level-supports": "~1.0.0", "xtend": "~4.0.0"}, "engines": {"node": ">=6"}}, "node_modules/deferred-leveldown/node_modules/level-concat-iterator": {"version": "2.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/deferred-leveldown/node_modules/level-supports": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"xtend": "^4.0.2"}, "engines": {"node": ">=6"}}, "node_modules/define-data-property": {"version": "1.1.4", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/delay": {"version": "5.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/delayed-stream": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/depd": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/destroy": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/detect-indent": {"version": "5.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/detect-newline": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/diff": {"version": "5.0.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.3.1"}}, "node_modules/diff-sequences": {"version": "29.6.3", "dev": true, "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/dom-serializer": {"version": "2.0.0", "license": "MIT", "dependencies": {"domelementtype": "^2.3.0", "domhandler": "^5.0.2", "entities": "^4.2.0"}, "funding": {"url": "https://github.com/cheeriojs/dom-serializer?sponsor=1"}}, "node_modules/dom-walk": {"version": "0.1.2"}, "node_modules/domelementtype": {"version": "2.3.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/domhandler": {"version": "5.0.3", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"domelementtype": "^2.3.0"}, "engines": {"node": ">= 4"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}}, "node_modules/domutils": {"version": "3.2.2", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dom-serializer": "^2.0.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "node_modules/dot-case": {"version": "2.1.1", "license": "MIT", "dependencies": {"no-case": "^2.2.0"}}, "node_modules/dot-prop": {"version": "6.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"is-obj": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/dotenv": {"version": "16.5.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/double-ended-queue": {"version": "2.1.0-0", "dev": true, "license": "MIT", "optional": true}, "node_modules/dunder-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/eastasianwidth": {"version": "0.2.0", "license": "MIT"}, "node_modules/ecc-jsbn": {"version": "0.1.2", "license": "MIT", "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "node_modules/ecc-jsbn/node_modules/jsbn": {"version": "0.1.1", "license": "MIT"}, "node_modules/ecdsa-sig-formatter": {"version": "1.0.11", "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}}, "node_modules/ee-first": {"version": "1.1.1", "license": "MIT"}, "node_modules/electron-to-chromium": {"version": "1.5.173", "dev": true, "license": "ISC"}, "node_modules/elliptic": {"version": "6.5.4", "license": "MIT", "dependencies": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.1", "inherits": "^2.0.4", "minimalistic-assert": "^1.0.1", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/emittery": {"version": "0.13.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sindresorhus/emittery?sponsor=1"}}, "node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "node_modules/encodeurl": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/encoding-down": {"version": "6.3.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"abstract-leveldown": "^6.2.1", "inherits": "^2.0.3", "level-codec": "^9.0.0", "level-errors": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/encoding-down/node_modules/abstract-leveldown": {"version": "6.3.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"buffer": "^5.5.0", "immediate": "^3.2.3", "level-concat-iterator": "~2.0.0", "level-supports": "~1.0.0", "xtend": "~4.0.0"}, "engines": {"node": ">=6"}}, "node_modules/encoding-down/node_modules/level-concat-iterator": {"version": "2.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/encoding-down/node_modules/level-supports": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"xtend": "^4.0.2"}, "engines": {"node": ">=6"}}, "node_modules/encoding-sniffer": {"version": "0.2.1", "license": "MIT", "dependencies": {"iconv-lite": "^0.6.3", "whatwg-encoding": "^3.1.1"}, "funding": {"url": "https://github.com/fb55/encoding-sniffer?sponsor=1"}}, "node_modules/end-of-stream": {"version": "1.4.5", "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/end-stream": {"version": "0.1.0", "dev": true, "optional": true, "dependencies": {"write-stream": "~0.4.3"}}, "node_modules/entities": {"version": "4.5.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/env-paths": {"version": "2.2.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/errno": {"version": "0.1.8", "dev": true, "license": "MIT", "optional": true, "dependencies": {"prr": "~1.0.1"}, "bin": {"errno": "cli.js"}}, "node_modules/error-ex": {"version": "1.3.2", "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/es-define-property": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/es5-ext": {"version": "0.10.64", "hasInstallScript": true, "license": "ISC", "dependencies": {"es6-iterator": "^2.0.3", "es6-symbol": "^3.1.3", "esniff": "^2.0.1", "next-tick": "^1.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/es6-iterator": {"version": "2.0.3", "license": "MIT", "dependencies": {"d": "1", "es5-ext": "^0.10.35", "es6-symbol": "^3.1.1"}}, "node_modules/es6-promise": {"version": "4.2.8", "license": "MIT"}, "node_modules/es6-symbol": {"version": "3.1.4", "license": "ISC", "dependencies": {"d": "^1.0.2", "ext": "^1.7.0"}, "engines": {"node": ">=0.12"}}, "node_modules/escalade": {"version": "3.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/esniff": {"version": "2.0.1", "license": "ISC", "dependencies": {"d": "^1.0.1", "es5-ext": "^0.10.62", "event-emitter": "^0.3.5", "type": "^2.7.2"}, "engines": {"node": ">=0.10"}}, "node_modules/esprima": {"version": "4.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/etag": {"version": "1.8.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/eth-ens-namehash": {"version": "2.0.8", "license": "ISC", "dependencies": {"idna-uts46-hx": "^2.3.1", "js-sha3": "^0.5.7"}}, "node_modules/eth-ens-namehash/node_modules/js-sha3": {"version": "0.5.7", "license": "MIT"}, "node_modules/eth-lib": {"version": "0.1.29", "license": "MIT", "dependencies": {"bn.js": "^4.11.6", "elliptic": "^6.4.0", "nano-json-stream-parser": "^0.1.2", "servify": "^0.1.12", "ws": "^3.0.0", "xhr-request-promise": "^0.1.2"}}, "node_modules/eth-lib/node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/eth-lib/node_modules/ws": {"version": "3.3.3", "license": "MIT", "dependencies": {"async-limiter": "~1.0.0", "safe-buffer": "~5.1.0", "ultron": "~1.1.0"}}, "node_modules/ethereum-bloom-filters": {"version": "1.2.0", "license": "MIT", "dependencies": {"@noble/hashes": "^1.4.0"}}, "node_modules/ethereum-cryptography": {"version": "2.2.1", "license": "MIT", "dependencies": {"@noble/curves": "1.4.2", "@noble/hashes": "1.4.0", "@scure/bip32": "1.4.0", "@scure/bip39": "1.3.0"}}, "node_modules/ethereumjs-util": {"version": "7.1.5", "license": "MPL-2.0", "dependencies": {"@types/bn.js": "^5.1.0", "bn.js": "^5.1.2", "create-hash": "^1.1.2", "ethereum-cryptography": "^0.1.3", "rlp": "^2.2.4"}, "engines": {"node": ">=10.0.0"}}, "node_modules/ethereumjs-util/node_modules/bn.js": {"version": "5.2.2", "license": "MIT"}, "node_modules/ethereumjs-util/node_modules/ethereum-cryptography": {"version": "0.1.3", "license": "MIT", "dependencies": {"@types/pbkdf2": "^3.0.0", "@types/secp256k1": "^4.0.1", "blakejs": "^1.1.0", "browserify-aes": "^1.2.0", "bs58check": "^2.1.2", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "hash.js": "^1.1.7", "keccak": "^3.0.0", "pbkdf2": "^3.0.17", "randombytes": "^2.1.0", "safe-buffer": "^5.1.2", "scrypt-js": "^3.0.0", "secp256k1": "^4.0.1", "setimmediate": "^1.0.5"}}, "node_modules/ethereumjs-util/node_modules/hash.js": {"version": "1.1.7", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "node_modules/ethereumjs-util/node_modules/scrypt-js": {"version": "3.0.1", "license": "MIT"}, "node_modules/ethereumjs-util/node_modules/setimmediate": {"version": "1.0.5", "license": "MIT"}, "node_modules/ethers": {"version": "4.0.49", "license": "MIT", "dependencies": {"aes-js": "3.0.0", "bn.js": "^4.11.9", "elliptic": "6.5.4", "hash.js": "1.1.3", "js-sha3": "0.5.7", "scrypt-js": "2.0.4", "setimmediate": "1.0.4", "uuid": "2.0.1", "xmlhttprequest": "1.8.0"}}, "node_modules/ethers/node_modules/js-sha3": {"version": "0.5.7", "license": "MIT"}, "node_modules/ethjs-unit": {"version": "0.1.6", "license": "MIT", "dependencies": {"bn.js": "4.11.6", "number-to-bn": "1.7.0"}, "engines": {"node": ">=6.5.0", "npm": ">=3"}}, "node_modules/ethjs-unit/node_modules/bn.js": {"version": "4.11.6", "license": "MIT"}, "node_modules/event-emitter": {"version": "0.3.5", "license": "MIT", "dependencies": {"d": "1", "es5-ext": "~0.10.14"}}, "node_modules/event-target-shim": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/eventemitter3": {"version": "4.0.4", "license": "MIT"}, "node_modules/events": {"version": "3.3.0", "license": "MIT", "engines": {"node": ">=0.8.x"}}, "node_modules/evp_bytestokey": {"version": "1.0.3", "license": "MIT", "dependencies": {"md5.js": "^1.3.4", "safe-buffer": "^5.1.1"}}, "node_modules/execa": {"version": "5.1.1", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/exit": {"version": "0.1.2", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/expect": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/expect-utils": "^29.7.0", "jest-get-type": "^29.6.3", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/express": {"version": "4.21.2", "license": "MIT", "dependencies": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.3", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.7.1", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.3.1", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.3", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.12", "proxy-addr": "~2.0.7", "qs": "6.13.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.19.0", "serve-static": "1.16.2", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/express-rate-limit": {"version": "6.11.2", "resolved": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.11.2.tgz", "integrity": "sha512-a7uwwfNTh1U60ssiIkuLFWHt4hAC5yxlLGU2VP0X4YNlyEDZAqF4tK3GD3NSitVBrCQmQ0++0uOyFOgC2y4DDw==", "license": "MIT", "engines": {"node": ">= 14"}, "peerDependencies": {"express": "^4 || ^5"}}, "node_modules/express/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/express/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/ext": {"version": "1.7.0", "license": "ISC", "dependencies": {"type": "^2.7.2"}}, "node_modules/extend": {"version": "3.0.2", "license": "MIT"}, "node_modules/extsprintf": {"version": "1.3.0", "engines": ["node >=0.6.0"], "license": "MIT"}, "node_modules/fast-check": {"version": "3.1.1", "license": "MIT", "dependencies": {"pure-rand": "^5.0.1"}, "engines": {"node": ">=8.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/fast-check"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "license": "MIT"}, "node_modules/fast-fifo": {"version": "1.3.2", "license": "MIT"}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "license": "MIT"}, "node_modules/fast-uri": {"version": "3.0.6", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/fb-watchman": {"version": "2.0.2", "dev": true, "license": "Apache-2.0", "dependencies": {"bser": "2.1.1"}}, "node_modules/fetch-blob": {"version": "3.2.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "license": "MIT", "dependencies": {"node-domexception": "^1.0.0", "web-streams-polyfill": "^3.0.3"}, "engines": {"node": "^12.20 || >= 14.13"}}, "node_modules/fetch-cookie": {"version": "0.11.0", "dev": true, "license": "Unlicense", "optional": true, "dependencies": {"tough-cookie": "^2.3.3 || ^3.0.1 || ^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/fill-range": {"version": "7.1.1", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "1.3.1", "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/finalhandler/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/finalhandler/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/find-up": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/flat": {"version": "5.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "bin": {"flat": "cli.js"}}, "node_modules/follow-redirects": {"version": "1.15.9", "dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "optional": true, "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/for-each": {"version": "0.3.5", "license": "MIT", "dependencies": {"is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/foreach": {"version": "2.0.6", "dev": true, "license": "MIT"}, "node_modules/foreground-child": {"version": "3.3.1", "license": "ISC", "dependencies": {"cross-spawn": "^7.0.6", "signal-exit": "^4.0.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/foreground-child/node_modules/signal-exit": {"version": "4.1.0", "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/forever-agent": {"version": "0.6.1", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/form-data": {"version": "4.0.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "hasown": "^2.0.2", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/form-data-encoder": {"version": "1.7.1", "license": "MIT"}, "node_modules/formdata-polyfill": {"version": "4.0.10", "license": "MIT", "dependencies": {"fetch-blob": "^3.1.2"}, "engines": {"node": ">=12.20.0"}}, "node_modules/forwarded": {"version": "0.2.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fresh": {"version": "0.5.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fs-extra": {"version": "0.30.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "jsonfile": "^2.1.0", "klaw": "^1.0.0", "path-is-absolute": "^1.0.0", "rimraf": "^2.2.8"}}, "node_modules/fs-minipass": {"version": "1.2.7", "license": "ISC", "dependencies": {"minipass": "^2.6.0"}}, "node_modules/fs.realpath": {"version": "1.0.0", "license": "ISC"}, "node_modules/function-bind": {"version": "1.1.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/functional-red-black-tree": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/ganache": {"version": "7.9.1", "bundleDependencies": ["@trufflesuite/bigint-buffer", "keccak", "leveldown", "secp256k1"], "dev": true, "license": "MIT", "dependencies": {"@trufflesuite/bigint-buffer": "1.1.10", "@trufflesuite/uws-js-unofficial": "20.30.0-unofficial.0", "@types/bn.js": "^5.1.0", "@types/lru-cache": "5.1.1", "@types/seedrandom": "3.0.1", "abstract-level": "1.0.3", "abstract-leveldown": "7.2.0", "async-eventemitter": "0.2.4", "emittery": "0.10.0", "keccak": "3.0.2", "leveldown": "6.1.0", "secp256k1": "4.0.3"}, "bin": {"ganache": "dist/node/cli.js", "ganache-cli": "dist/node/cli.js"}, "optionalDependencies": {"bufferutil": "4.0.5", "utf-8-validate": "5.0.7"}}, "node_modules/ganache-cli": {"version": "6.12.2", "resolved": "https://registry.npmjs.org/ganache-cli/-/ganache-cli-6.12.2.tgz", "integrity": "sha512-bnmwnJDBDsOWBUP8E/BExWf85TsdDEFelQSzihSJm9VChVO1SHp94YXLP5BlA4j/OTxp0wR4R1Tje9OHOuAJVw==", "bundleDependencies": ["source-map-support", "yargs", "ethereumjs-util"], "deprecated": "ganache-cli is now ganache; visit https://trfl.io/g7 for details", "dev": true, "license": "MIT", "dependencies": {"ethereumjs-util": "6.2.1", "source-map-support": "0.5.12", "yargs": "13.2.4"}, "bin": {"ganache-cli": "cli.js"}}, "node_modules/ganache-cli/node_modules/@types/bn.js": {"version": "4.11.6", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/ganache-cli/node_modules/@types/node": {"version": "14.11.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/ganache-cli/node_modules/@types/pbkdf2": {"version": "3.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/ganache-cli/node_modules/@types/secp256k1": {"version": "4.0.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/ganache-cli/node_modules/ansi-regex": {"version": "4.1.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ganache-cli/node_modules/ansi-styles": {"version": "3.2.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/ganache-cli/node_modules/base-x": {"version": "3.0.8", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"safe-buffer": "^5.0.1"}}, "node_modules/ganache-cli/node_modules/blakejs": {"version": "1.1.0", "dev": true, "inBundle": true, "license": "CC0-1.0"}, "node_modules/ganache-cli/node_modules/bn.js": {"version": "4.11.9", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/ganache-cli/node_modules/brorand": {"version": "1.1.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/ganache-cli/node_modules/browserify-aes": {"version": "1.2.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"buffer-xor": "^1.0.3", "cipher-base": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.3", "inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "node_modules/ganache-cli/node_modules/bs58": {"version": "4.0.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"base-x": "^3.0.2"}}, "node_modules/ganache-cli/node_modules/bs58check": {"version": "2.1.2", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"bs58": "^4.0.0", "create-hash": "^1.1.0", "safe-buffer": "^5.1.2"}}, "node_modules/ganache-cli/node_modules/buffer-from": {"version": "1.1.1", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/ganache-cli/node_modules/buffer-xor": {"version": "1.0.3", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/ganache-cli/node_modules/camelcase": {"version": "5.3.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ganache-cli/node_modules/cipher-base": {"version": "1.0.4", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "node_modules/ganache-cli/node_modules/cliui": {"version": "5.0.0", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"string-width": "^3.1.0", "strip-ansi": "^5.2.0", "wrap-ansi": "^5.1.0"}}, "node_modules/ganache-cli/node_modules/color-convert": {"version": "1.9.3", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/ganache-cli/node_modules/color-name": {"version": "1.1.3", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/ganache-cli/node_modules/create-hash": {"version": "1.2.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "md5.js": "^1.3.4", "ripemd160": "^2.0.1", "sha.js": "^2.4.0"}}, "node_modules/ganache-cli/node_modules/create-hmac": {"version": "1.1.7", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"cipher-base": "^1.0.3", "create-hash": "^1.1.0", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "node_modules/ganache-cli/node_modules/cross-spawn": {"version": "6.0.5", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "engines": {"node": ">=4.8"}}, "node_modules/ganache-cli/node_modules/decamelize": {"version": "1.2.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/ganache-cli/node_modules/elliptic": {"version": "6.5.3", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"bn.js": "^4.4.0", "brorand": "^1.0.1", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.0"}}, "node_modules/ganache-cli/node_modules/emoji-regex": {"version": "7.0.3", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/ganache-cli/node_modules/end-of-stream": {"version": "1.4.4", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/ganache-cli/node_modules/ethereum-cryptography": {"version": "0.1.3", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"@types/pbkdf2": "^3.0.0", "@types/secp256k1": "^4.0.1", "blakejs": "^1.1.0", "browserify-aes": "^1.2.0", "bs58check": "^2.1.2", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "hash.js": "^1.1.7", "keccak": "^3.0.0", "pbkdf2": "^3.0.17", "randombytes": "^2.1.0", "safe-buffer": "^5.1.2", "scrypt-js": "^3.0.0", "secp256k1": "^4.0.1", "setimmediate": "^1.0.5"}}, "node_modules/ganache-cli/node_modules/ethereumjs-util": {"version": "6.2.1", "dev": true, "inBundle": true, "license": "MPL-2.0", "dependencies": {"@types/bn.js": "^4.11.3", "bn.js": "^4.11.0", "create-hash": "^1.1.2", "elliptic": "^6.5.2", "ethereum-cryptography": "^0.1.3", "ethjs-util": "0.1.6", "rlp": "^2.2.3"}}, "node_modules/ganache-cli/node_modules/ethjs-util": {"version": "0.1.6", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"is-hex-prefixed": "1.0.0", "strip-hex-prefix": "1.0.0"}, "engines": {"node": ">=6.5.0", "npm": ">=3"}}, "node_modules/ganache-cli/node_modules/evp_bytestokey": {"version": "1.0.3", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"md5.js": "^1.3.4", "safe-buffer": "^5.1.1"}}, "node_modules/ganache-cli/node_modules/execa": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/ganache-cli/node_modules/find-up": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"locate-path": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/ganache-cli/node_modules/get-caller-file": {"version": "2.0.5", "dev": true, "inBundle": true, "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/ganache-cli/node_modules/get-stream": {"version": "4.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/ganache-cli/node_modules/hash-base": {"version": "3.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.4", "readable-stream": "^3.6.0", "safe-buffer": "^5.2.0"}, "engines": {"node": ">=4"}}, "node_modules/ganache-cli/node_modules/hash.js": {"version": "1.1.7", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "node_modules/ganache-cli/node_modules/hmac-drbg": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/ganache-cli/node_modules/inherits": {"version": "2.0.4", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/ganache-cli/node_modules/invert-kv": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ganache-cli/node_modules/is-fullwidth-code-point": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ganache-cli/node_modules/is-hex-prefixed": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=6.5.0", "npm": ">=3"}}, "node_modules/ganache-cli/node_modules/is-stream": {"version": "1.1.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/ganache-cli/node_modules/isexe": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/ganache-cli/node_modules/keccak": {"version": "3.0.1", "dev": true, "hasInstallScript": true, "inBundle": true, "license": "MIT", "dependencies": {"node-addon-api": "^2.0.0", "node-gyp-build": "^4.2.0"}, "engines": {"node": ">=10.0.0"}}, "node_modules/ganache-cli/node_modules/lcid": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"invert-kv": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/ganache-cli/node_modules/locate-path": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/ganache-cli/node_modules/map-age-cleaner": {"version": "0.1.3", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"p-defer": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/ganache-cli/node_modules/md5.js": {"version": "1.3.5", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"hash-base": "^3.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "node_modules/ganache-cli/node_modules/mem": {"version": "4.3.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"map-age-cleaner": "^0.1.1", "mimic-fn": "^2.0.0", "p-is-promise": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/ganache-cli/node_modules/mimic-fn": {"version": "2.1.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ganache-cli/node_modules/minimalistic-assert": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/ganache-cli/node_modules/minimalistic-crypto-utils": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/ganache-cli/node_modules/nice-try": {"version": "1.0.5", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/ganache-cli/node_modules/node-addon-api": {"version": "2.0.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/ganache-cli/node_modules/node-gyp-build": {"version": "4.2.3", "dev": true, "inBundle": true, "license": "MIT", "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}}, "node_modules/ganache-cli/node_modules/npm-run-path": {"version": "2.0.2", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"path-key": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/ganache-cli/node_modules/once": {"version": "1.4.0", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/ganache-cli/node_modules/os-locale": {"version": "3.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"execa": "^1.0.0", "lcid": "^2.0.0", "mem": "^4.0.0"}, "engines": {"node": ">=6"}}, "node_modules/ganache-cli/node_modules/p-defer": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ganache-cli/node_modules/p-finally": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ganache-cli/node_modules/p-is-promise": {"version": "2.1.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ganache-cli/node_modules/p-limit": {"version": "2.3.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ganache-cli/node_modules/p-locate": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"p-limit": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/ganache-cli/node_modules/p-try": {"version": "2.2.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ganache-cli/node_modules/path-exists": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ganache-cli/node_modules/path-key": {"version": "2.0.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ganache-cli/node_modules/pbkdf2": {"version": "3.1.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"create-hash": "^1.1.2", "create-hmac": "^1.1.4", "ripemd160": "^2.0.1", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}, "engines": {"node": ">=0.12"}}, "node_modules/ganache-cli/node_modules/pump": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/ganache-cli/node_modules/randombytes": {"version": "2.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"safe-buffer": "^5.1.0"}}, "node_modules/ganache-cli/node_modules/readable-stream": {"version": "3.6.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/ganache-cli/node_modules/require-directory": {"version": "2.1.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/ganache-cli/node_modules/require-main-filename": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/ganache-cli/node_modules/ripemd160": {"version": "2.0.2", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"hash-base": "^3.0.0", "inherits": "^2.0.1"}}, "node_modules/ganache-cli/node_modules/rlp": {"version": "2.2.6", "dev": true, "inBundle": true, "license": "MPL-2.0", "dependencies": {"bn.js": "^4.11.1"}, "bin": {"rlp": "bin/rlp"}}, "node_modules/ganache-cli/node_modules/safe-buffer": {"version": "5.2.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "inBundle": true, "license": "MIT"}, "node_modules/ganache-cli/node_modules/scrypt-js": {"version": "3.0.1", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/ganache-cli/node_modules/secp256k1": {"version": "4.0.2", "dev": true, "hasInstallScript": true, "inBundle": true, "license": "MIT", "dependencies": {"elliptic": "^6.5.2", "node-addon-api": "^2.0.0", "node-gyp-build": "^4.2.0"}, "engines": {"node": ">=10.0.0"}}, "node_modules/ganache-cli/node_modules/semver": {"version": "5.7.1", "dev": true, "inBundle": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/ganache-cli/node_modules/set-blocking": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/ganache-cli/node_modules/setimmediate": {"version": "1.0.5", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/ganache-cli/node_modules/sha.js": {"version": "2.4.11", "dev": true, "inBundle": true, "license": "(MIT AND BSD-3-<PERSON><PERSON>)", "dependencies": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}, "bin": {"sha.js": "bin.js"}}, "node_modules/ganache-cli/node_modules/shebang-command": {"version": "1.2.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"shebang-regex": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ganache-cli/node_modules/shebang-regex": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/ganache-cli/node_modules/signal-exit": {"version": "3.0.3", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/ganache-cli/node_modules/source-map": {"version": "0.6.1", "dev": true, "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/ganache-cli/node_modules/source-map-support": {"version": "0.5.12", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/ganache-cli/node_modules/string_decoder": {"version": "1.3.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/ganache-cli/node_modules/string-width": {"version": "3.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"emoji-regex": "^7.0.1", "is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^5.1.0"}, "engines": {"node": ">=6"}}, "node_modules/ganache-cli/node_modules/strip-ansi": {"version": "5.2.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"ansi-regex": "^4.1.0"}, "engines": {"node": ">=6"}}, "node_modules/ganache-cli/node_modules/strip-eof": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/ganache-cli/node_modules/strip-hex-prefix": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"is-hex-prefixed": "1.0.0"}, "engines": {"node": ">=6.5.0", "npm": ">=3"}}, "node_modules/ganache-cli/node_modules/util-deprecate": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/ganache-cli/node_modules/which": {"version": "1.3.1", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/ganache-cli/node_modules/which-module": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/ganache-cli/node_modules/wrap-ansi": {"version": "5.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.0", "string-width": "^3.0.0", "strip-ansi": "^5.0.0"}, "engines": {"node": ">=6"}}, "node_modules/ganache-cli/node_modules/wrappy": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/ganache-cli/node_modules/y18n": {"version": "4.0.0", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/ganache-cli/node_modules/yargs": {"version": "13.2.4", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"cliui": "^5.0.0", "find-up": "^3.0.0", "get-caller-file": "^2.0.1", "os-locale": "^3.1.0", "require-directory": "^2.1.1", "require-main-filename": "^2.0.0", "set-blocking": "^2.0.0", "string-width": "^3.0.0", "which-module": "^2.0.0", "y18n": "^4.0.0", "yargs-parser": "^13.1.0"}}, "node_modules/ganache-cli/node_modules/yargs-parser": {"version": "13.1.2", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}}, "node_modules/ganache/node_modules/@trufflesuite/bigint-buffer": {"version": "1.1.10", "resolved": "https://registry.npmjs.org/@trufflesuite/bigint-buffer/-/bigint-buffer-1.1.10.tgz", "integrity": "sha512-pYIQC5EcMmID74t26GCC67946mgTJFiLXOT/BYozgrd4UEY2JHEGLhWi9cMiQCt5BSqFEvKkCHNnoj82SRjiEw==", "dev": true, "hasInstallScript": true, "inBundle": true, "license": "Apache-2.0", "dependencies": {"node-gyp-build": "4.4.0"}, "engines": {"node": ">= 14.0.0"}}, "node_modules/ganache/node_modules/@trufflesuite/bigint-buffer/node_modules/node-gyp-build": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/node-gyp-build/-/node-gyp-build-4.4.0.tgz", "integrity": "sha512-amJnQCcgtRVw9SvoebO3BKGESClrfXGCUTX9hSn1OuGQTQBOZmVd0Z0OlecpuRksKvbsUqALE8jls/ErClAPuQ==", "dev": true, "inBundle": true, "license": "MIT", "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}}, "node_modules/ganache/node_modules/@trufflesuite/uws-js-unofficial": {"version": "20.30.0-unofficial.0", "resolved": "https://registry.npmjs.org/@trufflesuite/uws-js-unofficial/-/uws-js-unofficial-20.30.0-unofficial.0.tgz", "integrity": "sha512-r5X0aOQcuT6pLwTRLD+mPnAM/nlKtvIK4Z+My++A8tTOR0qTjNRx8UB8jzRj3D+p9PMAp5LnpCUUGmz7/TppwA==", "dev": true, "license": "Apache-2.0", "dependencies": {"ws": "8.13.0"}, "optionalDependencies": {"bufferutil": "4.0.7", "utf-8-validate": "6.0.3"}}, "node_modules/ganache/node_modules/@trufflesuite/uws-js-unofficial/node_modules/bufferutil": {"version": "4.0.7", "resolved": "https://registry.npmjs.org/bufferutil/-/bufferutil-4.0.7.tgz", "integrity": "sha512-kukuqc39WOHtdxtw4UScxF/WVnMFVSQVKhtx3AjZJzhd0RGZZldcrfSEbVsWWe6KNH253574cq5F+wpv0G9pJw==", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"node-gyp-build": "^4.3.0"}, "engines": {"node": ">=6.14.2"}}, "node_modules/ganache/node_modules/@trufflesuite/uws-js-unofficial/node_modules/utf-8-validate": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-6.0.3.tgz", "integrity": "sha512-uIuGf9TWQ/y+0Lp+KGZCMuJWc3N9BHA+l/UmHd/oUHwJJDeysyTRxNQVkbzsIWfGFbRe3OcgML/i0mvVRPOyDA==", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"node-gyp-build": "^4.3.0"}, "engines": {"node": ">=6.14.2"}}, "node_modules/ganache/node_modules/@trufflesuite/uws-js-unofficial/node_modules/ws": {"version": "8.13.0", "resolved": "https://registry.npmjs.org/ws/-/ws-8.13.0.tgz", "integrity": "sha512-x9vcZYTrFPC7aSIbj7sRCYo7L/Xb8Iy+pW0ng0wt2vCJv7M9HOMy0UoN3rr+IFC7hb7vXoqS+P9ktyLLLhO+LA==", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/ganache/node_modules/@types/bn.js": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/@types/bn.js/-/bn.js-5.1.0.tgz", "integrity": "sha512-QSSVYj7pYFN49kW77o2s9xTCwZ8F2xLbjLLSEVh8D2F4JUhZtPAGOFLTD+ffqksBx/u4cE/KImFjyhqCjn/LIA==", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/ganache/node_modules/@types/lru-cache": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@types/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha512-ssE3Vlrys7sdIzs5LOxCzTVMsU7i9oa/IaW92wF32JFb3CVczqOkru2xspuKczHEbG3nvmPY7IFqVmGGHdNbYw==", "dev": true, "license": "MIT"}, "node_modules/ganache/node_modules/@types/node": {"version": "17.0.0", "resolved": "https://registry.npmjs.org/@types/node/-/node-17.0.0.tgz", "integrity": "sha512-eMhwJXc931Ihh4tkU+Y7GiLzT/y/DBNpNtr4yU9O2w3SYBsr9NaOPhQlLKRmoWtI54uNwuo0IOUFQjVOTZYRvw==", "dev": true, "license": "MIT"}, "node_modules/ganache/node_modules/@types/seedrandom": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/@types/seedrandom/-/seedrandom-3.0.1.tgz", "integrity": "sha512-giB9gzDeiCeloIXDgzFBCgjj1k4WxcDrZtGl6h1IqmUPlxF+Nx8Ve+96QCyDZ/HseB/uvDsKbpib9hU5cU53pw==", "dev": true, "license": "MIT"}, "node_modules/ganache/node_modules/abstract-level": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/abstract-level/-/abstract-level-1.0.3.tgz", "integrity": "sha512-t6jv+xHy+VYwc4xqZMn2Pa9DjcdzvzZmQGRjTFc8spIbRGHgBrEKbPq+rYXc7CCo0lxgYvSgKVg9qZAhpVQSjA==", "dev": true, "license": "MIT", "dependencies": {"buffer": "^6.0.3", "catering": "^2.1.0", "is-buffer": "^2.0.5", "level-supports": "^4.0.0", "level-transcoder": "^1.0.1", "module-error": "^1.0.1", "queue-microtask": "^1.2.3"}, "engines": {"node": ">=12"}}, "node_modules/ganache/node_modules/abstract-level/node_modules/level-supports": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/level-supports/-/level-supports-4.0.1.tgz", "integrity": "sha512-PbXpve8rKeNcZ9C1mUicC9auIYFyGpkV9/i6g76tLgANwWhtG2v7I4xNBUlkn3lE2/dZF3Pi0ygYGtLc4RXXdA==", "dev": true, "license": "MIT", "engines": {"node": ">=12"}}, "node_modules/ganache/node_modules/abstract-leveldown": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/abstract-leveldown/-/abstract-leveldown-7.2.0.tgz", "integrity": "sha512-DnhQwcFEaYsvYDnACLZhMmCWd3rkOeEvglpa4q5i/5Jlm3UIsWaxVzuXvDLFCSCWRO3yy2/+V/G7FusFgejnfQ==", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"buffer": "^6.0.3", "catering": "^2.0.0", "is-buffer": "^2.0.5", "level-concat-iterator": "^3.0.0", "level-supports": "^2.0.1", "queue-microtask": "^1.2.3"}, "engines": {"node": ">=10"}}, "node_modules/ganache/node_modules/async": {"version": "2.6.4", "resolved": "https://registry.npmjs.org/async/-/async-2.6.4.tgz", "integrity": "sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA==", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/ganache/node_modules/async-eventemitter": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/async-eventemitter/-/async-eventemitter-0.2.4.tgz", "integrity": "sha512-pd20BwL7Yt1zwDFy+8MX8F1+WCT8aQeKj0kQnTrH9WaeRETlRamVhD0JtRPmrV4GfOJ2F9CvdQkZeZhnh2TuHw==", "dev": true, "license": "MIT", "dependencies": {"async": "^2.4.0"}}, "node_modules/ganache/node_modules/base64-js": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "inBundle": true, "license": "MIT"}, "node_modules/ganache/node_modules/brorand": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/brorand/-/brorand-1.1.0.tgz", "integrity": "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/ganache/node_modules/buffer": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/buffer/-/buffer-6.0.3.tgz", "integrity": "sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "inBundle": true, "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/ganache/node_modules/bufferutil": {"version": "4.0.5", "resolved": "https://registry.npmjs.org/bufferutil/-/bufferutil-4.0.5.tgz", "integrity": "sha512-HTm14iMQKK2FjFLRTM5lAVcyaUzOnqbPtesFIvREgXpJHdQm8bWS+GkQgIkfaBYRHuCnea7w8UVNfwiAQhlr9A==", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"node-gyp-build": "^4.3.0"}, "engines": {"node": ">=6.14.2"}}, "node_modules/ganache/node_modules/catering": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/catering/-/catering-2.1.1.tgz", "integrity": "sha512-K7Qy8O9p76sL3/3m7/zLKbRkyOlSZAgzEaLhyj2mXS8PsCud2Eo4hAb8aLtZqHh0QGqLcb9dlJSu6lHRVENm1w==", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ganache/node_modules/elliptic": {"version": "6.5.4", "resolved": "https://registry.npmjs.org/elliptic/-/elliptic-6.5.4.tgz", "integrity": "sha512-iLhC6ULemrljPZb+QutR5TQGB+pdW6KGD5RSegS+8sorOZT+rdQFbsQFJgvN3eRqNALqJer4oQ16YvJHlU8hzQ==", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.1", "inherits": "^2.0.4", "minimalistic-assert": "^1.0.1", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/ganache/node_modules/elliptic/node_modules/bn.js": {"version": "4.12.0", "resolved": "https://registry.npmjs.org/bn.js/-/bn.js-4.12.0.tgz", "integrity": "sha512-c98Bf3tPniI+scsdk237ku1Dc3ujXQTSgyiPUDEOe7tRkhrqridvh8klBv0HCEso1OLOYcHuCv/cS6DNxKH+ZA==", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/ganache/node_modules/emittery": {"version": "0.10.0", "resolved": "https://registry.npmjs.org/emittery/-/emittery-0.10.0.tgz", "integrity": "sha512-AGvFfs+d0JKCJQ4o01ASQLGPmSCxgfU9RFXvzPvZdjKK8oscynksuJhWrSTSw7j7Ep/sZct5b5ZhYCi8S/t0HQ==", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sindresorhus/emittery?sponsor=1"}}, "node_modules/ganache/node_modules/hash.js": {"version": "1.1.7", "resolved": "https://registry.npmjs.org/hash.js/-/hash.js-1.1.7.tgz", "integrity": "sha512-taOaskGt4z4SOANNseOviYDvjEJinIkRgmp7LbKP2YTTmVxWBl87s/uzK9r+44BclBSp2X7K1hqeNfz9JbBeXA==", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "node_modules/ganache/node_modules/hmac-drbg": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/hmac-drbg/-/hmac-drbg-1.0.1.tgz", "integrity": "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/ganache/node_modules/ieee754": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "inBundle": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ganache/node_modules/inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/ganache/node_modules/is-buffer": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-2.0.5.tgz", "integrity": "sha512-i2R6zNFDwgEHJyQUtJEk0XFi1i0dPFn/oqjK3/vPCcDeJvW5NQ83V8QbicfF1SupOaB0h8ntgBC2YiE7dfyctQ==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ganache/node_modules/keccak": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/keccak/-/keccak-3.0.2.tgz", "integrity": "sha512-PyKKjkH53wDMLGrvmRGSNWgmSxZOUqbnXwKL9tmgbFYA1iAYqW21kfR7mZXV0MlESiefxQQE9X9fTa3X+2MPDQ==", "dev": true, "hasInstallScript": true, "inBundle": true, "license": "MIT", "dependencies": {"node-addon-api": "^2.0.0", "node-gyp-build": "^4.2.0", "readable-stream": "^3.6.0"}, "engines": {"node": ">=10.0.0"}}, "node_modules/ganache/node_modules/level-concat-iterator": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/level-concat-iterator/-/level-concat-iterator-3.1.0.tgz", "integrity": "sha512-BWRCMHBxbIqPxJ8vHOvKUsaO0v1sLYZtjN3K2iZJsRBYtp+ONsY6Jfi6hy9K3+zolgQRryhIn2NRZjZnWJ9NmQ==", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"catering": "^2.1.0"}, "engines": {"node": ">=10"}}, "node_modules/ganache/node_modules/level-supports": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/level-supports/-/level-supports-2.1.0.tgz", "integrity": "sha512-E486g1NCjW5cF78KGPrMDRBYzPuueMZ6VBXHT6gC7A8UYWGiM14fGgp+s/L1oFfDWSPV/+SFkYCmZ0SiESkRKA==", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/ganache/node_modules/level-transcoder": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/level-transcoder/-/level-transcoder-1.0.1.tgz", "integrity": "sha512-t7bFwFtsQeD8cl8NIoQ2iwxA0CL/9IFw7/9gAjOonH0PWTTiRfY7Hq+Ejbsxh86tXobDQ6IOiddjNYIfOBs06w==", "dev": true, "license": "MIT", "dependencies": {"buffer": "^6.0.3", "module-error": "^1.0.1"}, "engines": {"node": ">=12"}}, "node_modules/ganache/node_modules/leveldown": {"version": "6.1.0", "resolved": "https://registry.npmjs.org/leveldown/-/leveldown-6.1.0.tgz", "integrity": "sha512-8C7oJDT44JXxh04aSSsfcMI8YiaGRhOFI9/pMEL7nWJLVsWajDPTRxsSHTM2WcTVY5nXM+SuRHzPPi0GbnDX+w==", "dev": true, "hasInstallScript": true, "inBundle": true, "license": "MIT", "dependencies": {"abstract-leveldown": "^7.2.0", "napi-macros": "~2.0.0", "node-gyp-build": "^4.3.0"}, "engines": {"node": ">=10.12.0"}}, "node_modules/ganache/node_modules/lodash": {"version": "4.17.21", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==", "dev": true, "license": "MIT"}, "node_modules/ganache/node_modules/minimalistic-assert": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "integrity": "sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/ganache/node_modules/minimalistic-crypto-utils": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz", "integrity": "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/ganache/node_modules/module-error": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/module-error/-/module-error-1.0.2.tgz", "integrity": "sha512-0yuvsqSCv8LbaOKhnsQ/T5JhyFlCYLPXK3U2sgV10zoKQwzs/MyfuQUOZQ1V/6OCOJsK/TRgNVrPuPDqtdMFtA==", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/ganache/node_modules/napi-macros": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/napi-macros/-/napi-macros-2.0.0.tgz", "integrity": "sha512-A0xLykHtARfueITVDernsAWdtIMbOJgKgcluwENp3AlsKN/PloyO10HtmoqnFAQAcxPkgZN7wdfPfEd0zNGxbg==", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/ganache/node_modules/node-addon-api": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-2.0.2.tgz", "integrity": "sha512-Ntyt4AIXyaLIuMHF6IOoTakB3K+RWxwtsHNRxllEoA6vPwP9o4866g6YWDLUdnucilZhmkxiHwHr11gAENw+QA==", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/ganache/node_modules/node-gyp-build": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/node-gyp-build/-/node-gyp-build-4.5.0.tgz", "integrity": "sha512-2iGbaQBV+ITgCz76ZEjmhUKAKVf7xfY1sRl4UiKQspfZMH2h06SyhNsnSVy50cwkFQDGLyif6m/6uFXHkOZ6rg==", "dev": true, "inBundle": true, "license": "MIT", "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}}, "node_modules/ganache/node_modules/queue-microtask": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "inBundle": true, "license": "MIT"}, "node_modules/ganache/node_modules/readable-stream": {"version": "3.6.0", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.0.tgz", "integrity": "sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA==", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/ganache/node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "inBundle": true, "license": "MIT"}, "node_modules/ganache/node_modules/secp256k1": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/secp256k1/-/secp256k1-4.0.3.tgz", "integrity": "sha512-NLZVf+ROMxwtEj3Xa562qgv2BK5e2WNmXPiOdVIPLgs6lyTzMvBq0aWTYMI5XCP9jZMVKOcqZLw/Wc4vDkuxhA==", "dev": true, "hasInstallScript": true, "inBundle": true, "license": "MIT", "dependencies": {"elliptic": "^6.5.4", "node-addon-api": "^2.0.0", "node-gyp-build": "^4.2.0"}, "engines": {"node": ">=10.0.0"}}, "node_modules/ganache/node_modules/string_decoder": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/ganache/node_modules/utf-8-validate": {"version": "5.0.7", "resolved": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-5.0.7.tgz", "integrity": "sha512-vLt1O5Pp+flcArHGIyKEQq883nBt8nN8tVBcoL0qUXj2XT1n7p70yGIq2VK98I5FdZ1YHc0wk/koOnHjnXWk1Q==", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"node-gyp-build": "^4.3.0"}, "engines": {"node": ">=6.14.2"}}, "node_modules/ganache/node_modules/util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/gensync": {"version": "1.0.0-beta.2", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/get-caller-file": {"version": "2.0.5", "dev": true, "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-package-type": {"version": "0.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8.0.0"}}, "node_modules/get-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-stream": {"version": "6.0.1", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/getpass": {"version": "0.1.7", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/glob": {"version": "7.2.3", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "5.1.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/global": {"version": "4.4.0", "license": "MIT", "dependencies": {"min-document": "^2.19.0", "process": "^0.11.10"}}, "node_modules/globals": {"version": "11.12.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/gopd": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/got": {"version": "12.1.0", "license": "MIT", "dependencies": {"@sindresorhus/is": "^4.6.0", "@szmarczak/http-timer": "^5.0.1", "@types/cacheable-request": "^6.0.2", "@types/responselike": "^1.0.0", "cacheable-lookup": "^6.0.4", "cacheable-request": "^7.0.2", "decompress-response": "^6.0.0", "form-data-encoder": "1.7.1", "get-stream": "^6.0.1", "http2-wrapper": "^2.1.10", "lowercase-keys": "^3.0.0", "p-cancelable": "^3.0.0", "responselike": "^2.0.0"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sindresorhus/got?sponsor=1"}}, "node_modules/graceful-fs": {"version": "4.2.11", "license": "ISC"}, "node_modules/graphql": {"version": "15.10.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">= 10.x"}}, "node_modules/graphql-tag": {"version": "2.12.6", "dev": true, "license": "MIT", "optional": true, "dependencies": {"tslib": "^2.1.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"graphql": "^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/har-schema": {"version": "2.0.0", "license": "ISC", "engines": {"node": ">=4"}}, "node_modules/har-validator": {"version": "5.1.5", "license": "MIT", "dependencies": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/has-flag": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hash-base": {"version": "3.1.0", "license": "MIT", "dependencies": {"inherits": "^2.0.4", "readable-stream": "^3.6.0", "safe-buffer": "^5.2.0"}, "engines": {"node": ">=4"}}, "node_modules/hash-base/node_modules/readable-stream": {"version": "3.6.2", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/hash.js": {"version": "1.1.3", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.0"}}, "node_modules/hasown": {"version": "2.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/he": {"version": "1.2.0", "dev": true, "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/header-case": {"version": "1.0.1", "license": "MIT", "dependencies": {"no-case": "^2.2.0", "upper-case": "^1.1.3"}}, "node_modules/helmet": {"version": "7.2.0", "license": "MIT", "engines": {"node": ">=16.0.0"}}, "node_modules/highlight.js": {"version": "10.7.3", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": "*"}}, "node_modules/highlightjs-solidity": {"version": "2.0.6", "license": "MIT"}, "node_modules/hmac-drbg": {"version": "1.0.1", "license": "MIT", "dependencies": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/hosted-git-info": {"version": "2.8.9", "license": "ISC"}, "node_modules/html-escaper": {"version": "2.0.2", "dev": true, "license": "MIT"}, "node_modules/htmlparser2": {"version": "10.0.0", "funding": ["https://github.com/fb55/htmlparser2?sponsor=1", {"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "MIT", "dependencies": {"domelementtype": "^2.3.0", "domhandler": "^5.0.3", "domutils": "^3.2.1", "entities": "^6.0.0"}}, "node_modules/htmlparser2/node_modules/entities": {"version": "6.0.1", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/http-cache-semantics": {"version": "4.2.0", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/http-errors": {"version": "2.0.0", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-https": {"version": "1.0.0", "license": "ISC"}, "node_modules/http-signature": {"version": "1.2.0", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "engines": {"node": ">=0.8", "npm": ">=1.3.7"}}, "node_modules/http2-wrapper": {"version": "2.2.1", "license": "MIT", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.2.0"}, "engines": {"node": ">=10.19.0"}}, "node_modules/human-signals": {"version": "2.1.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "node_modules/iconv-lite": {"version": "0.6.3", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/idna-uts46-hx": {"version": "2.3.1", "license": "MIT", "dependencies": {"punycode": "2.1.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/ieee754": {"version": "1.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ignore-by-default": {"version": "1.0.1", "dev": true, "license": "ISC"}, "node_modules/immediate": {"version": "3.3.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/import-local": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/inflight": {"version": "1.0.6", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "license": "ISC"}, "node_modules/invert-kv": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/ip-address": {"version": "9.0.5", "resolved": "https://registry.npmjs.org/ip-address/-/ip-address-9.0.5.tgz", "integrity": "sha512-zHtQzGojZXTwZTHQqra+ETKd4Sn3vgi7uBmlPoXVWZqYvuKmtI0l/VZTjqGmJY9x88GGOaZ9+G9ES8hC4T4X8g==", "license": "MIT", "dependencies": {"jsbn": "1.1.0", "sprintf-js": "^1.1.3"}, "engines": {"node": ">= 12"}}, "node_modules/ip-address/node_modules/sprintf-js": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.1.3.tgz", "integrity": "sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ipaddr.js": {"version": "1.9.1", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-arguments": {"version": "1.2.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-arrayish": {"version": "0.2.1", "license": "MIT"}, "node_modules/is-binary-path": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-buffer": {"version": "2.0.5", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/is-callable": {"version": "1.2.7", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-core-module": {"version": "2.16.1", "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-extglob": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-function": {"version": "1.0.2", "license": "MIT"}, "node_modules/is-generator-fn": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/is-generator-function": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-proto": "^1.0.0", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-glob": {"version": "4.0.3", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-hex-prefixed": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=6.5.0", "npm": ">=3"}}, "node_modules/is-lower-case": {"version": "1.1.3", "license": "MIT", "dependencies": {"lower-case": "^1.1.0"}}, "node_modules/is-number": {"version": "7.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-obj": {"version": "2.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=8"}}, "node_modules/is-plain-obj": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-regex": {"version": "1.2.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-stream": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-typed-array": {"version": "1.1.15", "license": "MIT", "dependencies": {"which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typedarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/is-unicode-supported": {"version": "0.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-upper-case": {"version": "1.1.2", "license": "MIT", "dependencies": {"upper-case": "^1.1.0"}}, "node_modules/is-utf8": {"version": "0.2.1", "license": "MIT"}, "node_modules/isarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "license": "ISC"}, "node_modules/isomorphic-ws": {"version": "4.0.1", "dev": true, "license": "MIT", "optional": true, "peerDependencies": {"ws": "*"}}, "node_modules/isstream": {"version": "0.1.2", "license": "MIT"}, "node_modules/istanbul-lib-coverage": {"version": "3.2.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-instrument": {"version": "6.0.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/core": "^7.23.9", "@babel/parser": "^7.23.9", "@istanbuljs/schema": "^0.1.3", "istanbul-lib-coverage": "^3.2.0", "semver": "^7.5.4"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-lib-report": {"version": "3.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^4.0.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-lib-report/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-report/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-source-maps": {"version": "4.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "source-map": "^0.6.1"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-reports": {"version": "3.1.7", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jackspeak": {"version": "3.4.3", "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/jest": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/core": "^29.7.0", "@jest/types": "^29.6.3", "import-local": "^3.0.2", "jest-cli": "^29.7.0"}, "bin": {"jest": "bin/jest.js"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/jest-changed-files": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"execa": "^5.0.0", "jest-util": "^29.7.0", "p-limit": "^3.1.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-circus": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/environment": "^29.7.0", "@jest/expect": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "co": "^4.6.0", "dedent": "^1.0.0", "is-generator-fn": "^2.0.0", "jest-each": "^29.7.0", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-runtime": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "p-limit": "^3.1.0", "pretty-format": "^29.7.0", "pure-rand": "^6.0.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-circus/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/jest-circus/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-circus/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/jest-circus/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/jest-circus/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-circus/node_modules/pure-rand": {"version": "6.1.0", "dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/dubzzz"}, {"type": "opencollective", "url": "https://opencollective.com/fast-check"}], "license": "MIT"}, "node_modules/jest-circus/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest-cli": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/core": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "chalk": "^4.0.0", "create-jest": "^29.7.0", "exit": "^0.1.2", "import-local": "^3.0.2", "jest-config": "^29.7.0", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "yargs": "^17.3.1"}, "bin": {"jest": "bin/jest.js"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/jest-cli/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/jest-cli/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-cli/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/jest-cli/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/jest-cli/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-cli/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest-config": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.11.6", "@jest/test-sequencer": "^29.7.0", "@jest/types": "^29.6.3", "babel-jest": "^29.7.0", "chalk": "^4.0.0", "ci-info": "^3.2.0", "deepmerge": "^4.2.2", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "jest-circus": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-get-type": "^29.6.3", "jest-regex-util": "^29.6.3", "jest-resolve": "^29.7.0", "jest-runner": "^29.7.0", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "micromatch": "^4.0.4", "parse-json": "^5.2.0", "pretty-format": "^29.7.0", "slash": "^3.0.0", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@types/node": "*", "ts-node": ">=9.0.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "ts-node": {"optional": true}}}, "node_modules/jest-config/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/jest-config/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-config/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/jest-config/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/jest-config/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-config/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest-diff": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.0.0", "diff-sequences": "^29.6.3", "jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-diff/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/jest-diff/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-diff/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/jest-diff/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/jest-diff/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-diff/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest-docblock": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"detect-newline": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-each": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "chalk": "^4.0.0", "jest-get-type": "^29.6.3", "jest-util": "^29.7.0", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-each/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/jest-each/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-each/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/jest-each/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/jest-each/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-each/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest-environment-node": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/environment": "^29.7.0", "@jest/fake-timers": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-mock": "^29.7.0", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-get-type": {"version": "29.6.3", "dev": true, "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-haste-map": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/graceful-fs": "^4.1.3", "@types/node": "*", "anymatch": "^3.0.3", "fb-watchman": "^2.0.0", "graceful-fs": "^4.2.9", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "jest-worker": "^29.7.0", "micromatch": "^4.0.4", "walker": "^1.0.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "optionalDependencies": {"fsevents": "^2.3.2"}}, "node_modules/jest-haste-map/node_modules/fsevents": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/jest-leak-detector": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-matcher-utils": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.0.0", "jest-diff": "^29.7.0", "jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-matcher-utils/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/jest-matcher-utils/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-matcher-utils/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/jest-matcher-utils/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/jest-matcher-utils/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-matcher-utils/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest-message-util": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.12.13", "@jest/types": "^29.6.3", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.4", "pretty-format": "^29.7.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-message-util/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/jest-message-util/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-message-util/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/jest-message-util/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/jest-message-util/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-message-util/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest-mock": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-pnp-resolver": {"version": "1.2.3", "dev": true, "license": "MIT", "engines": {"node": ">=6"}, "peerDependencies": {"jest-resolve": "*"}, "peerDependenciesMeta": {"jest-resolve": {"optional": true}}}, "node_modules/jest-regex-util": {"version": "29.6.3", "dev": true, "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-resolve": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-pnp-resolver": "^1.2.2", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "resolve": "^1.20.0", "resolve.exports": "^2.0.0", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-resolve-dependencies": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"jest-regex-util": "^29.6.3", "jest-snapshot": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-resolve/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/jest-resolve/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-resolve/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/jest-resolve/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/jest-resolve/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-resolve/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest-runner": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/console": "^29.7.0", "@jest/environment": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "emittery": "^0.13.1", "graceful-fs": "^4.2.9", "jest-docblock": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-haste-map": "^29.7.0", "jest-leak-detector": "^29.7.0", "jest-message-util": "^29.7.0", "jest-resolve": "^29.7.0", "jest-runtime": "^29.7.0", "jest-util": "^29.7.0", "jest-watcher": "^29.7.0", "jest-worker": "^29.7.0", "p-limit": "^3.1.0", "source-map-support": "0.5.13"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-runner/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/jest-runner/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-runner/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/jest-runner/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/jest-runner/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-runner/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest-runtime": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/environment": "^29.7.0", "@jest/fake-timers": "^29.7.0", "@jest/globals": "^29.7.0", "@jest/source-map": "^29.6.3", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "cjs-module-lexer": "^1.0.0", "collect-v8-coverage": "^1.0.0", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-message-util": "^29.7.0", "jest-mock": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-resolve": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "slash": "^3.0.0", "strip-bom": "^4.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-runtime/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/jest-runtime/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-runtime/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/jest-runtime/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/jest-runtime/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-runtime/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest-snapshot": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.11.6", "@babel/generator": "^7.7.2", "@babel/plugin-syntax-jsx": "^7.7.2", "@babel/plugin-syntax-typescript": "^7.7.2", "@babel/types": "^7.3.3", "@jest/expect-utils": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "babel-preset-current-node-syntax": "^1.0.0", "chalk": "^4.0.0", "expect": "^29.7.0", "graceful-fs": "^4.2.9", "jest-diff": "^29.7.0", "jest-get-type": "^29.6.3", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0", "natural-compare": "^1.4.0", "pretty-format": "^29.7.0", "semver": "^7.5.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-snapshot/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/jest-snapshot/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-snapshot/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/jest-snapshot/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/jest-snapshot/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-snapshot/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest-util": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-util/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/jest-util/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-util/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/jest-util/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/jest-util/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-util/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest-validate": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "camelcase": "^6.2.0", "chalk": "^4.0.0", "jest-get-type": "^29.6.3", "leven": "^3.1.0", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-validate/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/jest-validate/node_modules/camelcase": {"version": "6.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/jest-validate/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-validate/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/jest-validate/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/jest-validate/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-validate/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest-watcher": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.7.0", "string-length": "^4.0.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-watcher/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/jest-watcher/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-watcher/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/jest-watcher/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/jest-watcher/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-watcher/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest-worker": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "jest-util": "^29.7.0", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-worker/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-worker/node_modules/supports-color": {"version": "8.1.1", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/js-sha3": {"version": "0.8.0", "license": "MIT"}, "node_modules/js-tokens": {"version": "4.0.0", "dev": true, "license": "MIT"}, "node_modules/js-yaml": {"version": "3.14.1", "dev": true, "license": "MIT", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsbn": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/jsbn/-/jsbn-1.1.0.tgz", "integrity": "sha512-4bYVV3aAMtDTTu4+xsDYa6sy9GyJ69/amsu9sYF2zqjiEoZA5xJi3BrfX3uY+/IekIu7MwdObdbDWpoZdBv3/A==", "license": "MIT"}, "node_modules/jsesc": {"version": "3.1.0", "dev": true, "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json-buffer": {"version": "3.0.1", "license": "MIT"}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "dev": true, "license": "MIT"}, "node_modules/json-pointer": {"version": "0.6.2", "dev": true, "license": "MIT", "dependencies": {"foreach": "^2.0.4"}}, "node_modules/json-schema": {"version": "0.4.0", "license": "(AFL-2.1 OR BSD-3-Clause)"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "license": "MIT"}, "node_modules/json-schema-typed": {"version": "7.0.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "optional": true}, "node_modules/json-stable-stringify": {"version": "1.3.0", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "isarray": "^2.0.5", "jsonify": "^0.0.1", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/json-stable-stringify/node_modules/isarray": {"version": "2.0.5", "dev": true, "license": "MIT"}, "node_modules/json-stringify-safe": {"version": "5.0.1", "license": "ISC"}, "node_modules/json5": {"version": "2.2.3", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonfile": {"version": "2.4.0", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jsonify": {"version": "0.0.1", "dev": true, "license": "Public Domain", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/jsonwebtoken": {"version": "9.0.2", "license": "MIT", "dependencies": {"jws": "^3.2.2", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "semver": "^7.5.4"}, "engines": {"node": ">=12", "npm": ">=6"}}, "node_modules/jsprim": {"version": "1.4.2", "license": "MIT", "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.4.0", "verror": "1.10.0"}, "engines": {"node": ">=0.6.0"}}, "node_modules/jwa": {"version": "1.4.2", "license": "MIT", "dependencies": {"buffer-equal-constant-time": "^1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}}, "node_modules/jws": {"version": "3.2.2", "license": "MIT", "dependencies": {"jwa": "^1.4.1", "safe-buffer": "^5.0.1"}}, "node_modules/kareem": {"version": "2.5.1", "resolved": "https://registry.npmjs.org/kareem/-/kareem-2.5.1.tgz", "integrity": "sha512-7jFxRVm+jD+rkq3kY0iZDJfsO2/t4BBPeEb2qKn2lR/9KhuksYk5hxzfRYWMPV8P/x2d0kHD306YyWLzjjH+uA==", "license": "Apache-2.0", "engines": {"node": ">=12.0.0"}}, "node_modules/keccak": {"version": "3.0.4", "hasInstallScript": true, "license": "MIT", "dependencies": {"node-addon-api": "^2.0.0", "node-gyp-build": "^4.2.0", "readable-stream": "^3.6.0"}, "engines": {"node": ">=10.0.0"}}, "node_modules/keccak/node_modules/node-gyp-build": {"version": "4.8.4", "license": "MIT", "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}}, "node_modules/keccak/node_modules/readable-stream": {"version": "3.6.2", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/keyv": {"version": "4.5.4", "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/klaw": {"version": "1.3.1", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.9"}}, "node_modules/kleur": {"version": "3.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/lazystream": {"version": "1.0.1", "license": "MIT", "dependencies": {"readable-stream": "^2.0.5"}, "engines": {"node": ">= 0.6.3"}}, "node_modules/lcid": {"version": "1.0.0", "license": "MIT", "dependencies": {"invert-kv": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/level": {"version": "6.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"level-js": "^5.0.0", "level-packager": "^5.1.0", "leveldown": "^5.4.0"}, "engines": {"node": ">=8.6.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/level"}}, "node_modules/level-codec": {"version": "9.0.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"buffer": "^5.6.0"}, "engines": {"node": ">=6"}}, "node_modules/level-concat-iterator": {"version": "3.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"catering": "^2.1.0"}, "engines": {"node": ">=10"}}, "node_modules/level-errors": {"version": "2.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"errno": "~0.1.1"}, "engines": {"node": ">=6"}}, "node_modules/level-iterator-stream": {"version": "4.0.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"inherits": "^2.0.4", "readable-stream": "^3.4.0", "xtend": "^4.0.2"}, "engines": {"node": ">=6"}}, "node_modules/level-iterator-stream/node_modules/readable-stream": {"version": "3.6.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/level-js": {"version": "5.0.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"abstract-leveldown": "~6.2.3", "buffer": "^5.5.0", "inherits": "^2.0.3", "ltgt": "^2.1.2"}}, "node_modules/level-js/node_modules/abstract-leveldown": {"version": "6.2.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"buffer": "^5.5.0", "immediate": "^3.2.3", "level-concat-iterator": "~2.0.0", "level-supports": "~1.0.0", "xtend": "~4.0.0"}, "engines": {"node": ">=6"}}, "node_modules/level-js/node_modules/level-concat-iterator": {"version": "2.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/level-js/node_modules/level-supports": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"xtend": "^4.0.2"}, "engines": {"node": ">=6"}}, "node_modules/level-packager": {"version": "5.1.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"encoding-down": "^6.3.0", "levelup": "^4.3.2"}, "engines": {"node": ">=6"}}, "node_modules/level-supports": {"version": "2.1.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=10"}}, "node_modules/level-write-stream": {"version": "1.0.0", "dev": true, "optional": true, "dependencies": {"end-stream": "~0.1.0"}}, "node_modules/leveldown": {"version": "5.6.0", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"abstract-leveldown": "~6.2.1", "napi-macros": "~2.0.0", "node-gyp-build": "~4.1.0"}, "engines": {"node": ">=8.6.0"}}, "node_modules/leveldown/node_modules/abstract-leveldown": {"version": "6.2.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"buffer": "^5.5.0", "immediate": "^3.2.3", "level-concat-iterator": "~2.0.0", "level-supports": "~1.0.0", "xtend": "~4.0.0"}, "engines": {"node": ">=6"}}, "node_modules/leveldown/node_modules/level-concat-iterator": {"version": "2.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/leveldown/node_modules/level-supports": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"xtend": "^4.0.2"}, "engines": {"node": ">=6"}}, "node_modules/levelup": {"version": "4.4.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"deferred-leveldown": "~5.3.0", "level-errors": "~2.0.0", "level-iterator-stream": "~4.0.0", "level-supports": "~1.0.0", "xtend": "~4.0.0"}, "engines": {"node": ">=6"}}, "node_modules/levelup/node_modules/level-supports": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"xtend": "^4.0.2"}, "engines": {"node": ">=6"}}, "node_modules/leven": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "dev": true, "license": "MIT"}, "node_modules/load-json-file": {"version": "1.1.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "pinkie-promise": "^2.0.0", "strip-bom": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/load-json-file/node_modules/parse-json": {"version": "2.2.0", "license": "MIT", "dependencies": {"error-ex": "^1.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/load-json-file/node_modules/strip-bom": {"version": "2.0.0", "license": "MIT", "dependencies": {"is-utf8": "^0.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/locate-path": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/lodash": {"version": "4.17.21", "license": "MIT"}, "node_modules/lodash-es": {"version": "4.17.21", "dev": true, "license": "MIT"}, "node_modules/lodash.assign": {"version": "4.2.0", "license": "MIT"}, "node_modules/lodash.includes": {"version": "4.3.0", "license": "MIT"}, "node_modules/lodash.isboolean": {"version": "3.0.3", "license": "MIT"}, "node_modules/lodash.isinteger": {"version": "4.0.4", "license": "MIT"}, "node_modules/lodash.isnumber": {"version": "3.0.3", "license": "MIT"}, "node_modules/lodash.isplainobject": {"version": "4.0.6", "license": "MIT"}, "node_modules/lodash.isstring": {"version": "4.0.1", "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "license": "MIT"}, "node_modules/lodash.once": {"version": "4.1.1", "license": "MIT"}, "node_modules/lodash.sortby": {"version": "4.7.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/log-symbols": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-symbols/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/log-symbols/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/log-symbols/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/log-symbols/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/log-symbols/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/log-symbols/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/loglevel": {"version": "1.9.2", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">= 0.6.0"}, "funding": {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/loglevel"}}, "node_modules/long": {"version": "4.0.0", "dev": true, "license": "Apache-2.0", "optional": true}, "node_modules/loose-envify": {"version": "1.4.0", "dev": true, "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/lower-case": {"version": "1.1.4", "license": "MIT"}, "node_modules/lower-case-first": {"version": "1.0.2", "license": "MIT", "dependencies": {"lower-case": "^1.1.2"}}, "node_modules/lowercase-keys": {"version": "3.0.0", "license": "MIT", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lru-cache": {"version": "5.1.1", "dev": true, "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/ltgt": {"version": "2.2.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/make-dir": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"semver": "^7.5.3"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/makeerror": {"version": "1.0.12", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tmpl": "1.0.5"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/md5.js": {"version": "1.3.5", "license": "MIT", "dependencies": {"hash-base": "^3.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "node_modules/media-typer": {"version": "0.3.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/memdown": {"version": "1.4.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"abstract-leveldown": "~2.7.1", "functional-red-black-tree": "^1.0.1", "immediate": "^3.2.3", "inherits": "~2.0.1", "ltgt": "~2.2.0", "safe-buffer": "~5.1.1"}}, "node_modules/memdown/node_modules/abstract-leveldown": {"version": "2.7.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"xtend": "~4.0.0"}}, "node_modules/memdown/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "license": "MIT", "optional": true}, "node_modules/memory-pager": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/memory-pager/-/memory-pager-1.5.0.tgz", "integrity": "sha512-ZS4Bp4r/Zoeq6+NLJpP+0Zzm0pR8whtGPf1XExKLJBAczGMnSi3It14OiNCStjQjM6NU1okjQGSxgEZN8eBYKg==", "license": "MIT", "optional": true}, "node_modules/memorystream": {"version": "0.3.1", "engines": {"node": ">= 0.10.0"}}, "node_modules/merge-descriptors": {"version": "1.0.3", "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/merge-stream": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/methods": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/micromatch": {"version": "4.0.8", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime": {"version": "1.6.0", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/mimic-response": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/min-document": {"version": "2.19.0", "dependencies": {"dom-walk": "^0.1.0"}}, "node_modules/minimalistic-assert": {"version": "1.0.1", "license": "ISC"}, "node_modules/minimalistic-crypto-utils": {"version": "1.0.1", "license": "MIT"}, "node_modules/minimatch": {"version": "3.1.2", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minipass": {"version": "2.9.0", "license": "ISC", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}}, "node_modules/minizlib": {"version": "1.3.3", "license": "MIT", "dependencies": {"minipass": "^2.9.0"}}, "node_modules/mkdirp": {"version": "0.5.6", "license": "MIT", "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/mkdirp-promise": {"version": "5.0.1", "license": "ISC", "dependencies": {"mkdirp": "*"}, "engines": {"node": ">=4"}}, "node_modules/mocha": {"version": "10.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-colors": "4.1.1", "browser-stdout": "1.3.1", "chokidar": "3.5.3", "debug": "4.3.4", "diff": "5.0.0", "escape-string-regexp": "4.0.0", "find-up": "5.0.0", "glob": "7.2.0", "he": "1.2.0", "js-yaml": "4.1.0", "log-symbols": "4.1.0", "minimatch": "5.0.1", "ms": "2.1.3", "nanoid": "3.3.3", "serialize-javascript": "6.0.0", "strip-json-comments": "3.1.1", "supports-color": "8.1.1", "workerpool": "6.2.1", "yargs": "16.2.0", "yargs-parser": "20.2.4", "yargs-unparser": "2.0.0"}, "bin": {"_mocha": "bin/_mocha", "mocha": "bin/mocha.js"}, "engines": {"node": ">= 14.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mochajs"}}, "node_modules/mocha/node_modules/ansi-regex": {"version": "5.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/mocha/node_modules/argparse": {"version": "2.0.1", "dev": true, "license": "Python-2.0"}, "node_modules/mocha/node_modules/chokidar": {"version": "3.5.3", "dev": true, "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/mocha/node_modules/cliui": {"version": "7.0.4", "dev": true, "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "node_modules/mocha/node_modules/debug": {"version": "4.3.4", "dev": true, "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/mocha/node_modules/debug/node_modules/ms": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/mocha/node_modules/escape-string-regexp": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/mocha/node_modules/find-up": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/mocha/node_modules/fsevents": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/mocha/node_modules/glob": {"version": "7.2.0", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/mocha/node_modules/glob/node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/mocha/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/mocha/node_modules/js-yaml": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/mocha/node_modules/locate-path": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/mocha/node_modules/minimatch": {"version": "5.0.1", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/mocha/node_modules/minimatch/node_modules/brace-expansion": {"version": "2.0.2", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/mocha/node_modules/p-locate": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/mocha/node_modules/strip-ansi": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/mocha/node_modules/supports-color": {"version": "8.1.1", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/mocha/node_modules/yargs": {"version": "16.2.0", "dev": true, "license": "MIT", "dependencies": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}, "engines": {"node": ">=10"}}, "node_modules/mock-fs": {"version": "4.14.0", "license": "MIT"}, "node_modules/mongodb": {"version": "5.9.2", "resolved": "https://registry.npmjs.org/mongodb/-/mongodb-5.9.2.tgz", "integrity": "sha512-H60HecKO4Bc+7dhOv4sJlgvenK4fQNqqUIlXxZYQNbfEWSALGAwGoyJd/0Qwk4TttFXUOHJ2ZJQe/52ScaUwtQ==", "license": "Apache-2.0", "dependencies": {"bson": "^5.5.0", "mongodb-connection-string-url": "^2.6.0", "socks": "^2.7.1"}, "engines": {"node": ">=14.20.1"}, "optionalDependencies": {"@mongodb-js/saslprep": "^1.1.0"}, "peerDependencies": {"@aws-sdk/credential-providers": "^3.188.0", "@mongodb-js/zstd": "^1.0.0", "kerberos": "^1.0.0 || ^2.0.0", "mongodb-client-encryption": ">=2.3.0 <3", "snappy": "^7.2.2"}, "peerDependenciesMeta": {"@aws-sdk/credential-providers": {"optional": true}, "@mongodb-js/zstd": {"optional": true}, "kerberos": {"optional": true}, "mongodb-client-encryption": {"optional": true}, "snappy": {"optional": true}}}, "node_modules/mongodb-connection-string-url": {"version": "2.6.0", "resolved": "https://registry.npmjs.org/mongodb-connection-string-url/-/mongodb-connection-string-url-2.6.0.tgz", "integrity": "sha512-WvTZlI9ab0QYtTYnuMLgobULWhokRjtC7db9LtcVfJ+Hsnyr5eo6ZtNAt3Ly24XZScGMelOcGtm7lSn0332tPQ==", "license": "Apache-2.0", "dependencies": {"@types/whatwg-url": "^8.2.1", "whatwg-url": "^11.0.0"}}, "node_modules/mongoose": {"version": "7.8.7", "resolved": "https://registry.npmjs.org/mongoose/-/mongoose-7.8.7.tgz", "integrity": "sha512-5Bo4CrUxrPITrhMKsqUTOkXXo2CoRC5tXxVQhnddCzqDMwRXfyStrxj1oY865g8gaekSBhxAeNkYyUSJvGm9Hw==", "license": "MIT", "dependencies": {"bson": "^5.5.0", "kareem": "2.5.1", "mongodb": "5.9.2", "mpath": "0.9.0", "mquery": "5.0.0", "ms": "2.1.3", "sift": "16.0.1"}, "engines": {"node": ">=14.20.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mongoose"}}, "node_modules/mpath": {"version": "0.9.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/mquery": {"version": "5.0.0", "license": "MIT", "dependencies": {"debug": "4.x"}, "engines": {"node": ">=14.0.0"}}, "node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/multer": {"version": "1.4.5-lts.2", "license": "MIT", "dependencies": {"append-field": "^1.0.0", "busboy": "^1.0.0", "concat-stream": "^1.5.2", "mkdirp": "^0.5.4", "object-assign": "^4.1.1", "type-is": "^1.6.4", "xtend": "^4.0.0"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/multibase": {"version": "0.6.1", "license": "MIT", "dependencies": {"base-x": "^3.0.8", "buffer": "^5.5.0"}}, "node_modules/multicodec": {"version": "0.5.7", "license": "MIT", "dependencies": {"varint": "^5.0.0"}}, "node_modules/multihashes": {"version": "0.4.21", "license": "MIT", "dependencies": {"buffer": "^5.5.0", "multibase": "^0.7.0", "varint": "^5.0.0"}}, "node_modules/multihashes/node_modules/multibase": {"version": "0.7.0", "license": "MIT", "dependencies": {"base-x": "^3.0.8", "buffer": "^5.5.0"}}, "node_modules/nano-base32": {"version": "1.0.1", "license": "MIT"}, "node_modules/nano-json-stream-parser": {"version": "0.1.2", "license": "MIT"}, "node_modules/nanoid": {"version": "3.3.3", "dev": true, "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/napi-macros": {"version": "2.0.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/natural-compare": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/negotiator": {"version": "0.6.3", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/next-tick": {"version": "1.1.0", "license": "ISC"}, "node_modules/no-case": {"version": "2.3.2", "license": "MIT", "dependencies": {"lower-case": "^1.1.1"}}, "node_modules/node-abort-controller": {"version": "3.1.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/node-addon-api": {"version": "2.0.2", "license": "MIT"}, "node_modules/node-domexception": {"version": "1.0.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "github", "url": "https://paypal.me/jimmywarting"}], "license": "MIT", "engines": {"node": ">=10.5.0"}}, "node_modules/node-fetch": {"version": "3.3.2", "license": "MIT", "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}}, "node_modules/node-gyp-build": {"version": "4.1.1", "dev": true, "license": "MIT", "optional": true, "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}}, "node_modules/node-int64": {"version": "0.4.0", "dev": true, "license": "MIT"}, "node_modules/node-interval-tree": {"version": "1.3.3", "dev": true, "license": "MIT", "dependencies": {"shallowequal": "^1.0.2"}, "engines": {"node": ">= 7.6.0"}}, "node_modules/node-releases": {"version": "2.0.19", "dev": true, "license": "MIT"}, "node_modules/nodemon": {"version": "3.1.10", "dev": true, "license": "MIT", "dependencies": {"chokidar": "^3.5.2", "debug": "^4", "ignore-by-default": "^1.0.1", "minimatch": "^3.1.2", "pstree.remy": "^1.1.8", "semver": "^7.5.3", "simple-update-notifier": "^2.0.0", "supports-color": "^5.5.0", "touch": "^3.1.0", "undefsafe": "^2.0.5"}, "bin": {"nodemon": "bin/nodemon.js"}, "engines": {"node": ">=10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/nodemon"}}, "node_modules/nofilter": {"version": "1.0.4", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/normalize-package-data": {"version": "2.5.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "node_modules/normalize-package-data/node_modules/semver": {"version": "5.7.2", "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/normalize-path": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-url": {"version": "6.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/npm-run-path": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/nth-check": {"version": "2.1.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0"}, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}}, "node_modules/number-is-nan": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/number-to-bn": {"version": "1.7.0", "license": "MIT", "dependencies": {"bn.js": "4.11.6", "strip-hex-prefix": "1.0.0"}, "engines": {"node": ">=6.5.0", "npm": ">=3"}}, "node_modules/number-to-bn/node_modules/bn.js": {"version": "4.11.6", "license": "MIT"}, "node_modules/oauth-sign": {"version": "0.9.0", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/object-assign": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.13.4", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/oboe": {"version": "2.1.5", "license": "BSD", "dependencies": {"http-https": "^1.0.0"}}, "node_modules/on-finished": {"version": "2.4.1", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "5.1.2", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/original-require": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/os-locale": {"version": "1.4.0", "license": "MIT", "dependencies": {"lcid": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/p-cancelable": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=12.20"}}, "node_modules/p-limit": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/p-locate/node_modules/p-limit": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate/node_modules/p-try": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/p-try": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/package-json-from-dist": {"version": "1.0.1", "license": "BlueOak-1.0.0"}, "node_modules/param-case": {"version": "2.1.1", "license": "MIT", "dependencies": {"no-case": "^2.2.0"}}, "node_modules/parse-headers": {"version": "2.0.6", "license": "MIT"}, "node_modules/parse-json": {"version": "5.2.0", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parse5": {"version": "7.3.0", "license": "MIT", "dependencies": {"entities": "^6.0.0"}, "funding": {"url": "https://github.com/inikulin/parse5?sponsor=1"}}, "node_modules/parse5-htmlparser2-tree-adapter": {"version": "7.1.0", "license": "MIT", "dependencies": {"domhandler": "^5.0.3", "parse5": "^7.0.0"}, "funding": {"url": "https://github.com/inikulin/parse5?sponsor=1"}}, "node_modules/parse5-parser-stream": {"version": "7.1.2", "license": "MIT", "dependencies": {"parse5": "^7.0.0"}, "funding": {"url": "https://github.com/inikulin/parse5?sponsor=1"}}, "node_modules/parse5/node_modules/entities": {"version": "6.0.1", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/parseurl": {"version": "1.3.3", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/pascal-case": {"version": "2.0.1", "license": "MIT", "dependencies": {"camel-case": "^3.0.0", "upper-case-first": "^1.1.0"}}, "node_modules/path-case": {"version": "2.1.1", "license": "MIT", "dependencies": {"no-case": "^2.2.0"}}, "node_modules/path-exists": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "license": "MIT"}, "node_modules/path-scurry": {"version": "1.11.1", "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/path-scurry/node_modules/lru-cache": {"version": "10.4.3", "license": "ISC"}, "node_modules/path-scurry/node_modules/minipass": {"version": "7.1.2", "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/path-to-regexp": {"version": "0.1.12", "license": "MIT"}, "node_modules/path-type": {"version": "1.1.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/pbkdf2": {"version": "3.1.3", "license": "MIT", "dependencies": {"create-hash": "~1.1.3", "create-hmac": "^1.1.7", "ripemd160": "=2.0.1", "safe-buffer": "^5.2.1", "sha.js": "^2.4.11", "to-buffer": "^1.2.0"}, "engines": {"node": ">=0.12"}}, "node_modules/pbkdf2/node_modules/create-hash": {"version": "1.1.3", "license": "MIT", "dependencies": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "sha.js": "^2.4.0"}}, "node_modules/pbkdf2/node_modules/hash-base": {"version": "2.0.2", "license": "MIT", "dependencies": {"inherits": "^2.0.1"}}, "node_modules/pbkdf2/node_modules/ripemd160": {"version": "2.0.1", "license": "MIT", "dependencies": {"hash-base": "^2.0.0", "inherits": "^2.0.1"}}, "node_modules/performance-now": {"version": "2.1.0", "license": "MIT"}, "node_modules/picocolors": {"version": "1.1.1", "dev": true, "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pify": {"version": "2.3.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/pinkie": {"version": "2.0.4", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/pinkie-promise": {"version": "2.0.1", "license": "MIT", "dependencies": {"pinkie": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/pirates": {"version": "4.0.7", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/pkg-dir": {"version": "4.2.0", "dev": true, "license": "MIT", "dependencies": {"find-up": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-up": {"version": "3.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"find-up": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-up/node_modules/find-up": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"locate-path": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/pkg-up/node_modules/locate-path": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/pkg-up/node_modules/p-limit": {"version": "2.3.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/pkg-up/node_modules/p-locate": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"p-limit": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/pkg-up/node_modules/p-try": {"version": "2.2.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/pkg-up/node_modules/path-exists": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/pluralize": {"version": "8.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/possible-typed-array-names": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/pouchdb": {"version": "7.3.0", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"abort-controller": "3.0.0", "argsarray": "0.0.1", "buffer-from": "1.1.2", "clone-buffer": "1.0.0", "double-ended-queue": "2.1.0-0", "fetch-cookie": "0.11.0", "immediate": "3.3.0", "inherits": "2.0.4", "level": "6.0.1", "level-codec": "9.0.2", "level-write-stream": "1.0.0", "leveldown": "5.6.0", "levelup": "4.4.0", "ltgt": "2.2.1", "node-fetch": "2.6.7", "readable-stream": "1.1.14", "spark-md5": "3.0.2", "through2": "3.0.2", "uuid": "8.3.2", "vuvuzela": "1.0.3"}}, "node_modules/pouchdb-abstract-mapreduce": {"version": "7.3.1", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"pouchdb-binary-utils": "7.3.1", "pouchdb-collate": "7.3.1", "pouchdb-collections": "7.3.1", "pouchdb-errors": "7.3.1", "pouchdb-fetch": "7.3.1", "pouchdb-mapreduce-utils": "7.3.1", "pouchdb-md5": "7.3.1", "pouchdb-utils": "7.3.1"}}, "node_modules/pouchdb-adapter-leveldb-core": {"version": "7.3.1", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"argsarray": "0.0.1", "buffer-from": "1.1.2", "double-ended-queue": "2.1.0-0", "levelup": "4.4.0", "pouchdb-adapter-utils": "7.3.1", "pouchdb-binary-utils": "7.3.1", "pouchdb-collections": "7.3.1", "pouchdb-errors": "7.3.1", "pouchdb-json": "7.3.1", "pouchdb-md5": "7.3.1", "pouchdb-merge": "7.3.1", "pouchdb-utils": "7.3.1", "sublevel-pouchdb": "7.3.1", "through2": "3.0.2"}}, "node_modules/pouchdb-adapter-memory": {"version": "7.3.1", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"memdown": "1.4.1", "pouchdb-adapter-leveldb-core": "7.3.1", "pouchdb-utils": "7.3.1"}}, "node_modules/pouchdb-adapter-utils": {"version": "7.3.1", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"pouchdb-binary-utils": "7.3.1", "pouchdb-collections": "7.3.1", "pouchdb-errors": "7.3.1", "pouchdb-md5": "7.3.1", "pouchdb-merge": "7.3.1", "pouchdb-utils": "7.3.1"}}, "node_modules/pouchdb-binary-utils": {"version": "7.3.1", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"buffer-from": "1.1.2"}}, "node_modules/pouchdb-collate": {"version": "7.3.1", "dev": true, "license": "Apache-2.0", "optional": true}, "node_modules/pouchdb-collections": {"version": "7.3.1", "dev": true, "license": "Apache-2.0", "optional": true}, "node_modules/pouchdb-debug": {"version": "7.2.1", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"debug": "3.1.0"}}, "node_modules/pouchdb-debug/node_modules/debug": {"version": "3.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"ms": "2.0.0"}}, "node_modules/pouchdb-debug/node_modules/ms": {"version": "2.0.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/pouchdb-errors": {"version": "7.3.1", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"inherits": "2.0.4"}}, "node_modules/pouchdb-fetch": {"version": "7.3.1", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"abort-controller": "3.0.0", "fetch-cookie": "0.11.0", "node-fetch": "2.6.7"}}, "node_modules/pouchdb-fetch/node_modules/node-fetch": {"version": "2.6.7", "dev": true, "license": "MIT", "optional": true, "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/pouchdb-fetch/node_modules/tr46": {"version": "0.0.3", "dev": true, "license": "MIT", "optional": true}, "node_modules/pouchdb-fetch/node_modules/webidl-conversions": {"version": "3.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "optional": true}, "node_modules/pouchdb-fetch/node_modules/whatwg-url": {"version": "5.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/pouchdb-find": {"version": "7.3.1", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"pouchdb-abstract-mapreduce": "7.3.1", "pouchdb-collate": "7.3.1", "pouchdb-errors": "7.3.1", "pouchdb-fetch": "7.3.1", "pouchdb-md5": "7.3.1", "pouchdb-selector-core": "7.3.1", "pouchdb-utils": "7.3.1"}}, "node_modules/pouchdb-json": {"version": "7.3.1", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"vuvuzela": "1.0.3"}}, "node_modules/pouchdb-mapreduce-utils": {"version": "7.3.1", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"argsarray": "0.0.1", "inherits": "2.0.4", "pouchdb-collections": "7.3.1", "pouchdb-utils": "7.3.1"}}, "node_modules/pouchdb-md5": {"version": "7.3.1", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"pouchdb-binary-utils": "7.3.1", "spark-md5": "3.0.2"}}, "node_modules/pouchdb-merge": {"version": "7.3.1", "dev": true, "license": "Apache-2.0", "optional": true}, "node_modules/pouchdb-selector-core": {"version": "7.3.1", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"pouchdb-collate": "7.3.1", "pouchdb-utils": "7.3.1"}}, "node_modules/pouchdb-utils": {"version": "7.3.1", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"argsarray": "0.0.1", "clone-buffer": "1.0.0", "immediate": "3.3.0", "inherits": "2.0.4", "pouchdb-collections": "7.3.1", "pouchdb-errors": "7.3.1", "pouchdb-md5": "7.3.1", "uuid": "8.3.2"}}, "node_modules/pouchdb-utils/node_modules/uuid": {"version": "8.3.2", "dev": true, "license": "MIT", "optional": true, "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/pouchdb/node_modules/isarray": {"version": "0.0.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/pouchdb/node_modules/node-fetch": {"version": "2.6.7", "dev": true, "license": "MIT", "optional": true, "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/pouchdb/node_modules/readable-stream": {"version": "1.1.14", "dev": true, "license": "MIT", "optional": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/pouchdb/node_modules/string_decoder": {"version": "0.10.31", "dev": true, "license": "MIT", "optional": true}, "node_modules/pouchdb/node_modules/tr46": {"version": "0.0.3", "dev": true, "license": "MIT", "optional": true}, "node_modules/pouchdb/node_modules/uuid": {"version": "8.3.2", "dev": true, "license": "MIT", "optional": true, "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/pouchdb/node_modules/webidl-conversions": {"version": "3.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "optional": true}, "node_modules/pouchdb/node_modules/whatwg-url": {"version": "5.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/pretty-format": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/pretty-format/node_modules/ansi-styles": {"version": "5.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/process": {"version": "0.11.10", "license": "MIT", "engines": {"node": ">= 0.6.0"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "license": "MIT"}, "node_modules/prompts": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "engines": {"node": ">= 6"}}, "node_modules/proxy-addr": {"version": "2.0.7", "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/prr": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/psl": {"version": "1.15.0", "license": "MIT", "dependencies": {"punycode": "^2.3.1"}, "funding": {"url": "https://github.com/sponsors/lupomontero"}}, "node_modules/psl/node_modules/punycode": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pstree.remy": {"version": "1.1.8", "dev": true, "license": "MIT"}, "node_modules/pump": {"version": "3.0.3", "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/punycode": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pure-rand": {"version": "5.0.5", "funding": [{"type": "individual", "url": "https://github.com/sponsors/dubzzz"}, {"type": "opencollective", "url": "https://opencollective.com/fast-check"}], "license": "MIT"}, "node_modules/qs": {"version": "6.13.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.6"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/query-string": {"version": "5.1.1", "license": "MIT", "dependencies": {"decode-uri-component": "^0.2.0", "object-assign": "^4.1.0", "strict-uri-encode": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/querystringify": {"version": "2.2.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/queue-microtask": {"version": "1.2.3", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "optional": true}, "node_modules/quick-lru": {"version": "5.1.1", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/randombytes": {"version": "2.1.0", "license": "MIT", "dependencies": {"safe-buffer": "^5.1.0"}}, "node_modules/range-parser": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.5.2", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/raw-body/node_modules/iconv-lite": {"version": "0.4.24", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/react-is": {"version": "18.3.1", "dev": true, "license": "MIT"}, "node_modules/read-pkg": {"version": "1.1.0", "license": "MIT", "dependencies": {"load-json-file": "^1.0.0", "normalize-package-data": "^2.3.2", "path-type": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/read-pkg-up": {"version": "1.0.1", "license": "MIT", "dependencies": {"find-up": "^1.0.0", "read-pkg": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/read-pkg-up/node_modules/find-up": {"version": "1.1.2", "license": "MIT", "dependencies": {"path-exists": "^2.0.0", "pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/read-pkg-up/node_modules/path-exists": {"version": "2.1.0", "license": "MIT", "dependencies": {"pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/readable-stream": {"version": "2.3.8", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/readable-stream/node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/readdir-glob": {"version": "1.1.3", "license": "Apache-2.0", "dependencies": {"minimatch": "^5.1.0"}}, "node_modules/readdir-glob/node_modules/brace-expansion": {"version": "2.0.2", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/readdir-glob/node_modules/minimatch": {"version": "5.1.6", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/readdirp": {"version": "3.6.0", "dev": true, "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/redux": {"version": "3.7.2", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.2.1", "lodash-es": "^4.2.1", "loose-envify": "^1.1.0", "symbol-observable": "^1.0.3"}}, "node_modules/redux-saga": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"@redux-saga/core": "^1.0.0"}}, "node_modules/request": {"version": "2.88.2", "license": "Apache-2.0", "dependencies": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.3", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.5.0", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}, "engines": {"node": ">= 6"}}, "node_modules/request/node_modules/form-data": {"version": "2.3.3", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "node_modules/request/node_modules/punycode": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/request/node_modules/qs": {"version": "6.5.3", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.6"}}, "node_modules/request/node_modules/tough-cookie": {"version": "2.5.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"psl": "^1.1.28", "punycode": "^2.1.1"}, "engines": {"node": ">=0.8"}}, "node_modules/request/node_modules/uuid": {"version": "3.4.0", "license": "MIT", "bin": {"uuid": "bin/uuid"}}, "node_modules/require-directory": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-from-string": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-main-filename": {"version": "1.0.1", "license": "ISC"}, "node_modules/requires-port": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/reselect": {"version": "4.1.8", "dev": true, "license": "MIT"}, "node_modules/reselect-tree": {"version": "1.3.7", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.1.0", "json-pointer": "^0.6.1", "reselect": "^4.0.0"}}, "node_modules/reselect-tree/node_modules/debug": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/resolve": {"version": "1.22.10", "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-alpn": {"version": "1.2.1", "license": "MIT"}, "node_modules/resolve-cwd": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/resolve-from": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/resolve.exports": {"version": "2.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/responselike": {"version": "2.0.1", "license": "MIT", "dependencies": {"lowercase-keys": "^2.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/responselike/node_modules/lowercase-keys": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/restore-cursor": {"version": "3.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=8"}}, "node_modules/retry": {"version": "0.13.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">= 4"}}, "node_modules/rimraf": {"version": "2.7.1", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/ripemd160": {"version": "2.0.2", "license": "MIT", "dependencies": {"hash-base": "^3.0.0", "inherits": "^2.0.1"}}, "node_modules/ripemd160-min": {"version": "0.0.6", "engines": {"node": ">=8"}}, "node_modules/rlp": {"version": "2.2.7", "license": "MPL-2.0", "dependencies": {"bn.js": "^5.2.0"}, "bin": {"rlp": "bin/rlp"}}, "node_modules/rlp/node_modules/bn.js": {"version": "5.2.2", "license": "MIT"}, "node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safe-regex-test": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-regex": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safer-buffer": {"version": "2.1.2", "license": "MIT"}, "node_modules/scrypt-js": {"version": "2.0.4", "license": "MIT"}, "node_modules/secp256k1": {"version": "4.0.4", "hasInstallScript": true, "license": "MIT", "dependencies": {"elliptic": "^6.5.7", "node-addon-api": "^5.0.0", "node-gyp-build": "^4.2.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/secp256k1/node_modules/elliptic": {"version": "6.6.1", "license": "MIT", "dependencies": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.1", "inherits": "^2.0.4", "minimalistic-assert": "^1.0.1", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/secp256k1/node_modules/node-addon-api": {"version": "5.1.0", "license": "MIT"}, "node_modules/secp256k1/node_modules/node-gyp-build": {"version": "4.8.4", "license": "MIT", "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}}, "node_modules/semver": {"version": "7.7.2", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/send": {"version": "0.19.0", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/send/node_modules/debug/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/send/node_modules/encodeurl": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/sentence-case": {"version": "2.1.1", "license": "MIT", "dependencies": {"no-case": "^2.2.0", "upper-case-first": "^1.1.2"}}, "node_modules/serialize-javascript": {"version": "6.0.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"randombytes": "^2.1.0"}}, "node_modules/serve-static": {"version": "1.16.2", "license": "MIT", "dependencies": {"encodeurl": "~2.0.0", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.19.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/servify": {"version": "0.1.12", "license": "MIT", "dependencies": {"body-parser": "^1.16.0", "cors": "^2.8.1", "express": "^4.14.0", "request": "^2.79.0", "xhr": "^2.3.3"}, "engines": {"node": ">=6"}}, "node_modules/set-blocking": {"version": "2.0.0", "license": "ISC"}, "node_modules/set-function-length": {"version": "1.2.2", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/setimmediate": {"version": "1.0.4", "license": "MIT"}, "node_modules/setprototypeof": {"version": "1.2.0", "license": "ISC"}, "node_modules/sha.js": {"version": "2.4.11", "license": "(MIT AND BSD-3-<PERSON><PERSON>)", "dependencies": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}, "bin": {"sha.js": "bin.js"}}, "node_modules/sha3": {"version": "2.1.4", "license": "MIT", "dependencies": {"buffer": "6.0.3"}}, "node_modules/sha3/node_modules/buffer": {"version": "6.0.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/shallowequal": {"version": "1.1.0", "dev": true, "license": "MIT"}, "node_modules/shebang-command": {"version": "2.0.0", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/side-channel": {"version": "1.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/sift": {"version": "16.0.1", "resolved": "https://registry.npmjs.org/sift/-/sift-16.0.1.tgz", "integrity": "sha512-Wv6BjQ5zbhW7VFefWusVP33T/EM0vYikCaQ2qR8yULbsilAT8/wQaXvuQ3ptGLpoKx+lihJE3y2UTgKDyyNHZQ==", "license": "MIT"}, "node_modules/signal-exit": {"version": "3.0.7", "dev": true, "license": "ISC"}, "node_modules/simple-concat": {"version": "1.0.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/simple-get": {"version": "2.8.2", "license": "MIT", "dependencies": {"decompress-response": "^3.3.0", "once": "^1.3.1", "simple-concat": "^1.0.0"}}, "node_modules/simple-get/node_modules/decompress-response": {"version": "3.3.0", "license": "MIT", "dependencies": {"mimic-response": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/simple-update-notifier": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"semver": "^7.5.3"}, "engines": {"node": ">=10"}}, "node_modules/sisteransi": {"version": "1.0.5", "dev": true, "license": "MIT"}, "node_modules/slash": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/smart-buffer": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/smart-buffer/-/smart-buffer-4.2.0.tgz", "integrity": "sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==", "license": "MIT", "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}}, "node_modules/snake-case": {"version": "2.1.0", "license": "MIT", "dependencies": {"no-case": "^2.2.0"}}, "node_modules/socks": {"version": "2.8.6", "resolved": "https://registry.npmjs.org/socks/-/socks-2.8.6.tgz", "integrity": "sha512-pe4Y2yzru68lXCb38aAqRf5gvN8YdjP1lok5o0J7BOHljkyCGKVz7H3vpVIXKD27rj2giOJ7DwVyk/GWrPHDWA==", "license": "MIT", "dependencies": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}, "engines": {"node": ">= 10.0.0", "npm": ">= 3.0.0"}}, "node_modules/solc": {"version": "0.4.26", "license": "MIT", "dependencies": {"fs-extra": "^0.30.0", "memorystream": "^0.3.1", "require-from-string": "^1.1.0", "semver": "^5.3.0", "yargs": "^4.7.1"}, "bin": {"solcjs": "solcjs"}}, "node_modules/solc/node_modules/ansi-regex": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/solc/node_modules/camelcase": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/solc/node_modules/cliui": {"version": "3.2.0", "license": "ISC", "dependencies": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wrap-ansi": "^2.0.0"}}, "node_modules/solc/node_modules/decamelize": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/solc/node_modules/get-caller-file": {"version": "1.0.3", "license": "ISC"}, "node_modules/solc/node_modules/is-fullwidth-code-point": {"version": "1.0.0", "license": "MIT", "dependencies": {"number-is-nan": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/solc/node_modules/semver": {"version": "5.7.2", "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/solc/node_modules/string-width": {"version": "1.0.2", "license": "MIT", "dependencies": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/solc/node_modules/strip-ansi": {"version": "3.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/solc/node_modules/wrap-ansi": {"version": "2.1.0", "license": "MIT", "dependencies": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/solc/node_modules/y18n": {"version": "3.2.2", "license": "ISC"}, "node_modules/solc/node_modules/yargs": {"version": "4.8.1", "license": "MIT", "dependencies": {"cliui": "^3.2.0", "decamelize": "^1.1.1", "get-caller-file": "^1.0.1", "lodash.assign": "^4.0.3", "os-locale": "^1.4.0", "read-pkg-up": "^1.0.1", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^1.0.1", "which-module": "^1.0.0", "window-size": "^0.2.0", "y18n": "^3.2.1", "yargs-parser": "^2.4.1"}}, "node_modules/solc/node_modules/yargs-parser": {"version": "2.4.1", "license": "ISC", "dependencies": {"camelcase": "^3.0.0", "lodash.assign": "^4.0.6"}}, "node_modules/source-map": {"version": "0.6.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.13", "dev": true, "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/spark-md5": {"version": "3.0.2", "dev": true, "license": "(WTFPL OR MIT)", "optional": true}, "node_modules/sparse-bitfield": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/sparse-bitfield/-/sparse-bitfield-3.0.3.tgz", "integrity": "sha512-kvzhi7vqKTfkh0PZU+2D2PIllw2ymqJKujUcyPMd9Y75Nv4nPbGJZXNhxsgdQab2BmlDct1YnfQCguEvHr7VsQ==", "license": "MIT", "optional": true, "dependencies": {"memory-pager": "^1.0.2"}}, "node_modules/spdx-correct": {"version": "3.2.0", "license": "Apache-2.0", "dependencies": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-exceptions": {"version": "2.5.0", "license": "CC-BY-3.0"}, "node_modules/spdx-expression-parse": {"version": "3.0.1", "license": "MIT", "dependencies": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-license-ids": {"version": "3.0.21", "license": "CC0-1.0"}, "node_modules/sprintf-js": {"version": "1.0.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/sshpk": {"version": "1.18.0", "license": "MIT", "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sshpk/node_modules/jsbn": {"version": "0.1.1", "license": "MIT"}, "node_modules/stack-utils": {"version": "2.0.6", "dev": true, "license": "MIT", "dependencies": {"escape-string-regexp": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/stack-utils/node_modules/escape-string-regexp": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/statuses": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/streamsearch": {"version": "1.1.0", "engines": {"node": ">=10.0.0"}}, "node_modules/streamx": {"version": "2.22.1", "license": "MIT", "dependencies": {"fast-fifo": "^1.3.2", "text-decoder": "^1.1.0"}, "optionalDependencies": {"bare-events": "^2.2.0"}}, "node_modules/strict-uri-encode": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/string_decoder": {"version": "1.1.1", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/string_decoder/node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/string-length": {"version": "4.0.2", "dev": true, "license": "MIT", "dependencies": {"char-regex": "^1.0.2", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/string-length/node_modules/ansi-regex": {"version": "5.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/string-length/node_modules/strip-ansi": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width": {"version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width-cjs": {"name": "string-width", "version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width-cjs/node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/string-width-cjs/node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width/node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/string-width/node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "4.0.0", "license": "MIT", "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/strip-ansi-cjs": {"name": "strip-ansi", "version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi-cjs/node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/strip-bom": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/strip-final-newline": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/strip-hex-prefix": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-hex-prefixed": "1.0.0"}, "engines": {"node": ">=6.5.0", "npm": ">=3"}}, "node_modules/strip-indent": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/sublevel-pouchdb": {"version": "7.3.1", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"inherits": "2.0.4", "level-codec": "9.0.2", "ltgt": "2.2.1", "readable-stream": "1.1.14"}}, "node_modules/sublevel-pouchdb/node_modules/isarray": {"version": "0.0.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/sublevel-pouchdb/node_modules/readable-stream": {"version": "1.1.14", "dev": true, "license": "MIT", "optional": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/sublevel-pouchdb/node_modules/string_decoder": {"version": "0.10.31", "dev": true, "license": "MIT", "optional": true}, "node_modules/supports-color": {"version": "5.5.0", "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/swap-case": {"version": "1.1.2", "license": "MIT", "dependencies": {"lower-case": "^1.1.1", "upper-case": "^1.1.1"}}, "node_modules/swarm-js": {"version": "0.1.42", "license": "MIT", "dependencies": {"bluebird": "^3.5.0", "buffer": "^5.0.5", "eth-lib": "^0.1.26", "fs-extra": "^4.0.2", "got": "^11.8.5", "mime-types": "^2.1.16", "mkdirp-promise": "^5.0.1", "mock-fs": "^4.1.0", "setimmediate": "^1.0.5", "tar": "^4.0.2", "xhr-request": "^1.0.1"}}, "node_modules/swarm-js/node_modules/@szmarczak/http-timer": {"version": "4.0.6", "license": "MIT", "dependencies": {"defer-to-connect": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/swarm-js/node_modules/cacheable-lookup": {"version": "5.0.4", "license": "MIT", "engines": {"node": ">=10.6.0"}}, "node_modules/swarm-js/node_modules/fs-extra": {"version": "4.0.3", "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}}, "node_modules/swarm-js/node_modules/got": {"version": "11.8.6", "license": "MIT", "dependencies": {"@sindresorhus/is": "^4.0.0", "@szmarczak/http-timer": "^4.0.5", "@types/cacheable-request": "^6.0.1", "@types/responselike": "^1.0.0", "cacheable-lookup": "^5.0.3", "cacheable-request": "^7.0.2", "decompress-response": "^6.0.0", "http2-wrapper": "^1.0.0-beta.5.2", "lowercase-keys": "^2.0.0", "p-cancelable": "^2.0.0", "responselike": "^2.0.0"}, "engines": {"node": ">=10.19.0"}, "funding": {"url": "https://github.com/sindresorhus/got?sponsor=1"}}, "node_modules/swarm-js/node_modules/http2-wrapper": {"version": "1.0.3", "license": "MIT", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.0.0"}, "engines": {"node": ">=10.19.0"}}, "node_modules/swarm-js/node_modules/jsonfile": {"version": "4.0.0", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/swarm-js/node_modules/lowercase-keys": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/swarm-js/node_modules/p-cancelable": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/swarm-js/node_modules/setimmediate": {"version": "1.0.5", "license": "MIT"}, "node_modules/swarm-js/node_modules/universalify": {"version": "0.1.2", "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/symbol-observable": {"version": "1.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/tar": {"version": "4.4.19", "license": "ISC", "dependencies": {"chownr": "^1.1.4", "fs-minipass": "^1.2.7", "minipass": "^2.9.0", "minizlib": "^1.3.3", "mkdirp": "^0.5.5", "safe-buffer": "^5.2.1", "yallist": "^3.1.1"}, "engines": {"node": ">=4.5"}}, "node_modules/tar-stream": {"version": "3.1.7", "license": "MIT", "dependencies": {"b4a": "^1.6.4", "fast-fifo": "^1.2.0", "streamx": "^2.15.0"}}, "node_modules/test-exclude": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"@istanbuljs/schema": "^0.1.2", "glob": "^7.1.4", "minimatch": "^3.0.4"}, "engines": {"node": ">=8"}}, "node_modules/testrpc": {"version": "0.0.1"}, "node_modules/text-decoder": {"version": "1.2.3", "license": "Apache-2.0", "dependencies": {"b4a": "^1.6.4"}}, "node_modules/through2": {"version": "3.0.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"inherits": "^2.0.4", "readable-stream": "2 || 3"}}, "node_modules/timed-out": {"version": "4.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/tiny-typed-emitter": {"version": "2.1.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/title-case": {"version": "2.1.1", "license": "MIT", "dependencies": {"no-case": "^2.2.0", "upper-case": "^1.0.3"}}, "node_modules/tmpl": {"version": "1.0.5", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/to-buffer": {"version": "1.2.1", "license": "MIT", "dependencies": {"isarray": "^2.0.5", "safe-buffer": "^5.2.1", "typed-array-buffer": "^1.0.3"}, "engines": {"node": ">= 0.4"}}, "node_modules/to-buffer/node_modules/isarray": {"version": "2.0.5", "license": "MIT"}, "node_modules/to-regex-range": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/touch": {"version": "3.1.1", "dev": true, "license": "ISC", "bin": {"nodetouch": "bin/nodetouch.js"}}, "node_modules/tough-cookie": {"version": "4.1.4", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "universalify": "^0.2.0", "url-parse": "^1.5.3"}, "engines": {"node": ">=6"}}, "node_modules/tough-cookie/node_modules/punycode": {"version": "2.3.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/tr46": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/tr46/-/tr46-3.0.0.tgz", "integrity": "sha512-l7FvfAHlcmulp8kr+flpQZmVwtu7nfRV7NZujtN0OqES8EL4O4e0qqzL0DC5gAvx/ZC/9lk6rhcUwYvkBnBnYA==", "license": "MIT", "dependencies": {"punycode": "^2.1.1"}, "engines": {"node": ">=12"}}, "node_modules/tr46/node_modules/punycode": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/truffle": {"version": "5.11.5", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"@truffle/db-loader": "^0.2.36", "@truffle/debugger": "^12.1.5", "app-module-path": "^2.2.0", "ganache": "7.9.1", "mocha": "10.1.0", "original-require": "^1.0.1"}, "bin": {"truffle": "build/cli.bundled.js"}, "engines": {"node": "^16.20 || ^18.16 || >=20"}, "optionalDependencies": {"@truffle/db": "^2.0.36"}}, "node_modules/tslib": {"version": "2.4.1", "dev": true, "license": "0BSD", "optional": true}, "node_modules/tunnel-agent": {"version": "0.6.0", "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/tweetnacl": {"version": "0.14.5", "license": "Unlicense"}, "node_modules/type": {"version": "2.7.3", "license": "ISC"}, "node_modules/type-detect": {"version": "4.0.8", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/type-fest": {"version": "0.21.3", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/type-is": {"version": "1.6.18", "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/typed-array-buffer": {"version": "1.0.3", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}}, "node_modules/typedarray": {"version": "0.0.6", "license": "MIT"}, "node_modules/typedarray-to-buffer": {"version": "3.1.5", "license": "MIT", "dependencies": {"is-typedarray": "^1.0.0"}}, "node_modules/typescript": {"version": "5.8.3", "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz", "integrity": "sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==", "license": "Apache-2.0", "peer": true, "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/typescript-compare": {"version": "0.0.2", "dev": true, "license": "MIT", "dependencies": {"typescript-logic": "^0.0.0"}}, "node_modules/typescript-logic": {"version": "0.0.0", "dev": true, "license": "MIT"}, "node_modules/typescript-tuple": {"version": "2.2.1", "dev": true, "license": "MIT", "dependencies": {"typescript-compare": "^0.0.2"}}, "node_modules/ultron": {"version": "1.1.1", "license": "MIT"}, "node_modules/undefsafe": {"version": "2.0.5", "dev": true, "license": "MIT"}, "node_modules/undici": {"version": "7.10.0", "license": "MIT", "engines": {"node": ">=20.18.1"}}, "node_modules/undici-types": {"version": "7.8.0", "license": "MIT"}, "node_modules/universalify": {"version": "0.2.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">= 4.0.0"}}, "node_modules/unpipe": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/update-browserslist-db": {"version": "1.1.3", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/upper-case": {"version": "1.1.3", "license": "MIT"}, "node_modules/upper-case-first": {"version": "1.1.2", "license": "MIT", "dependencies": {"upper-case": "^1.1.1"}}, "node_modules/uri-js": {"version": "4.4.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/url-parse": {"version": "1.5.10", "dev": true, "license": "MIT", "optional": true, "dependencies": {"querystringify": "^2.1.1", "requires-port": "^1.0.0"}}, "node_modules/url-set-query": {"version": "1.0.0", "license": "MIT"}, "node_modules/utf-8-validate": {"version": "5.0.10", "hasInstallScript": true, "license": "MIT", "dependencies": {"node-gyp-build": "^4.3.0"}, "engines": {"node": ">=6.14.2"}}, "node_modules/utf-8-validate/node_modules/node-gyp-build": {"version": "4.8.4", "license": "MIT", "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}}, "node_modules/utf8": {"version": "3.0.0", "license": "MIT"}, "node_modules/util": {"version": "0.12.5", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "is-arguments": "^1.0.4", "is-generator-function": "^1.0.7", "is-typed-array": "^1.1.3", "which-typed-array": "^1.1.2"}}, "node_modules/util-deprecate": {"version": "1.0.2", "license": "MIT"}, "node_modules/utils-merge": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "2.0.1"}, "node_modules/v8-to-istanbul": {"version": "9.3.0", "dev": true, "license": "ISC", "dependencies": {"@jridgewell/trace-mapping": "^0.3.12", "@types/istanbul-lib-coverage": "^2.0.1", "convert-source-map": "^2.0.0"}, "engines": {"node": ">=10.12.0"}}, "node_modules/validate-npm-package-license": {"version": "3.0.4", "license": "Apache-2.0", "dependencies": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "node_modules/value-or-promise": {"version": "1.0.11", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=12"}}, "node_modules/varint": {"version": "5.0.2", "license": "MIT"}, "node_modules/vary": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/verror": {"version": "1.10.0", "engines": ["node >=0.6.0"], "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "node_modules/verror/node_modules/core-util-is": {"version": "1.0.2", "license": "MIT"}, "node_modules/vuvuzela": {"version": "1.0.3", "dev": true, "license": "Apache-2.0", "optional": true}, "node_modules/walker": {"version": "1.0.8", "dev": true, "license": "Apache-2.0", "dependencies": {"makeerror": "1.0.12"}}, "node_modules/web-streams-polyfill": {"version": "3.3.3", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/web3": {"version": "4.16.0", "license": "LGPL-3.0", "dependencies": {"web3-core": "^4.7.1", "web3-errors": "^1.3.1", "web3-eth": "^4.11.1", "web3-eth-abi": "^4.4.1", "web3-eth-accounts": "^4.3.1", "web3-eth-contract": "^4.7.2", "web3-eth-ens": "^4.4.0", "web3-eth-iban": "^4.0.7", "web3-eth-personal": "^4.1.0", "web3-net": "^4.1.0", "web3-providers-http": "^4.2.0", "web3-providers-ws": "^4.0.8", "web3-rpc-methods": "^1.3.0", "web3-rpc-providers": "^1.0.0-rc.4", "web3-types": "^1.10.0", "web3-utils": "^4.3.3", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14.0.0", "npm": ">=6.12.0"}}, "node_modules/web3-bzz": {"version": "1.10.0", "hasInstallScript": true, "license": "LGPL-3.0", "dependencies": {"@types/node": "^12.12.6", "got": "12.1.0", "swarm-js": "^0.1.40"}, "engines": {"node": ">=8.0.0"}}, "node_modules/web3-bzz/node_modules/@types/node": {"version": "12.20.55", "license": "MIT"}, "node_modules/web3-core": {"version": "4.7.1", "license": "LGPL-3.0", "dependencies": {"web3-errors": "^1.3.1", "web3-eth-accounts": "^4.3.1", "web3-eth-iban": "^4.0.7", "web3-providers-http": "^4.2.0", "web3-providers-ws": "^4.0.8", "web3-types": "^1.10.0", "web3-utils": "^4.3.3", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}, "optionalDependencies": {"web3-providers-ipc": "^4.0.7"}}, "node_modules/web3-core-helpers": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"web3-eth-iban": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/web3-core-helpers/node_modules/bn.js": {"version": "5.2.2", "license": "MIT"}, "node_modules/web3-core-helpers/node_modules/web3-eth-iban": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"bn.js": "^5.2.1", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/web3-core-method": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"@ethersproject/transactions": "^5.6.2", "web3-core-helpers": "1.10.0", "web3-core-promievent": "1.10.0", "web3-core-subscriptions": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/web3-core-promievent": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"eventemitter3": "4.0.4"}, "engines": {"node": ">=8.0.0"}}, "node_modules/web3-core-requestmanager": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"util": "^0.12.5", "web3-core-helpers": "1.10.0", "web3-providers-http": "1.10.0", "web3-providers-ipc": "1.10.0", "web3-providers-ws": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/web3-core-requestmanager/node_modules/cross-fetch": {"version": "3.2.0", "license": "MIT", "dependencies": {"node-fetch": "^2.7.0"}}, "node_modules/web3-core-requestmanager/node_modules/node-fetch": {"version": "2.7.0", "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/web3-core-requestmanager/node_modules/tr46": {"version": "0.0.3", "license": "MIT"}, "node_modules/web3-core-requestmanager/node_modules/web3-providers-http": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"abortcontroller-polyfill": "^1.7.3", "cross-fetch": "^3.1.4", "es6-promise": "^4.2.8", "web3-core-helpers": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/web3-core-requestmanager/node_modules/web3-providers-ipc": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"oboe": "2.1.5", "web3-core-helpers": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/web3-core-requestmanager/node_modules/web3-providers-ws": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"eventemitter3": "4.0.4", "web3-core-helpers": "1.10.0", "websocket": "^1.0.32"}, "engines": {"node": ">=8.0.0"}}, "node_modules/web3-core-requestmanager/node_modules/webidl-conversions": {"version": "3.0.1", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/web3-core-requestmanager/node_modules/whatwg-url": {"version": "5.0.0", "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/web3-core-subscriptions": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"eventemitter3": "4.0.4", "web3-core-helpers": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/web3-core/node_modules/eventemitter3": {"version": "5.0.1", "license": "MIT"}, "node_modules/web3-core/node_modules/web3-utils": {"version": "4.3.3", "license": "LGPL-3.0", "dependencies": {"ethereum-cryptography": "^2.0.0", "eventemitter3": "^5.0.1", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-errors": {"version": "1.3.1", "license": "LGPL-3.0", "dependencies": {"web3-types": "^1.10.0"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth": {"version": "4.11.1", "license": "LGPL-3.0", "dependencies": {"setimmediate": "^1.0.5", "web3-core": "^4.7.1", "web3-errors": "^1.3.1", "web3-eth-abi": "^4.4.1", "web3-eth-accounts": "^4.3.1", "web3-net": "^4.1.0", "web3-providers-ws": "^4.0.8", "web3-rpc-methods": "^1.3.0", "web3-types": "^1.10.0", "web3-utils": "^4.3.3", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth-abi": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"@ethersproject/abi": "^5.6.3", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/web3-eth-accounts": {"version": "4.3.1", "license": "LGPL-3.0", "dependencies": {"@ethereumjs/rlp": "^4.0.1", "crc-32": "^1.2.2", "ethereum-cryptography": "^2.0.0", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-utils": "^4.3.3", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth-accounts/node_modules/eventemitter3": {"version": "5.0.1", "license": "MIT"}, "node_modules/web3-eth-accounts/node_modules/web3-utils": {"version": "4.3.3", "license": "LGPL-3.0", "dependencies": {"ethereum-cryptography": "^2.0.0", "eventemitter3": "^5.0.1", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth-contract": {"version": "4.7.2", "license": "LGPL-3.0", "dependencies": {"@ethereumjs/rlp": "^5.0.2", "web3-core": "^4.7.1", "web3-errors": "^1.3.1", "web3-eth": "^4.11.1", "web3-eth-abi": "^4.4.1", "web3-types": "^1.10.0", "web3-utils": "^4.3.3", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth-contract/node_modules/@ethereumjs/rlp": {"version": "5.0.2", "license": "MPL-2.0", "bin": {"rlp": "bin/rlp.cjs"}, "engines": {"node": ">=18"}}, "node_modules/web3-eth-contract/node_modules/eventemitter3": {"version": "5.0.1", "license": "MIT"}, "node_modules/web3-eth-contract/node_modules/web3-eth-abi": {"version": "4.4.1", "license": "LGPL-3.0", "dependencies": {"abitype": "0.7.1", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-utils": "^4.3.3", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth-contract/node_modules/web3-utils": {"version": "4.3.3", "license": "LGPL-3.0", "dependencies": {"ethereum-cryptography": "^2.0.0", "eventemitter3": "^5.0.1", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth-ens": {"version": "4.4.0", "license": "LGPL-3.0", "dependencies": {"@adraffy/ens-normalize": "^1.8.8", "web3-core": "^4.5.0", "web3-errors": "^1.2.0", "web3-eth": "^4.8.0", "web3-eth-contract": "^4.5.0", "web3-net": "^4.1.0", "web3-types": "^1.7.0", "web3-utils": "^4.3.0", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth-ens/node_modules/eventemitter3": {"version": "5.0.1", "license": "MIT"}, "node_modules/web3-eth-ens/node_modules/web3-utils": {"version": "4.3.3", "license": "LGPL-3.0", "dependencies": {"ethereum-cryptography": "^2.0.0", "eventemitter3": "^5.0.1", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth-iban": {"version": "4.0.7", "license": "LGPL-3.0", "dependencies": {"web3-errors": "^1.1.3", "web3-types": "^1.3.0", "web3-utils": "^4.0.7", "web3-validator": "^2.0.3"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth-iban/node_modules/eventemitter3": {"version": "5.0.1", "license": "MIT"}, "node_modules/web3-eth-iban/node_modules/web3-utils": {"version": "4.3.3", "license": "LGPL-3.0", "dependencies": {"ethereum-cryptography": "^2.0.0", "eventemitter3": "^5.0.1", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth-personal": {"version": "4.1.0", "license": "LGPL-3.0", "dependencies": {"web3-core": "^4.6.0", "web3-eth": "^4.9.0", "web3-rpc-methods": "^1.3.0", "web3-types": "^1.8.0", "web3-utils": "^4.3.1", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth-personal/node_modules/eventemitter3": {"version": "5.0.1", "license": "MIT"}, "node_modules/web3-eth-personal/node_modules/web3-utils": {"version": "4.3.3", "license": "LGPL-3.0", "dependencies": {"ethereum-cryptography": "^2.0.0", "eventemitter3": "^5.0.1", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth/node_modules/eventemitter3": {"version": "5.0.1", "license": "MIT"}, "node_modules/web3-eth/node_modules/setimmediate": {"version": "1.0.5", "license": "MIT"}, "node_modules/web3-eth/node_modules/web3-eth-abi": {"version": "4.4.1", "license": "LGPL-3.0", "dependencies": {"abitype": "0.7.1", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-utils": "^4.3.3", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth/node_modules/web3-utils": {"version": "4.3.3", "license": "LGPL-3.0", "dependencies": {"ethereum-cryptography": "^2.0.0", "eventemitter3": "^5.0.1", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-net": {"version": "4.1.0", "license": "LGPL-3.0", "dependencies": {"web3-core": "^4.4.0", "web3-rpc-methods": "^1.3.0", "web3-types": "^1.6.0", "web3-utils": "^4.3.0"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-net/node_modules/eventemitter3": {"version": "5.0.1", "license": "MIT"}, "node_modules/web3-net/node_modules/web3-utils": {"version": "4.3.3", "license": "LGPL-3.0", "dependencies": {"ethereum-cryptography": "^2.0.0", "eventemitter3": "^5.0.1", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-providers-http": {"version": "4.2.0", "license": "LGPL-3.0", "dependencies": {"cross-fetch": "^4.0.0", "web3-errors": "^1.3.0", "web3-types": "^1.7.0", "web3-utils": "^4.3.1"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-providers-http/node_modules/eventemitter3": {"version": "5.0.1", "license": "MIT"}, "node_modules/web3-providers-http/node_modules/web3-utils": {"version": "4.3.3", "license": "LGPL-3.0", "dependencies": {"ethereum-cryptography": "^2.0.0", "eventemitter3": "^5.0.1", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-providers-ipc": {"version": "4.0.7", "license": "LGPL-3.0", "optional": true, "dependencies": {"web3-errors": "^1.1.3", "web3-types": "^1.3.0", "web3-utils": "^4.0.7"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-providers-ipc/node_modules/eventemitter3": {"version": "5.0.1", "license": "MIT", "optional": true}, "node_modules/web3-providers-ipc/node_modules/web3-utils": {"version": "4.3.3", "license": "LGPL-3.0", "optional": true, "dependencies": {"ethereum-cryptography": "^2.0.0", "eventemitter3": "^5.0.1", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-providers-ws": {"version": "4.0.8", "license": "LGPL-3.0", "dependencies": {"@types/ws": "8.5.3", "isomorphic-ws": "^5.0.0", "web3-errors": "^1.2.0", "web3-types": "^1.7.0", "web3-utils": "^4.3.1", "ws": "^8.17.1"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-providers-ws/node_modules/eventemitter3": {"version": "5.0.1", "license": "MIT"}, "node_modules/web3-providers-ws/node_modules/isomorphic-ws": {"version": "5.0.0", "license": "MIT", "peerDependencies": {"ws": "*"}}, "node_modules/web3-providers-ws/node_modules/web3-utils": {"version": "4.3.3", "license": "LGPL-3.0", "dependencies": {"ethereum-cryptography": "^2.0.0", "eventemitter3": "^5.0.1", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-providers-ws/node_modules/ws": {"version": "8.18.2", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/web3-rpc-methods": {"version": "1.3.0", "license": "LGPL-3.0", "dependencies": {"web3-core": "^4.4.0", "web3-types": "^1.6.0", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-rpc-providers": {"version": "1.0.0-rc.4", "license": "LGPL-3.0", "dependencies": {"web3-errors": "^1.3.1", "web3-providers-http": "^4.2.0", "web3-providers-ws": "^4.0.8", "web3-types": "^1.10.0", "web3-utils": "^4.3.3", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-rpc-providers/node_modules/eventemitter3": {"version": "5.0.1", "license": "MIT"}, "node_modules/web3-rpc-providers/node_modules/web3-utils": {"version": "4.3.3", "license": "LGPL-3.0", "dependencies": {"ethereum-cryptography": "^2.0.0", "eventemitter3": "^5.0.1", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-shh": {"version": "1.10.0", "hasInstallScript": true, "license": "LGPL-3.0", "dependencies": {"web3-core": "1.10.0", "web3-core-method": "1.10.0", "web3-core-subscriptions": "1.10.0", "web3-net": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/web3-shh/node_modules/@types/node": {"version": "12.20.55", "license": "MIT"}, "node_modules/web3-shh/node_modules/bignumber.js": {"version": "9.3.0", "license": "MIT", "engines": {"node": "*"}}, "node_modules/web3-shh/node_modules/web3-core": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"@types/bn.js": "^5.1.1", "@types/node": "^12.12.6", "bignumber.js": "^9.0.0", "web3-core-helpers": "1.10.0", "web3-core-method": "1.10.0", "web3-core-requestmanager": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/web3-shh/node_modules/web3-net": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"web3-core": "1.10.0", "web3-core-method": "1.10.0", "web3-utils": "1.10.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/web3-types": {"version": "1.10.0", "license": "LGPL-3.0", "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-utils": {"version": "1.10.0", "license": "LGPL-3.0", "dependencies": {"bn.js": "^5.2.1", "ethereum-bloom-filters": "^1.0.6", "ethereumjs-util": "^7.1.0", "ethjs-unit": "0.1.6", "number-to-bn": "1.7.0", "randombytes": "^2.1.0", "utf8": "3.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/web3-utils/node_modules/bn.js": {"version": "5.2.2", "license": "MIT"}, "node_modules/web3-validator": {"version": "2.0.6", "license": "LGPL-3.0", "dependencies": {"ethereum-cryptography": "^2.0.0", "util": "^0.12.5", "web3-errors": "^1.2.0", "web3-types": "^1.6.0", "zod": "^3.21.4"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3/node_modules/eventemitter3": {"version": "5.0.1", "license": "MIT"}, "node_modules/web3/node_modules/web3-eth-abi": {"version": "4.4.1", "license": "LGPL-3.0", "dependencies": {"abitype": "0.7.1", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-utils": "^4.3.3", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3/node_modules/web3-utils": {"version": "4.3.3", "license": "LGPL-3.0", "dependencies": {"ethereum-cryptography": "^2.0.0", "eventemitter3": "^5.0.1", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/webidl-conversions": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-7.0.0.tgz", "integrity": "sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}}, "node_modules/websocket": {"version": "1.0.35", "license": "Apache-2.0", "dependencies": {"bufferutil": "^4.0.1", "debug": "^2.2.0", "es5-ext": "^0.10.63", "typedarray-to-buffer": "^3.1.5", "utf-8-validate": "^5.0.2", "yaeti": "^0.0.6"}, "engines": {"node": ">=4.0.0"}}, "node_modules/websocket/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/websocket/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/whatwg-encoding": {"version": "3.1.1", "license": "MIT", "dependencies": {"iconv-lite": "0.6.3"}, "engines": {"node": ">=18"}}, "node_modules/whatwg-mimetype": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/whatwg-url": {"version": "11.0.0", "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-11.0.0.tgz", "integrity": "sha512-RKT8HExMpoYx4igMiVMY83lN6UeITKJlBQ+vR/8ZJ8OCdSiN3RwCq+9gH0+Xzj0+5IrM6i4j/6LuvzbZIQgEcQ==", "license": "MIT", "dependencies": {"tr46": "^3.0.0", "webidl-conversions": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/which": {"version": "2.0.2", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/which-module": {"version": "1.0.0", "license": "ISC"}, "node_modules/which-typed-array": {"version": "1.1.19", "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/window-size": {"version": "0.2.0", "license": "MIT", "bin": {"window-size": "cli.js"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/workerpool": {"version": "6.2.1", "dev": true, "license": "Apache-2.0"}, "node_modules/wrap-ansi": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs": {"name": "wrap-ansi", "version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs/node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/wrap-ansi-cjs/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/wrap-ansi-cjs/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/wrap-ansi-cjs/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/wrap-ansi-cjs/node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/wrap-ansi/node_modules/ansi-regex": {"version": "5.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/wrap-ansi/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/wrap-ansi/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/wrap-ansi/node_modules/strip-ansi": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/wrappy": {"version": "1.0.2", "license": "ISC"}, "node_modules/write-file-atomic": {"version": "4.0.2", "dev": true, "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/write-stream": {"version": "0.4.3", "dev": true, "optional": true, "dependencies": {"readable-stream": "~0.0.2"}}, "node_modules/write-stream/node_modules/readable-stream": {"version": "0.0.4", "dev": true, "license": "BSD", "optional": true}, "node_modules/ws": {"version": "7.5.10", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xhr": {"version": "2.6.0", "license": "MIT", "dependencies": {"global": "~4.4.0", "is-function": "^1.0.1", "parse-headers": "^2.0.0", "xtend": "^4.0.0"}}, "node_modules/xhr-request": {"version": "1.1.0", "license": "MIT", "dependencies": {"buffer-to-arraybuffer": "^0.0.5", "object-assign": "^4.1.1", "query-string": "^5.0.1", "simple-get": "^2.7.0", "timed-out": "^4.0.1", "url-set-query": "^1.0.0", "xhr": "^2.0.4"}}, "node_modules/xhr-request-promise": {"version": "0.1.3", "license": "MIT", "dependencies": {"xhr-request": "^1.1.0"}}, "node_modules/xmlhttprequest": {"version": "1.8.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/xss": {"version": "1.0.15", "dev": true, "license": "MIT", "optional": true, "dependencies": {"commander": "^2.20.3", "cssfilter": "0.0.10"}, "bin": {"xss": "bin/xss"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/xtend": {"version": "4.0.2", "license": "MIT", "engines": {"node": ">=0.4"}}, "node_modules/y18n": {"version": "5.0.8", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yaeti": {"version": "0.0.6", "license": "MIT", "engines": {"node": ">=0.10.32"}}, "node_modules/yallist": {"version": "3.1.1", "license": "ISC"}, "node_modules/yargs": {"version": "17.7.2", "dev": true, "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "20.2.4", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yargs-unparser": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"camelcase": "^6.0.0", "decamelize": "^4.0.0", "flat": "^5.0.2", "is-plain-obj": "^2.1.0"}, "engines": {"node": ">=10"}}, "node_modules/yargs-unparser/node_modules/camelcase": {"version": "6.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/yargs/node_modules/yargs-parser": {"version": "21.1.1", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/yocto-queue": {"version": "0.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/zip-stream": {"version": "6.0.1", "license": "MIT", "dependencies": {"archiver-utils": "^5.0.0", "compress-commons": "^6.0.2", "readable-stream": "^4.0.0"}, "engines": {"node": ">= 14"}}, "node_modules/zip-stream/node_modules/buffer": {"version": "6.0.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/zip-stream/node_modules/readable-stream": {"version": "4.7.0", "license": "MIT", "dependencies": {"abort-controller": "^3.0.0", "buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "string_decoder": "^1.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/zip-stream/node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/zod": {"version": "3.25.67", "license": "MIT", "funding": {"url": "https://github.com/sponsors/colinhacks"}}}}