// Admin Panel Management
class AdminManager {
    constructor() {
        this.currentPage = 1;
        this.currentSearch = '';
        this.currentRole = '';
        this.currentStatus = '';
        this.editingUserId = null;
        this.statsInterval = null;
        this.blockchainInterval = null;
        this.isMobile = window.innerWidth <= 768;
        this.isPinned = true; // Admin sidebar is pinned by default
        this.isCollapsed = false; // Admin sidebar is expanded by default
        this.isInitialized = false;

        // Delay initialization to ensure DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    init() {
        try {
            this.setupEventListeners();
            this.setupResponsive();
            this.setupPinning();
            this.isInitialized = true;

            console.log('AdminManager initialized successfully');
        } catch (error) {
            console.error('AdminManager initialization error:', error);
        }
    }

    // Lazy load data when admin panel is actually shown
    initializeData() {
        if (!this.isInitialized) {
            console.warn('AdminManager not initialized yet');
            return;
        }

        this.showLoadingState();
        this.startRealTimeSync();

        // Load data with staggered timing for better UX
        setTimeout(() => this.loadUserStats(), 100);
        setTimeout(() => this.loadUsers(), 200);
        setTimeout(() => this.loadBlockchainHealth(), 300);
        setTimeout(() => this.hideLoadingState(), 1000);
    }

    showLoadingState() {
        // Add loading indicators to stat cards
        const statCards = document.querySelectorAll('.stat-card h4');
        statCards.forEach(card => {
            card.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        });

        // Add loading to table
        const tableBody = document.getElementById('users-table-body');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" style="text-align: center; padding: 2rem;">
                        <i class="fas fa-spinner fa-spin fa-2x" style="color: #667eea;"></i>
                        <p style="margin-top: 1rem; color: #7f8c8d;">Loading users...</p>
                    </td>
                </tr>
            `;
        }
    }

    hideLoadingState() {
        // This will be called after data is loaded
        console.log('Admin panel data loaded successfully');
    }

    setupEventListeners() {
        // Admin navigation switching
        document.querySelectorAll('.admin-nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                this.switchTab(e.currentTarget.dataset.tab);
            });
        });

        // User search
        const searchInput = document.getElementById('user-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.currentSearch = e.target.value;
                this.loadUsers();
            });
        }

        // Role filter
        const roleFilter = document.getElementById('role-filter');
        if (roleFilter) {
            roleFilter.addEventListener('change', (e) => {
                this.currentRole = e.target.value;
                this.loadUsers();
            });
        }

        // Status filter
        const statusFilter = document.getElementById('status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.currentStatus = e.target.value;
                this.loadUsers();
            });
        }

        // Select all users checkbox
        const selectAllCheckbox = document.getElementById('select-all-users');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.toggleSelectAllUsers(e.target.checked);
            });
        }

        // Bulk actions
        const bulkActionSelect = document.getElementById('bulk-action-select');
        const applyBulkActionBtn = document.getElementById('apply-bulk-action');
        if (bulkActionSelect && applyBulkActionBtn) {
            applyBulkActionBtn.addEventListener('click', () => {
                this.applyBulkAction(bulkActionSelect.value);
            });
        }

        // Add user button
        const addUserBtn = document.getElementById('add-user-btn');
        if (addUserBtn) {
            addUserBtn.addEventListener('click', () => this.showAddUserModal());
        }

        // User form submission
        const userForm = document.getElementById('user-form');
        if (userForm) {
            userForm.addEventListener('submit', (e) => this.handleUserSubmit(e));
        }

        // Enhanced user management buttons
        const resetPasswordBtn = document.getElementById('reset-password-btn');
        if (resetPasswordBtn) {
            resetPasswordBtn.addEventListener('click', () => this.resetUserPassword());
        }

        const sendWelcomeEmailBtn = document.getElementById('send-welcome-email-btn');
        if (sendWelcomeEmailBtn) {
            sendWelcomeEmailBtn.addEventListener('click', () => this.sendWelcomeEmail());
        }

        const viewLoginHistoryBtn = document.getElementById('view-login-history-btn');
        if (viewLoginHistoryBtn) {
            viewLoginHistoryBtn.addEventListener('click', () => this.viewLoginHistory());
        }

        // Mobile toggle
        const mobileToggle = document.getElementById('admin-mobile-toggle');
        if (mobileToggle) {
            mobileToggle.addEventListener('click', () => this.toggleMobileSidebar());
        }

        // Mobile overlay
        const overlay = document.getElementById('admin-overlay');
        if (overlay) {
            overlay.addEventListener('click', () => this.closeMobileSidebar());
        }

        // Window resize
        window.addEventListener('resize', () => this.handleResize());
    }

    setupPinning() {
        // Pin button functionality
        const pinButton = document.getElementById('admin-sidebar-pin');
        if (pinButton) {
            pinButton.addEventListener('click', () => this.togglePin());
        }

        // Toggle button functionality
        const toggleButton = document.getElementById('admin-sidebar-toggle');
        if (toggleButton) {
            toggleButton.addEventListener('click', () => this.toggleCollapse());
        }

        // Auto-pin on admin page load
        this.setPinned(true);

        // Load saved collapse state
        const savedCollapseState = localStorage.getItem('adminSidebarCollapsed');
        if (savedCollapseState !== null) {
            this.setCollapsed(savedCollapseState === 'true');
        }
    }

    togglePin() {
        this.isPinned = !this.isPinned;
        this.setPinned(this.isPinned);
    }

    setPinned(pinned) {
        const sidebar = document.getElementById('admin-sidebar');
        const pinButton = document.getElementById('admin-sidebar-pin');

        if (sidebar && pinButton) {
            if (pinned) {
                sidebar.classList.add('pinned');
                pinButton.classList.add('pinned');
                pinButton.title = 'Unpin Sidebar';
                pinButton.innerHTML = '<i class="fas fa-thumbtack"></i>';
            } else {
                sidebar.classList.remove('pinned');
                pinButton.classList.remove('pinned');
                pinButton.title = 'Pin Sidebar';
                pinButton.innerHTML = '<i class="far fa-thumbtack"></i>';
            }
        }

        this.isPinned = pinned;

        // Save preference
        localStorage.setItem('adminSidebarPinned', pinned.toString());
    }

    toggleCollapse() {
        this.isCollapsed = !this.isCollapsed;
        this.setCollapsed(this.isCollapsed);
    }

    setCollapsed(collapsed) {
        const sidebar = document.getElementById('admin-sidebar');
        const toggleButton = document.getElementById('admin-sidebar-toggle');
        const mainContent = document.querySelector('.admin-main-content');
        const body = document.body;

        if (sidebar && toggleButton) {
            if (collapsed) {
                sidebar.classList.add('collapsed');
                toggleButton.classList.add('collapsed');
                toggleButton.title = 'Expand Sidebar';
                toggleButton.innerHTML = '<i class="fas fa-chevron-right"></i>';

                // Adjust main content
                if (mainContent) {
                    mainContent.classList.add('sidebar-collapsed');
                }

                // Adjust body class for global layout
                if (body.classList.contains('admin-has-sidebar')) {
                    body.classList.add('sidebar-collapsed');
                }
            } else {
                sidebar.classList.remove('collapsed');
                toggleButton.classList.remove('collapsed');
                toggleButton.title = 'Collapse Sidebar';
                toggleButton.innerHTML = '<i class="fas fa-chevron-left"></i>';

                // Adjust main content
                if (mainContent) {
                    mainContent.classList.remove('sidebar-collapsed');
                }

                // Adjust body class for global layout
                body.classList.remove('sidebar-collapsed');
            }
        }

        this.isCollapsed = collapsed;

        // Save preference
        localStorage.setItem('adminSidebarCollapsed', collapsed.toString());

        // Trigger a custom event for other components to respond
        window.dispatchEvent(new CustomEvent('sidebarToggle', {
            detail: { collapsed: collapsed }
        }));
    }

    setupResponsive() {
        this.handleResize();

        // Load saved pin preference
        const savedPinState = localStorage.getItem('adminSidebarPinned');
        if (savedPinState !== null) {
            this.setPinned(savedPinState === 'true');
        }

        // Load saved collapse preference
        const savedCollapseState = localStorage.getItem('adminSidebarCollapsed');
        if (savedCollapseState !== null) {
            this.setCollapsed(savedCollapseState === 'true');
        }
    }

    handleResize() {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 768;

        const mobileHeader = document.querySelector('.admin-mobile-header');
        const sidebar = document.querySelector('.admin-sidebar');
        const body = document.body;

        if (this.isMobile) {
            if (mobileHeader) mobileHeader.style.display = 'flex';
            if (sidebar) {
                sidebar.classList.remove('mobile-open');
                // On mobile, always show expanded sidebar when opened
                sidebar.classList.remove('collapsed');
            }
            // Remove collapsed class from body on mobile
            body.classList.remove('sidebar-collapsed');
            this.closeMobileSidebar();
        } else {
            if (mobileHeader) mobileHeader.style.display = 'none';
            if (sidebar) {
                sidebar.classList.remove('mobile-open');
                sidebar.style.transform = '';
                // Restore collapsed state on desktop
                if (this.isCollapsed) {
                    sidebar.classList.add('collapsed');
                    body.classList.add('sidebar-collapsed');
                }
            }
            this.closeMobileSidebar();
        }
    }

    toggleMobileSidebar() {
        const sidebar = document.querySelector('.admin-sidebar');
        const overlay = document.getElementById('admin-overlay');

        if (sidebar && overlay) {
            sidebar.classList.toggle('mobile-open');
            overlay.classList.toggle('show');
        }
    }

    closeMobileSidebar() {
        const sidebar = document.querySelector('.admin-sidebar');
        const overlay = document.getElementById('admin-overlay');

        if (sidebar) sidebar.classList.remove('mobile-open');
        if (overlay) overlay.classList.remove('show');
    }

        // Blockchain health refresh
        const refreshBlockchainBtn = document.getElementById('refresh-blockchain-health');
        if (refreshBlockchainBtn) {
            refreshBlockchainBtn.addEventListener('click', () => this.loadBlockchainHealth());
        }

        // Logs refresh
        const refreshLogsBtn = document.getElementById('refresh-logs');
        if (refreshLogsBtn) {
            refreshLogsBtn.addEventListener('click', () => this.loadRealTimeLogs());
        }

        // Auto refresh toggle
        const autoRefreshBtn = document.getElementById('auto-refresh-toggle');
        if (autoRefreshBtn) {
            autoRefreshBtn.addEventListener('click', () => this.toggleAutoRefresh());
        }

        // Export case button
        const exportCaseBtn = document.getElementById('export-case-btn');
        if (exportCaseBtn) {
            exportCaseBtn.addEventListener('click', () => this.exportCase());
        }

        // Create sample evidence button
        const createSampleBtn = document.getElementById('create-sample-evidence-btn');
        if (createSampleBtn) {
            createSampleBtn.addEventListener('click', () => this.createSampleEvidence());
        }
    }

    switchTab(tabName) {
        // Add click animation to the clicked item
        const clickedItem = document.querySelector(`[data-tab="${tabName}"]`);
        if (clickedItem) {
            clickedItem.style.transform = 'scale(0.95)';
            setTimeout(() => {
                clickedItem.style.transform = '';
            }, 150);
        }

        // Update admin navigation items with animation
        document.querySelectorAll('.admin-nav-item').forEach(item => {
            item.classList.remove('active');
            item.style.transform = '';
        });

        setTimeout(() => {
            if (clickedItem) {
                clickedItem.classList.add('active');
                clickedItem.style.transform = 'translateX(5px)';
            }
        }, 100);

        // Update tab content with fade effect
        document.querySelectorAll('.tab-content').forEach(content => {
            content.style.opacity = '0';
            setTimeout(() => {
                content.classList.remove('active');
            }, 200);
        });

        setTimeout(() => {
            const targetTab = document.getElementById(`${tabName}-tab`);
            if (targetTab) {
                targetTab.classList.add('active');
                targetTab.style.opacity = '1';
            }
        }, 250);

        // Show loading state for the new tab
        this.showTabLoadingState(tabName);

        // Load content based on tab with delay for smooth transition
        setTimeout(() => {
            switch (tabName) {
                case 'users':
                    this.loadUsers();
                    this.loadUserStats();
                    break;
                case 'stats':
                    this.loadStats();
                    break;
                case 'blockchain':
                    this.loadBlockchainHealth();
                    break;
                case 'logs':
                    this.loadRealTimeLogs();
                    break;
                case 'export':
                    // Export tab doesn't need initial loading
                    break;
                case 'settings':
                    this.loadSettings();
                    break;
            }
        }, 300);
    }

    showTabLoadingState(tabName) {
        const tabContent = document.getElementById(`${tabName}-tab`);
        if (tabContent && tabName === 'users') {
            // Show loading for users tab
            this.showLoadingState();
        }
    }

    async loadUsers() {
        try {
            showLoading();

            // Try to load from API first, fallback to mock data
            try {
                const response = await api.getUsers(
                    this.currentPage,
                    10,
                    this.currentSearch,
                    this.currentRole
                );

                if (response.success) {
                    this.displayUsers(response.users);
                    this.displayPagination(response.pagination);
                    return;
                }
            } catch (apiError) {
                console.log('API not available, using mock data');
            }

            // Fallback to mock data
            const mockUsers = this.getMockUsers();
            this.displayUsers(mockUsers);
            this.displayPagination({ currentPage: 1, totalPages: 1, totalUsers: mockUsers.length });

        } catch (error) {
            console.error('Load users error:', error);
            this.displayUsersError('Error loading users');
        } finally {
            hideLoading();
        }
    }

    getMockUsers() {
        return [
            {
                _id: '1',
                username: 'admin',
                email: '<EMAIL>',
                fullName: 'System Administrator',
                role: 'admin',
                department: 'IT Security',
                isActive: true,
                createdAt: new Date('2025-01-01'),
                lastLogin: new Date()
            },
            {
                _id: '2',
                username: 'officer1',
                email: '<EMAIL>',
                fullName: 'John Smith',
                role: 'police',
                department: 'Criminal Investigation',
                badgeNumber: 'P001',
                isActive: true,
                createdAt: new Date('2025-01-15'),
                lastLogin: new Date(Date.now() - 86400000) // 1 day ago
            },
            {
                _id: '3',
                username: 'lawyer1',
                email: '<EMAIL>',
                fullName: 'Sarah Johnson',
                role: 'lawyer',
                department: 'Criminal Defense',
                barNumber: 'L001',
                isActive: true,
                createdAt: new Date('2025-02-01'),
                lastLogin: new Date(Date.now() - 3600000) // 1 hour ago
            }
        ];
    }

    getMockStats() {
        return {
            users: {
                total: 3,
                byRole: [
                    { _id: 'admin', count: 1 },
                    { _id: 'police', count: 1 },
                    { _id: 'lawyer', count: 1 }
                ]
            },
            evidence: {
                total: 5,
                verified: 5,
                verificationRate: '100.0'
            }
        };
    }

    getMockUserStats() {
        return {
            totalUsers: 3,
            activeUsers: 3,
            adminUsers: 1,
            newUsersThisMonth: 1
        };
    }

    getMockBlockchainHealth() {
        return {
            status: 'Connected',
            contractStatus: 'Active',
            avgGasUsed: 21000,
            eventsEmitted: 5,
            rolesAssigned: { uploader: 2, admin: 1 },
            network: 'development',
            accounts: 10,
            lastUpdated: new Date().toISOString()
        };
    }

    getMockLogs() {
        return {
            accessLogs: [
                {
                    timestamp: new Date().toISOString(),
                    action: 'upload',
                    evidenceId: '12345',
                    user: 'officer1',
                    ipAddress: '127.0.0.1'
                },
                {
                    timestamp: new Date(Date.now() - 3600000).toISOString(),
                    action: 'verify',
                    evidenceId: '12345',
                    user: 'admin',
                    ipAddress: '127.0.0.1'
                }
            ],
            verificationLogs: [
                {
                    timestamp: new Date(Date.now() - 1800000).toISOString(),
                    action: 'verify',
                    evidenceId: '12345',
                    user: 'admin',
                    result: true
                }
            ]
        };
    }

    displayUsersError(message) {
        const tbody = document.getElementById('users-table-body');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">
                        <div class="error-message">
                            <i class="fas fa-exclamation-triangle"></i>
                            <p>${message}</p>
                            <button class="btn btn-primary" onclick="adminManager.loadUsers()">
                                <i class="fas fa-sync-alt"></i> Retry
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }
    }

    displayUsers(users) {
        const tbody = document.getElementById('users-table-body');
        if (!tbody) return;

        tbody.innerHTML = users.map(user => `
            <tr>
                <td>
                    <input type="checkbox" class="user-checkbox" value="${user._id}">
                </td>
                <td>
                    <div class="user-info-cell">
                        <div class="user-avatar">
                            ${user.fullName ? user.fullName.charAt(0).toUpperCase() : 'U'}
                        </div>
                        <div class="user-details">
                            <h4>${user.fullName || 'Unknown'}</h4>
                            <p>${user.email}</p>
                            <p>@${user.username}</p>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="role-badge role-${user.role}">${this.formatRole(user.role)}</span>
                </td>
                <td>${user.department || 'N/A'}</td>
                <td>
                    <span class="status-badge ${user.isActive ? 'active' : 'inactive'}">
                        ${user.isActive ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td>${user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}</td>
                <td>${new Date(user.createdAt).toLocaleDateString()}</td>
                <td>
                    <div class="user-actions">
                        <button class="action-btn edit" onclick="adminManager.editUser('${user._id}')" title="Edit User">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn reset" onclick="adminManager.resetUserPassword('${user._id}')" title="Reset Password">
                            <i class="fas fa-key"></i>
                        </button>
                        <button class="action-btn toggle" onclick="adminManager.toggleUserStatus('${user._id}', ${user.isActive})" title="${user.isActive ? 'Deactivate' : 'Activate'} User">
                            <i class="fas fa-${user.isActive ? 'user-slash' : 'user-check'}"></i>
                        </button>
                        <button class="action-btn delete" onclick="adminManager.deleteUser('${user._id}')" title="Delete User">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        // Update checkbox listeners
        this.updateCheckboxListeners();
    }

    formatRole(role) {
        const roleMap = {
            'admin': 'Administrator',
            'police': 'Police Officer',
            'lawyer': 'Lawyer'
        };
        return roleMap[role] || role;
    }

    updateCheckboxListeners() {
        const checkboxes = document.querySelectorAll('.user-checkbox');
        const selectAllCheckbox = document.getElementById('select-all-users');
        const bulkActionSelect = document.getElementById('bulk-action-select');
        const applyBulkActionBtn = document.getElementById('apply-bulk-action');

        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
                const hasChecked = checkedBoxes.length > 0;

                // Update bulk action controls
                if (bulkActionSelect) bulkActionSelect.disabled = !hasChecked;
                if (applyBulkActionBtn) applyBulkActionBtn.disabled = !hasChecked;

                // Update select all checkbox
                if (selectAllCheckbox) {
                    selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes.length;
                    selectAllCheckbox.checked = checkedBoxes.length === checkboxes.length;
                }
            });
        });
    }

    displayPagination(pagination) {
        const container = document.getElementById('users-pagination');
        if (!container) return;

        const { current, pages, total } = pagination;
        
        let paginationHTML = `<div class="pagination-info">Showing ${total} users</div>`;
        
        if (pages > 1) {
            paginationHTML += '<div class="pagination-buttons">';
            
            // Previous button
            if (current > 1) {
                paginationHTML += `<button onclick="adminManager.changePage(${current - 1})">Previous</button>`;
            }
            
            // Page numbers
            for (let i = Math.max(1, current - 2); i <= Math.min(pages, current + 2); i++) {
                paginationHTML += `<button class="${i === current ? 'active' : ''}" onclick="adminManager.changePage(${i})">${i}</button>`;
            }
            
            // Next button
            if (current < pages) {
                paginationHTML += `<button onclick="adminManager.changePage(${current + 1})">Next</button>`;
            }
            
            paginationHTML += '</div>';
        }
        
        container.innerHTML = paginationHTML;
    }

    changePage(page) {
        this.currentPage = page;
        this.loadUsers();
    }

    async editUser(userId) {
        try {
            // Get user data
            const response = await api.request(`/admin/users`, 'GET');
            const user = response.users.find(u => u._id === userId);
            
            if (!user) {
                showErrorNotification('Error', 'User not found');
                return;
            }

            this.editingUserId = userId;
            document.getElementById('user-modal-title').textContent = 'Edit User';
            document.getElementById('user-submit-text').textContent = 'Update User';
            document.getElementById('password-group').style.display = 'none';
            
            // Fill form with user data
            document.getElementById('user-username').value = user.username;
            document.getElementById('user-email').value = user.email;
            document.getElementById('user-fullname').value = user.fullName;
            document.getElementById('user-role').value = user.role;
            document.getElementById('user-department').value = user.department || '';
            
            document.getElementById('user-modal').classList.remove('hidden');
        } catch (error) {
            console.error('Edit user error:', error);
            showErrorNotification('Error', 'Failed to load user data');
        }
    }



    async toggleUserStatus(userId, activate) {
        try {
            const response = await api.request(`/admin/users/${userId}`, 'PUT', {
                isActive: activate
            });
            
            if (response.success) {
                showSuccessNotification('Success', `User ${activate ? 'activated' : 'deactivated'} successfully`);
                this.loadUsers();
            } else {
                showErrorNotification('Error', response.message);
            }
        } catch (error) {
            console.error('Toggle user status error:', error);
            showErrorNotification('Error', 'Failed to update user status');
        }
    }

    async resetPassword(userId) {
        const newPassword = prompt('Enter new password (minimum 6 characters):');
        if (!newPassword || newPassword.length < 6) {
            showErrorNotification('Error', 'Password must be at least 6 characters long');
            return;
        }

        try {
            const response = await api.request(`/admin/users/${userId}/reset-password`, 'POST', {
                newPassword
            });
            
            if (response.success) {
                showSuccessNotification('Success', 'Password reset successfully');
            } else {
                showErrorNotification('Error', response.message);
            }
        } catch (error) {
            console.error('Reset password error:', error);
            showErrorNotification('Error', 'Failed to reset password');
        }
    }

    async loadStats() {
        try {
            // Try API first, fallback to mock data
            try {
                const response = await api.getStats();
                if (response.success) {
                    this.displayStats(response.stats);
                    return;
                }
            } catch (apiError) {
                console.log('Stats API not available, using mock data');
            }

            // Use mock data
            this.displayStats(this.getMockStats());
        } catch (error) {
            console.error('Load stats error:', error);
            this.displayStatsError('Error loading statistics');
        }
    }

    displayStats(stats) {
        try {
            // Update stat cards with fallbacks
            const totalUsersEl = document.getElementById('total-users');
            const totalEvidenceEl = document.getElementById('total-evidence');
            const verifiedEvidenceEl = document.getElementById('verified-evidence');
            const verificationRateEl = document.getElementById('verification-rate');

            if (totalUsersEl) totalUsersEl.textContent = stats.users?.total || 0;
            if (totalEvidenceEl) totalEvidenceEl.textContent = stats.evidence?.total || 0;
            if (verifiedEvidenceEl) verifiedEvidenceEl.textContent = stats.evidence?.verified || 0;
            if (verificationRateEl) verificationRateEl.textContent = (stats.evidence?.verificationRate || 0) + '%';

            // Display role distribution
            const roleChart = document.getElementById('role-chart');
            if (roleChart && stats.users?.byRole) {
                roleChart.innerHTML = stats.users.byRole.map(role => `
                    <div class="role-stat">
                        <span class="role-badge role-${role._id.toLowerCase()}">${role._id.toUpperCase()}</span>
                        <span class="role-count">${role.count}</span>
                    </div>
                `).join('');
            } else if (roleChart) {
                roleChart.innerHTML = '<p>No role data available</p>';
            }
        } catch (error) {
            console.error('Error displaying stats:', error);
        }

        // Display recent activity
        const activityList = document.getElementById('activity-list');
        if (activityList) {
            try {
                const activities = [];

                // Add user activities
                if (stats.users?.recent) {
                    activities.push(...stats.users.recent.map(user => ({
                        type: 'user',
                        text: `New user: ${user.firstName || 'Unknown'} ${user.lastName || ''}`.trim(),
                        date: user.createdAt
                    })));
                }

                // Add evidence activities
                if (stats.evidence?.recent) {
                    activities.push(...stats.evidence.recent.map(evidence => ({
                        type: 'evidence',
                        text: `Evidence uploaded: ${evidence.originalName || 'Unknown file'}`,
                        date: evidence.uploadedAt
                    })));
                }

                if (activities.length > 0) {
                    const sortedActivities = activities
                        .sort((a, b) => new Date(b.date) - new Date(a.date))
                        .slice(0, 10);

                    activityList.innerHTML = sortedActivities.map(activity => `
                        <div class="activity-item">
                            <i class="fas fa-${activity.type === 'user' ? 'user-plus' : 'upload'}"></i>
                            <span>${activity.text}</span>
                            <small>${formatDate(activity.date)}</small>
                        </div>
                    `).join('');
                } else {
                    activityList.innerHTML = '<p>No recent activity</p>';
                }
            } catch (error) {
                console.error('Error displaying activity:', error);
                activityList.innerHTML = '<p>Error loading activity data</p>';
            }
        }
    }

    displayStatsError(message) {
        // Display error message in stats tab
        const statsTab = document.getElementById('stats-tab');
        if (statsTab) {
            statsTab.innerHTML = `
                <div class="admin-content-header">
                    <h3><i class="fas fa-chart-bar"></i> System Statistics</h3>
                    <p>Overview of system usage and performance metrics</p>
                </div>
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>${message}</p>
                    <button class="btn btn-primary" onclick="adminManager.loadStats()">
                        <i class="fas fa-sync-alt"></i> Retry
                    </button>
                </div>
            `;
        }
    }

    loadSettings() {
        console.log('Loading system settings...');

        // Load current settings from localStorage or defaults
        const settings = {
            maxFileSize: localStorage.getItem('maxFileSize') || '100',
            sessionTimeout: localStorage.getItem('sessionTimeout') || '30',
            twoFactorAuth: localStorage.getItem('twoFactorAuth') === 'true',
            autoBackup: localStorage.getItem('autoBackup') === 'true'
        };

        // Update form fields
        const maxFileSizeInput = document.getElementById('max-file-size');
        const sessionTimeoutInput = document.getElementById('session-timeout');
        const twoFactorToggle = document.getElementById('two-factor-toggle');
        const autoBackupToggle = document.getElementById('auto-backup-toggle');

        if (maxFileSizeInput) maxFileSizeInput.value = settings.maxFileSize;
        if (sessionTimeoutInput) sessionTimeoutInput.value = settings.sessionTimeout;
        if (twoFactorToggle) twoFactorToggle.checked = settings.twoFactorAuth;
        if (autoBackupToggle) autoBackupToggle.checked = settings.autoBackup;

        // Add event listeners for settings
        this.setupSettingsEventListeners();
    }

    setupSettingsEventListeners() {
        // Save settings button
        const saveSettingsBtn = document.getElementById('save-settings');
        if (saveSettingsBtn) {
            saveSettingsBtn.addEventListener('click', () => this.saveSettings());
        }

        // Create sample data button
        const createSampleBtn = document.getElementById('create-sample-data');
        if (createSampleBtn) {
            createSampleBtn.addEventListener('click', () => this.createSampleData());
        }
    }

    saveSettings() {
        const maxFileSize = document.getElementById('max-file-size')?.value || '100';
        const sessionTimeout = document.getElementById('session-timeout')?.value || '30';
        const twoFactorAuth = document.getElementById('two-factor-toggle')?.checked || false;
        const autoBackup = document.getElementById('auto-backup-toggle')?.checked || false;

        // Save to localStorage
        localStorage.setItem('maxFileSize', maxFileSize);
        localStorage.setItem('sessionTimeout', sessionTimeout);
        localStorage.setItem('twoFactorAuth', twoFactorAuth.toString());
        localStorage.setItem('autoBackup', autoBackup.toString());

        showSuccessNotification('Settings Saved', 'System settings have been updated successfully');
    }

    async createSampleData() {
        try {
            showLoading();
            const response = await api.createSampleEvidence();

            if (response.success) {
                showSuccessNotification('Sample Data Created', 'Sample evidence and users have been created for testing');
            } else {
                showErrorNotification('Error', response.message || 'Failed to create sample data');
            }
        } catch (error) {
            console.error('Create sample data error:', error);
            showErrorNotification('Error', 'Failed to create sample data');
        } finally {
            hideLoading();
        }
    }

    async loadBlockchainHealth() {
        const statusContainer = document.getElementById('blockchain-status');
        if (statusContainer) {
            statusContainer.innerHTML = '<div class="health-card"><h4>Loading blockchain health...</h4><i class="fas fa-spinner fa-spin"></i></div>';
        }

        try {
            // Try API first, fallback to mock data
            try {
                const response = await api.getBlockchainHealth();
                if (response.success) {
                    this.displayBlockchainHealth(response.health);
                    return;
                }
            } catch (apiError) {
                console.log('Blockchain API not available, using mock data');
            }

            // Use mock data
            this.displayBlockchainHealth(this.getMockBlockchainHealth());
        } catch (error) {
            console.error('Error loading blockchain health:', error);
            this.displayBlockchainError('Error loading blockchain data');
        }
    }

    displayBlockchainHealth(health) {
        const statusContainer = document.getElementById('blockchain-status');
        if (!statusContainer) return;

        const statusClass = health.status === 'Connected' ? 'connected' :
                           health.status === 'Disconnected' ? 'disconnected' : 'error';

        statusContainer.innerHTML = `
            <div class="health-card ${statusClass}">
                <h4>
                    <span class="status-indicator ${statusClass}"></span>
                    Blockchain Status
                </h4>
                <div class="health-metric">
                    <span class="metric-label">Contract Status:</span>
                    <span class="metric-value">${health.contractStatus}</span>
                </div>
                <div class="health-metric">
                    <span class="metric-label">⛽ Avg Gas Used:</span>
                    <span class="metric-value">${health.avgGasUsed.toLocaleString()}</span>
                </div>
                <div class="health-metric">
                    <span class="metric-label">🧾 Events Emitted:</span>
                    <span class="metric-value">${health.eventsEmitted}</span>
                </div>
                <div class="health-metric">
                    <span class="metric-label">🔐 Roles Assigned:</span>
                    <span class="metric-value">${health.rolesAssigned.uploader} Uploader, ${health.rolesAssigned.admin} Admin</span>
                </div>
                <div class="health-metric">
                    <span class="metric-label">📈 Network:</span>
                    <span class="metric-value">${health.network}</span>
                </div>
                <div class="health-metric">
                    <span class="metric-label">Accounts:</span>
                    <span class="metric-value">${health.accounts || 0}</span>
                </div>
                <div class="health-metric">
                    <span class="metric-label">Last Updated:</span>
                    <span class="metric-value">${formatDate(health.lastUpdated)}</span>
                </div>
            </div>
        `;
    }

    displayBlockchainError(message) {
        const statusContainer = document.getElementById('blockchain-status');
        if (!statusContainer) return;

        statusContainer.innerHTML = `
            <div class="health-card error">
                <h4>
                    <span class="status-indicator error"></span>
                    Blockchain Error
                </h4>
                <p style="color: #856404; margin-top: 1rem;">${message}</p>
            </div>
        `;
    }

    async loadRealTimeLogs() {
        try {
            // Try API first, fallback to mock data
            try {
                const response = await api.getRealTimeLogs();
                if (response.success) {
                    this.displayRealTimeLogs(response.logs);
                    return;
                }
            } catch (apiError) {
                console.log('Logs API not available, using mock data');
            }

            // Use mock data
            this.displayRealTimeLogs(this.getMockLogs());
        } catch (error) {
            console.error('Error loading real-time logs:', error);
            this.displayLogsError('Error loading logs data');
        }
    }

    displayRealTimeLogs(logs) {
        const accessLogsContainer = document.getElementById('access-logs');
        const verificationLogsContainer = document.getElementById('verification-logs');

        if (accessLogsContainer) {
            if (logs.accessLogs && logs.accessLogs.length > 0) {
                accessLogsContainer.innerHTML = logs.accessLogs.map(log => `
                    <div class="log-entry">
                        <div>
                            <span class="log-timestamp">${formatDate(log.timestamp)}</span>
                            <span class="log-user">${log.user || 'Unknown'}</span>
                            <span class="log-action">${log.action.toUpperCase()}</span>
                            <span class="log-evidence">Evidence: ${log.evidenceId}</span>
                        </div>
                        <div class="log-ip">${log.ipAddress || 'N/A'}</div>
                    </div>
                `).join('');
            } else {
                accessLogsContainer.innerHTML = '<div class="log-entry">No recent access logs</div>';
            }
        }

        if (verificationLogsContainer) {
            if (logs.verificationLogs && logs.verificationLogs.length > 0) {
                verificationLogsContainer.innerHTML = logs.verificationLogs.map(log => `
                    <div class="log-entry">
                        <div>
                            <span class="log-timestamp">${formatDate(log.timestamp)}</span>
                            <span class="log-user">${log.user || 'Unknown'}</span>
                            <span class="log-action">VERIFY</span>
                            <span class="log-evidence">Evidence: ${log.evidenceId}</span>
                        </div>
                        <div class="log-result ${log.result ? 'success' : 'error'}">
                            ${log.result ? 'PASSED' : 'FAILED'}
                        </div>
                    </div>
                `).join('');
            } else {
                verificationLogsContainer.innerHTML = '<div class="log-entry">No recent verification logs</div>';
            }
        }
    }

    displayLogsError(message) {
        const accessLogsContainer = document.getElementById('access-logs');
        const verificationLogsContainer = document.getElementById('verification-logs');

        const errorHTML = `<div class="log-entry error">${message}</div>`;

        if (accessLogsContainer) accessLogsContainer.innerHTML = errorHTML;
        if (verificationLogsContainer) verificationLogsContainer.innerHTML = errorHTML;
    }

    toggleAutoRefresh() {
        const button = document.getElementById('auto-refresh-toggle');
        if (!button) return;

        if (this.autoRefreshInterval) {
            // Stop auto refresh
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
            button.classList.remove('active');
            button.innerHTML = '<i class="fas fa-play"></i> Auto Refresh';
        } else {
            // Start auto refresh
            this.autoRefreshInterval = setInterval(() => {
                this.loadRealTimeLogs();
            }, 5000); // Refresh every 5 seconds
            button.classList.add('active');
            button.innerHTML = '<i class="fas fa-pause"></i> Stop Auto Refresh';
        }
    }

    async exportCase() {
        const caseIdInput = document.getElementById('export-case-id');
        const caseId = caseIdInput?.value?.trim();

        if (!caseId) {
            showToast('Please enter a case ID', 'error');
            return;
        }

        try {
            showLoading();

            const blob = await api.exportLegalCase(caseId);

            // Create download link
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `case-${caseId}-legal-export.zip`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            showToast('Legal export generated successfully!', 'success');

            // Clear the input
            if (caseIdInput) caseIdInput.value = '';

        } catch (error) {
            console.error('Export error:', error);
            showToast('Error generating legal export. Please check the case ID and try again.', 'error');
        } finally {
            hideLoading();
        }
    }

    async createSampleEvidence() {
        try {
            showLoading();

            const response = await api.createSampleEvidence();

            if (response.success) {
                showToast('Sample evidence created successfully!', 'success');
                console.log('Created evidence:', response.evidence);
            } else {
                showToast('Failed to create sample evidence', 'error');
            }
        } catch (error) {
            console.error('Create sample evidence error:', error);
            showToast('Error creating sample evidence', 'error');
        } finally {
            hideLoading();
        }
    }

    // Enhanced User Management Methods
    toggleSelectAllUsers(checked) {
        const checkboxes = document.querySelectorAll('.user-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
        });

        const bulkActionSelect = document.getElementById('bulk-action-select');
        const applyBulkActionBtn = document.getElementById('apply-bulk-action');
        if (bulkActionSelect) bulkActionSelect.disabled = !checked;
        if (applyBulkActionBtn) applyBulkActionBtn.disabled = !checked;
    }

    async applyBulkAction(action) {
        const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
        const userIds = Array.from(checkedBoxes).map(cb => cb.value);

        if (userIds.length === 0) {
            showErrorNotification('Error', 'Please select users first');
            return;
        }

        const confirmMessage = this.getBulkActionConfirmMessage(action, userIds.length);
        if (!confirm(confirmMessage)) return;

        try {
            showLoading();
            const response = await api.request('/admin/bulk-user-action', 'POST', {
                action,
                userIds
            });

            if (response.success) {
                showSuccessNotification('Success', response.message);
                this.loadUsers();
                this.loadUserStats();

                // Reset bulk action controls
                const selectAllCheckbox = document.getElementById('select-all-users');
                if (selectAllCheckbox) selectAllCheckbox.checked = false;
                this.toggleSelectAllUsers(false);
            } else {
                showErrorNotification('Error', response.message);
            }
        } catch (error) {
            console.error('Bulk action error:', error);
            showErrorNotification('Error', 'Failed to perform bulk action');
        } finally {
            hideLoading();
        }
    }

    getBulkActionConfirmMessage(action, count) {
        const messages = {
            'activate': `Are you sure you want to activate ${count} user(s)?`,
            'deactivate': `Are you sure you want to deactivate ${count} user(s)?`,
            'delete': `Are you sure you want to delete ${count} user(s)? This action cannot be undone.`
        };
        return messages[action] || `Are you sure you want to perform this action on ${count} user(s)?`;
    }

    async loadUserStats() {
        try {
            // Try API first, fallback to mock data
            try {
                const response = await api.getUserStats();
                if (response.success) {
                    this.displayUserStats(response.stats);
                    return;
                }
            } catch (apiError) {
                console.log('User stats API not available, using mock data');
            }

            // Use mock data
            this.displayUserStats(this.getMockUserStats());
        } catch (error) {
            console.error('Load user stats error:', error);
            this.displayUserStats({
                totalUsers: 0,
                activeUsers: 0,
                adminUsers: 0,
                newUsersThisMonth: 0
            });
        }
    }

    displayUserStats(stats) {
        const totalUsersElement = document.getElementById('total-users-count');
        const activeUsersElement = document.getElementById('active-users-count');
        const adminUsersElement = document.getElementById('admin-users-count');
        const newUsersElement = document.getElementById('new-users-count');

        // Animate the numbers
        this.animateNumber(totalUsersElement, stats.totalUsers || 0);
        this.animateNumber(activeUsersElement, stats.activeUsers || 0);
        this.animateNumber(adminUsersElement, stats.adminUsers || 0);
        this.animateNumber(newUsersElement, stats.newUsersThisMonth || 0);
    }

    animateNumber(element, targetValue) {
        if (!element) return;

        const currentValue = parseInt(element.textContent) || 0;
        const increment = targetValue > currentValue ? 1 : -1;
        const duration = 1000; // 1 second
        const steps = Math.abs(targetValue - currentValue);
        const stepDuration = steps > 0 ? duration / steps : 0;

        if (steps === 0) return;

        let current = currentValue;
        const timer = setInterval(() => {
            current += increment;
            element.textContent = current;

            // Add pulse effect
            element.style.transform = 'scale(1.1)';
            setTimeout(() => {
                element.style.transform = 'scale(1)';
            }, 100);

            if (current === targetValue) {
                clearInterval(timer);
                // Final pulse
                element.style.transform = 'scale(1.2)';
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                }, 200);
            }
        }, stepDuration);
    }

    async resetUserPassword(userId) {
        if (!confirm('Are you sure you want to reset this user\'s password? A new temporary password will be generated.')) {
            return;
        }

        try {
            showLoading();
            const response = await api.request(`/admin/users/${userId}/reset-password`, 'POST');

            if (response.success) {
                showSuccessNotification('Success', `Password reset successfully. New password: ${response.newPassword}`);
            } else {
                showErrorNotification('Error', response.message);
            }
        } catch (error) {
            console.error('Reset password error:', error);
            showErrorNotification('Error', 'Failed to reset password');
        } finally {
            hideLoading();
        }
    }

    async toggleUserStatus(userId, currentStatus) {
        const action = currentStatus ? 'deactivate' : 'activate';
        const confirmMessage = `Are you sure you want to ${action} this user?`;

        if (!confirm(confirmMessage)) return;

        try {
            showLoading();
            const response = await api.request(`/admin/users/${userId}/toggle-status`, 'POST');

            if (response.success) {
                showSuccessNotification('Success', response.message);
                this.loadUsers();
                this.loadUserStats();
            } else {
                showErrorNotification('Error', response.message);
            }
        } catch (error) {
            console.error('Toggle user status error:', error);
            showErrorNotification('Error', 'Failed to update user status');
        } finally {
            hideLoading();
        }
    }

    async sendWelcomeEmail() {
        if (!this.editingUserId) return;

        try {
            showLoading();
            const response = await api.request(`/admin/users/${this.editingUserId}/send-welcome`, 'POST');

            if (response.success) {
                showSuccessNotification('Success', 'Welcome email sent successfully');
            } else {
                showErrorNotification('Error', response.message);
            }
        } catch (error) {
            console.error('Send welcome email error:', error);
            showErrorNotification('Error', 'Failed to send welcome email');
        } finally {
            hideLoading();
        }
    }

    async viewLoginHistory() {
        if (!this.editingUserId) return;

        try {
            showLoading();
            const response = await api.request(`/admin/users/${this.editingUserId}/login-history`, 'GET');

            if (response.success) {
                this.showLoginHistoryModal(response.history);
            } else {
                showErrorNotification('Error', response.message);
            }
        } catch (error) {
            console.error('View login history error:', error);
            showErrorNotification('Error', 'Failed to load login history');
        } finally {
            hideLoading();
        }
    }

    showLoginHistoryModal(history) {
        // Create and show login history modal
        const modal = document.createElement('div');
        modal.className = 'modal show';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Login History</h3>
                    <button class="modal-close" onclick="this.closest('.modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>IP Address</th>
                                <th>User Agent</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${history.map(entry => `
                                <tr>
                                    <td>${new Date(entry.timestamp).toLocaleString()}</td>
                                    <td>${entry.ipAddress}</td>
                                    <td>${entry.userAgent}</td>
                                    <td><span class="status-badge ${entry.success ? 'success' : 'danger'}">${entry.success ? 'Success' : 'Failed'}</span></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    // Missing user management functions
    async deleteUser(userId) {
        if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
            return;
        }

        try {
            showLoading();
            const response = await api.request(`/admin/users/${userId}`, 'DELETE');

            if (response.success) {
                showSuccessNotification('Success', 'User deleted successfully');
                this.loadUsers();
                this.loadUserStats();
            } else {
                showErrorNotification('Error', response.message || 'Failed to delete user');
            }
        } catch (error) {
            console.error('Delete user error:', error);
            showErrorNotification('Error', 'Failed to delete user');
        } finally {
            hideLoading();
        }
    }

    async editUser(userId) {
        try {
            showLoading();
            const response = await api.request(`/admin/users/${userId}`, 'GET');

            if (response.success) {
                this.showEditUserModal(response.user);
            } else {
                showErrorNotification('Error', response.message || 'Failed to load user data');
            }
        } catch (error) {
            console.error('Edit user error:', error);
            showErrorNotification('Error', 'Failed to load user data');
        } finally {
            hideLoading();
        }
    }

    showEditUserModal(user) {
        this.editingUserId = user._id;

        // Update modal title
        const modalTitle = document.getElementById('user-modal-title');
        if (modalTitle) modalTitle.textContent = 'Edit User';

        // Update submit button text
        const submitText = document.getElementById('user-submit-text');
        if (submitText) submitText.textContent = 'Update User';

        // Fill form with user data
        document.getElementById('user-username').value = user.username || '';
        document.getElementById('user-email').value = user.email || '';
        document.getElementById('user-fullname').value = user.fullName || '';
        document.getElementById('user-phone').value = user.phone || '';
        document.getElementById('user-role').value = user.role || '';
        document.getElementById('user-department').value = user.department || '';
        document.getElementById('user-badge-number').value = user.badgeNumber || '';
        document.getElementById('user-status').value = user.isActive ? 'active' : 'inactive';
        document.getElementById('user-notes').value = user.notes || '';

        // Hide password fields for editing
        const passwordGroup = document.getElementById('password-group');
        const confirmPasswordGroup = document.getElementById('confirm-password-group');
        if (passwordGroup) passwordGroup.style.display = 'none';
        if (confirmPasswordGroup) confirmPasswordGroup.style.display = 'none';

        // Show edit-only section
        const editOnlySection = document.getElementById('edit-only-section');
        if (editOnlySection) editOnlySection.style.display = 'block';

        // Show modal
        const modal = document.getElementById('user-modal');
        if (modal) modal.classList.remove('hidden');
    }

    showAddUserModal() {
        this.editingUserId = null;

        // Update modal title
        const modalTitle = document.getElementById('user-modal-title');
        if (modalTitle) modalTitle.textContent = 'Add New User';

        // Update submit button text
        const submitText = document.getElementById('user-submit-text');
        if (submitText) submitText.textContent = 'Create User';

        // Clear form
        const form = document.getElementById('user-form');
        if (form) form.reset();

        // Show password fields for new user
        const passwordGroup = document.getElementById('password-group');
        const confirmPasswordGroup = document.getElementById('confirm-password-group');
        if (passwordGroup) passwordGroup.style.display = 'block';
        if (confirmPasswordGroup) confirmPasswordGroup.style.display = 'block';

        // Hide edit-only section
        const editOnlySection = document.getElementById('edit-only-section');
        if (editOnlySection) editOnlySection.style.display = 'none';

        // Show modal
        const modal = document.getElementById('user-modal');
        if (modal) modal.classList.remove('hidden');
    }

    closeUserModal() {
        const modal = document.getElementById('user-modal');
        if (modal) modal.classList.add('hidden');
        this.editingUserId = null;
    }

    async handleUserSubmit(e) {
        e.preventDefault();

        const formData = {
            username: document.getElementById('user-username').value,
            email: document.getElementById('user-email').value,
            fullName: document.getElementById('user-fullname').value,
            phone: document.getElementById('user-phone').value,
            role: document.getElementById('user-role').value,
            department: document.getElementById('user-department').value,
            badgeNumber: document.getElementById('user-badge-number').value,
            isActive: document.getElementById('user-status').value === 'active',
            notes: document.getElementById('user-notes').value
        };

        // Validate required fields
        if (!formData.username || !formData.email || !formData.fullName || !formData.role) {
            showErrorNotification('Error', 'Please fill in all required fields');
            return;
        }

        // For new users, validate password
        if (!this.editingUserId) {
            const password = document.getElementById('user-password').value;
            const confirmPassword = document.getElementById('user-confirm-password').value;

            if (!password || password.length < 6) {
                showErrorNotification('Error', 'Password must be at least 6 characters long');
                return;
            }

            if (password !== confirmPassword) {
                showErrorNotification('Error', 'Passwords do not match');
                return;
            }

            formData.password = password;
        }

        try {
            showLoading();
            let response;

            if (this.editingUserId) {
                // Update existing user
                response = await api.updateUser(this.editingUserId, formData);
            } else {
                // Create new user
                response = await api.createUser(formData);
            }

            if (response.success) {
                showSuccessNotification('Success', response.message || `User ${this.editingUserId ? 'updated' : 'created'} successfully`);
                this.closeUserModal();
                this.loadUsers();
                this.loadUserStats();
            } else {
                showErrorNotification('Error', response.message || `Failed to ${this.editingUserId ? 'update' : 'create'} user`);
            }
        } catch (error) {
            console.error('User submit error:', error);
            showErrorNotification('Error', `Failed to ${this.editingUserId ? 'update' : 'create'} user`);
        } finally {
            hideLoading();
        }
    }

    // Real-time data synchronization
    startRealTimeSync() {
        // Update user stats every 30 seconds
        this.statsInterval = setInterval(() => {
            if (document.querySelector('.admin-nav-item.active')?.dataset.tab === 'users') {
                this.loadUserStats();
            }
        }, 30000);

        // Update blockchain status every 60 seconds
        this.blockchainInterval = setInterval(() => {
            this.loadBlockchainHealth();
        }, 60000);
    }

    stopRealTimeSync() {
        if (this.statsInterval) {
            clearInterval(this.statsInterval);
            this.statsInterval = null;
        }
        if (this.blockchainInterval) {
            clearInterval(this.blockchainInterval);
            this.blockchainInterval = null;
        }
    }

    // Cleanup when leaving admin page
    cleanup() {
        this.stopRealTimeSync();
        this.closeMobileSidebar();
    }
}

// Global functions for modals
function closeUserModal() {
    if (window.adminManager) {
        window.adminManager.closeUserModal();
    }
}

function closeConfirmationModal() {
    const modal = document.getElementById('confirmation-modal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

function showConfirmationModal(title, message, onConfirm) {
    const modal = document.getElementById('confirmation-modal');
    const titleElement = document.getElementById('confirmation-title');
    const messageElement = document.getElementById('confirmation-message');
    const confirmBtn = document.getElementById('confirm-action-btn');

    if (modal && titleElement && messageElement && confirmBtn) {
        titleElement.textContent = title;
        messageElement.textContent = message;

        // Remove existing listeners
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

        // Add new listener
        newConfirmBtn.addEventListener('click', () => {
            onConfirm();
            closeConfirmationModal();
        });

        modal.classList.remove('hidden');
    }
}

// Create global admin manager instance
const adminManager = new AdminManager();
window.adminManager = adminManager;
