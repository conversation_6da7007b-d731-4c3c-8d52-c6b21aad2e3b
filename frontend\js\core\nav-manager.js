// Navigation Manager for Two-State Navigation System
// Handles switching between public and authenticated navigation states

class NavigationManager {
    constructor() {
        this.isAuthenticated = false;
        this.userRole = null;
        this.currentNavState = 'public';
        this.navContainer = null;
        
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        this.navContainer = document.getElementById('nav-container') || document.querySelector('.nav-container');
        
        // Check initial authentication state
        this.checkAuthenticationState();
        
        // Load appropriate navigation
        this.loadNavigation();
    }

    checkAuthenticationState() {
        const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
        this.isAuthenticated = !!token;
        
        if (this.isAuthenticated) {
            try {
                const userData = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
                if (userData) {
                    const user = JSON.parse(userData);
                    this.userRole = user.role;
                }
            } catch (error) {
                console.error('NavManager: Error parsing user data:', error);
                this.isAuthenticated = false;
            }
        }
    }

    async loadNavigation() {
        if (this.isAuthenticated) {
            await this.loadAuthenticatedNav();
        } else {
            await this.loadPublicNav();
        }
    }

    async loadPublicNav() {
        console.log('NavManager: Loading public navigation');
        
        try {
            // Load public navigation template
            const response = await fetch('../shared/components/public-nav.html');
            if (!response.ok) {
                throw new Error(`Failed to load public navigation: ${response.status}`);
            }
            
            const navHTML = await response.text();
            
            // Update navigation container
            if (this.navContainer) {
                this.navContainer.innerHTML = navHTML;
            }
            
            // Setup public navigation event listeners
            this.setupPublicNavListeners();
            
            // Update body class for styling
            document.body.className = document.body.className.replace(/nav-state-\w+/g, '');
            document.body.classList.add('nav-state-public');
            
            this.currentNavState = 'public';
            
        } catch (error) {
            console.error('NavManager: Error loading public navigation:', error);
            this.fallbackToBasicNav('public');
        }
    }

    async loadAuthenticatedNav() {
        console.log('NavManager: Loading authenticated navigation');
        
        try {
            // Load authenticated navigation template
            const response = await fetch('../shared/components/auth-nav.html');
            if (!response.ok) {
                throw new Error(`Failed to load authenticated navigation: ${response.status}`);
            }
            
            const navHTML = await response.text();
            
            // Update navigation container
            if (this.navContainer) {
                this.navContainer.innerHTML = navHTML;
            }
            
            // Setup authenticated navigation event listeners
            this.setupAuthNavListeners();
            
            // Update user info
            this.updateUserInfo();
            
            // Show/hide navigation items based on role
            this.updateNavigationByRole();
            
            // Update body class for styling
            document.body.className = document.body.className.replace(/nav-state-\w+/g, '');
            document.body.classList.add('nav-state-authenticated');
            
            this.currentNavState = 'authenticated';
            
        } catch (error) {
            console.error('NavManager: Error loading authenticated navigation:', error);
            this.fallbackToBasicNav('authenticated');
        }
    }

    setupPublicNavListeners() {
        // Home navigation
        const homeNav = document.getElementById('nav-home');
        if (homeNav) {
            homeNav.addEventListener('click', () => this.navigateTo('/'));
        }

        // About navigation
        const aboutNav = document.getElementById('nav-about');
        if (aboutNav) {
            aboutNav.addEventListener('click', () => this.navigateTo('/about'));
        }

        // Login navigation
        const loginNav = document.getElementById('nav-login');
        if (loginNav) {
            loginNav.addEventListener('click', () => this.navigateTo('/login'));
        }
    }

    setupAuthNavListeners() {
        // Dashboard navigation
        const dashboardNav = document.getElementById('nav-dashboard');
        if (dashboardNav) {
            dashboardNav.addEventListener('click', () => this.navigateTo('/dashboard'));
        }

        // Upload navigation
        const uploadNav = document.getElementById('nav-upload');
        if (uploadNav) {
            uploadNav.addEventListener('click', () => this.navigateTo('/upload'));
        }

        // Evidence navigation
        const evidenceNav = document.getElementById('nav-evidence');
        if (evidenceNav) {
            evidenceNav.addEventListener('click', () => this.navigateTo('/evidence'));
        }

        // Verify navigation
        const verifyNav = document.getElementById('nav-verify');
        if (verifyNav) {
            verifyNav.addEventListener('click', () => this.navigateTo('/verify'));
        }

        // Profile navigation
        const profileNav = document.getElementById('nav-profile');
        if (profileNav) {
            profileNav.addEventListener('click', () => this.navigateTo('/profile'));
        }

        // Admin navigation
        const adminNav = document.getElementById('nav-admin');
        if (adminNav) {
            adminNav.addEventListener('click', () => this.navigateTo('/admin'));
        }

        // Logout navigation
        const logoutNav = document.getElementById('nav-logout');
        if (logoutNav) {
            logoutNav.addEventListener('click', () => this.handleLogout());
        }
    }

    updateUserInfo() {
        const userNameElement = document.getElementById('user-name');
        const userRoleElement = document.getElementById('user-role');
        
        if (this.isAuthenticated) {
            try {
                const userData = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
                if (userData) {
                    const user = JSON.parse(userData);
                    
                    if (userNameElement) {
                        userNameElement.textContent = user.fullName || user.username || 'User';
                    }
                    
                    if (userRoleElement) {
                        userRoleElement.textContent = (user.role || 'user').toUpperCase();
                        userRoleElement.className = `role-badge role-${user.role || 'user'}`;
                    }
                }
            } catch (error) {
                console.error('NavManager: Error updating user info:', error);
            }
        }
    }

    updateNavigationByRole() {
        if (!this.userRole) return;

        // Upload navigation - only for police and admin
        const uploadNav = document.getElementById('nav-upload');
        if (uploadNav) {
            uploadNav.style.display = (this.userRole === 'police' || this.userRole === 'admin') ? 'flex' : 'none';
        }

        // Admin navigation - only for admin
        const adminNav = document.getElementById('nav-admin');
        if (adminNav) {
            adminNav.style.display = this.userRole === 'admin' ? 'flex' : 'none';
        }
    }

    navigateTo(route) {
        if (window.router) {
            window.router.navigate(route);
        } else {
            // Fallback navigation
            window.location.href = route;
        }
    }

    handleLogout() {
        // Clear authentication data
        localStorage.removeItem('authToken');
        localStorage.removeItem('currentUser');
        sessionStorage.removeItem('authToken');
        sessionStorage.removeItem('currentUser');
        
        // Update state
        this.isAuthenticated = false;
        this.userRole = null;
        
        // Switch to public navigation
        this.switchToPublicMode();
        
        // Navigate to login
        this.navigateTo('/login');
    }

    async switchToAuthenticatedMode(user) {
        console.log('NavManager: Switching to authenticated mode');
        
        this.isAuthenticated = true;
        this.userRole = user.role;
        
        await this.loadAuthenticatedNav();
        
        // Notify router of authentication change
        if (window.router) {
            window.router.onAuthenticationChange(true, user.role);
        }
    }

    async switchToPublicMode() {
        console.log('NavManager: Switching to public mode');
        
        this.isAuthenticated = false;
        this.userRole = null;
        
        await this.loadPublicNav();
        
        // Notify router of authentication change
        if (window.router) {
            window.router.onAuthenticationChange(false);
        }
    }

    fallbackToBasicNav(type) {
        console.log(`NavManager: Using fallback navigation for ${type}`);
        
        if (!this.navContainer) return;
        
        if (type === 'public') {
            this.navContainer.innerHTML = `
                <div class="nav-brand">
                    <i class="fas fa-shield-check"></i>
                    <span>Evidence Protection System</span>
                </div>
                <div class="nav-menu">
                    <div class="nav-item" id="nav-home">
                        <i class="fas fa-home"></i>
                        <span>Home</span>
                    </div>
                    <div class="nav-item" id="nav-about">
                        <i class="fas fa-info-circle"></i>
                        <span>About</span>
                    </div>
                    <div class="nav-item" id="nav-login">
                        <i class="fas fa-key"></i>
                        <span>Login</span>
                    </div>
                </div>
            `;
            this.setupPublicNavListeners();
        } else {
            this.navContainer.innerHTML = `
                <div class="nav-brand minimal">
                    <i class="fas fa-shield-check"></i>
                    <span>EPS</span>
                </div>
                <div class="nav-menu">
                    <div class="nav-item" id="nav-dashboard">
                        <i class="fas fa-chart-line"></i>
                        <span>Dashboard</span>
                    </div>
                    <div class="nav-item" id="nav-evidence">
                        <i class="fas fa-archive"></i>
                        <span>Evidence</span>
                    </div>
                    <div class="nav-item" id="nav-profile">
                        <i class="fas fa-user-circle"></i>
                        <span>Profile</span>
                    </div>
                    <div class="nav-item" id="nav-logout">
                        <i class="fas fa-door-open"></i>
                        <span>Logout</span>
                    </div>
                </div>
                <div class="user-info">
                    <span id="user-name">User</span>
                    <span id="user-role" class="role-badge">Role</span>
                </div>
            `;
            this.setupAuthNavListeners();
            this.updateUserInfo();
            this.updateNavigationByRole();
        }
    }

    getCurrentNavState() {
        return this.currentNavState;
    }
}

// Initialize navigation manager when DOM is ready
if (typeof window !== 'undefined') {
    window.addEventListener('DOMContentLoaded', () => {
        if (!window.navManager) {
            window.navManager = new NavigationManager();
        }
    });
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NavigationManager;
}
