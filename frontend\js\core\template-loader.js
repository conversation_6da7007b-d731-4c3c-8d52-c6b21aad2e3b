// Template Loader Utility
// Handles loading and rendering of HTML templates with variable substitution

class TemplateLoader {
    constructor() {
        this.templateCache = new Map();
        this.componentCache = new Map();
    }

    // Load and cache a template
    async loadTemplate(templatePath) {
        if (this.templateCache.has(templatePath)) {
            return this.templateCache.get(templatePath);
        }

        try {
            const response = await fetch(templatePath);
            if (!response.ok) {
                throw new Error(`Failed to load template: ${templatePath} (${response.status})`);
            }

            const templateContent = await response.text();
            this.templateCache.set(templatePath, templateContent);
            return templateContent;
        } catch (error) {
            console.error(`TemplateLoader: Error loading template ${templatePath}:`, error);
            throw error;
        }
    }

    // Load and cache a component
    async loadComponent(componentPath) {
        if (this.componentCache.has(componentPath)) {
            return this.componentCache.get(componentPath);
        }

        try {
            const response = await fetch(componentPath);
            if (!response.ok) {
                throw new Error(`Failed to load component: ${componentPath} (${response.status})`);
            }

            const componentContent = await response.text();
            this.componentCache.set(componentPath, componentContent);
            return componentContent;
        } catch (error) {
            console.error(`TemplateLoader: Error loading component ${componentPath}:`, error);
            throw error;
        }
    }

    // Render template with variables
    renderTemplate(templateContent, variables = {}) {
        let rendered = templateContent;

        // Replace template variables {{variable}}
        Object.keys(variables).forEach(key => {
            const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
            rendered = rendered.replace(regex, variables[key] || '');
        });

        // Clean up any remaining template variables
        rendered = rendered.replace(/{{[^}]*}}/g, '');

        return rendered;
    }

    // Load and render a complete page
    async loadPage(templatePath, contentPath, variables = {}) {
        try {
            // Load base template
            const template = await this.loadTemplate(templatePath);
            
            // Load page content
            const content = await this.loadTemplate(contentPath);
            
            // Combine template and content
            const pageVariables = {
                ...variables,
                content: content
            };
            
            return this.renderTemplate(template, pageVariables);
        } catch (error) {
            console.error('TemplateLoader: Error loading page:', error);
            throw error;
        }
    }

    // Load multiple components and inject them into containers
    async loadComponents(componentMap) {
        const loadPromises = Object.entries(componentMap).map(async ([containerId, componentPath]) => {
            try {
                const component = await this.loadComponent(componentPath);
                const container = document.getElementById(containerId);
                
                if (container) {
                    container.innerHTML = component;
                    return { containerId, success: true };
                } else {
                    console.warn(`TemplateLoader: Container ${containerId} not found`);
                    return { containerId, success: false, error: 'Container not found' };
                }
            } catch (error) {
                console.error(`TemplateLoader: Error loading component for ${containerId}:`, error);
                return { containerId, success: false, error: error.message };
            }
        });

        return Promise.all(loadPromises);
    }

    // Inject a component into a specific container
    async injectComponent(containerId, componentPath, variables = {}) {
        try {
            const component = await this.loadComponent(componentPath);
            const rendered = this.renderTemplate(component, variables);
            
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = rendered;
                return true;
            } else {
                console.warn(`TemplateLoader: Container ${containerId} not found`);
                return false;
            }
        } catch (error) {
            console.error(`TemplateLoader: Error injecting component into ${containerId}:`, error);
            return false;
        }
    }

    // Clear template cache
    clearCache() {
        this.templateCache.clear();
        this.componentCache.clear();
    }

    // Clear specific template from cache
    clearTemplate(templatePath) {
        this.templateCache.delete(templatePath);
    }

    // Clear specific component from cache
    clearComponent(componentPath) {
        this.componentCache.delete(componentPath);
    }

    // Get cache statistics
    getCacheStats() {
        return {
            templates: this.templateCache.size,
            components: this.componentCache.size,
            templatePaths: Array.from(this.templateCache.keys()),
            componentPaths: Array.from(this.componentCache.keys())
        };
    }

    // Preload templates and components
    async preloadResources(resources) {
        const loadPromises = resources.map(async (resource) => {
            try {
                if (resource.type === 'template') {
                    await this.loadTemplate(resource.path);
                } else if (resource.type === 'component') {
                    await this.loadComponent(resource.path);
                }
                return { path: resource.path, success: true };
            } catch (error) {
                console.error(`TemplateLoader: Error preloading ${resource.path}:`, error);
                return { path: resource.path, success: false, error: error.message };
            }
        });

        return Promise.all(loadPromises);
    }

    // Helper method to load common page structure
    async loadPageStructure(pageType = 'public') {
        const componentMap = {
            'footer-container': '../shared/components/footer.html'
        };

        if (pageType === 'public') {
            componentMap['nav-container'] = '../shared/components/public-nav.html';
        } else {
            componentMap['nav-container'] = '../shared/components/auth-nav.html';
        }

        return this.loadComponents(componentMap);
    }

    // Helper method to setup page with common components
    async setupPage(pageType = 'public', pageTitle = 'Evidence Protection System') {
        try {
            // Update page title
            document.title = pageTitle;

            // Load common components
            await this.loadPageStructure(pageType);

            // Initialize navigation manager if not already done
            if (!window.navManager && pageType === 'authenticated') {
                window.navManager = new NavigationManager();
            }

            return true;
        } catch (error) {
            console.error('TemplateLoader: Error setting up page:', error);
            return false;
        }
    }
}

// Utility functions for common template operations
const templateUtils = {
    // Format date for templates
    formatDate: (date, format = 'short') => {
        if (!date) return '';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';

        switch (format) {
            case 'short':
                return d.toLocaleDateString();
            case 'long':
                return d.toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                });
            case 'time':
                return d.toLocaleTimeString();
            case 'datetime':
                return d.toLocaleString();
            default:
                return d.toLocaleDateString();
        }
    },

    // Escape HTML for safe template rendering
    escapeHtml: (text) => {
        if (!text) return '';
        
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    // Truncate text for templates
    truncateText: (text, maxLength = 100) => {
        if (!text || text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    },

    // Format file size for templates
    formatFileSize: (bytes) => {
        if (!bytes || bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
};

// Initialize template loader when DOM is ready
if (typeof window !== 'undefined') {
    window.addEventListener('DOMContentLoaded', () => {
        if (!window.templateLoader) {
            window.templateLoader = new TemplateLoader();
        }
        
        // Make template utils globally available
        window.templateUtils = templateUtils;
    });
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { TemplateLoader, templateUtils };
}
