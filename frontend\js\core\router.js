// Router System for Evidence Protection System
// Handles navigation between separate HTML files with authentication checks

class Router {
    constructor() {
        this.routes = {
            // Public routes (pre-login)
            '/': 'public/index.html',
            '/home': 'public/index.html',
            '/about': 'public/about.html',
            '/login': 'public/login.html',
            
            // Authenticated routes (post-login)
            '/dashboard': 'app/dashboard.html',
            '/upload': 'app/upload.html',
            '/evidence': 'app/evidence.html',
            '/verify': 'app/verify.html',
            '/profile': 'app/profile.html',
            
            // Admin routes
            '/admin': 'app/admin/index.html',
            '/admin/users': 'app/admin/users.html',
            '/admin/stats': 'app/admin/stats.html',
            '/admin/blockchain': 'app/admin/blockchain.html',
            '/admin/logs': 'app/admin/logs.html',
            '/admin/export': 'app/admin/export.html',
            '/admin/settings': 'app/admin/settings.html'
        };

        this.publicRoutes = ['/', '/home', '/about', '/login'];
        this.adminRoutes = ['/admin', '/admin/users', '/admin/stats', '/admin/blockchain', '/admin/logs', '/admin/export', '/admin/settings'];
        
        this.currentRoute = null;
        this.isInitialized = false;
        
        this.init();
    }

    init() {
        // Handle browser back/forward buttons
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.route) {
                this.navigate(e.state.route, false);
            } else {
                this.navigate(window.location.pathname, false);
            }
        });

        // Handle initial page load
        this.handleInitialLoad();
    }

    handleInitialLoad() {
        const currentPath = window.location.pathname;
        
        // If we're on the root or a specific route, handle accordingly
        if (currentPath === '/' || currentPath === '/index.html' || currentPath.endsWith('index.html')) {
            // Check authentication status
            if (this.isAuthenticated()) {
                this.navigate('/dashboard', false);
            } else {
                this.navigate('/login', false);
            }
        } else {
            // Navigate to the current path
            this.navigate(currentPath, false);
        }
    }

    async navigate(route, updateHistory = true) {
        console.log(`Router: Navigating to ${route}`);
        
        // Normalize route
        route = this.normalizeRoute(route);
        
        // Check if route exists
        if (!this.routes[route]) {
            console.warn(`Router: Route ${route} not found, redirecting to appropriate default`);
            route = this.isAuthenticated() ? '/dashboard' : '/login';
        }

        // Check authentication and permissions
        if (!this.canAccessRoute(route)) {
            console.warn(`Router: Access denied to ${route}`);
            this.handleAccessDenied(route);
            return;
        }

        // Get the target file
        const targetFile = this.routes[route];
        
        try {
            // Navigate to the new page
            if (updateHistory) {
                history.pushState({ route }, '', route);
            }
            
            // Update current route
            this.currentRoute = route;
            
            // Redirect to the actual file
            window.location.href = targetFile;
            
        } catch (error) {
            console.error(`Router: Error navigating to ${route}:`, error);
            this.handleNavigationError(error, route);
        }
    }

    normalizeRoute(route) {
        // Remove trailing slashes and normalize
        route = route.replace(/\/+$/, '') || '/';
        
        // Handle special cases
        if (route === '/index.html' || route === '') {
            route = '/';
        }
        
        return route;
    }

    canAccessRoute(route) {
        const isAuthenticated = this.isAuthenticated();
        const userRole = this.getUserRole();

        // Public routes are always accessible
        if (this.publicRoutes.includes(route)) {
            return true;
        }

        // Authenticated routes require login
        if (!isAuthenticated) {
            return false;
        }

        // Admin routes require admin role
        if (this.adminRoutes.includes(route)) {
            return userRole === 'admin';
        }

        // Upload route requires police role
        if (route === '/upload') {
            return userRole === 'police' || userRole === 'admin';
        }

        // Other authenticated routes are accessible to all logged-in users
        return true;
    }

    handleAccessDenied(attemptedRoute) {
        const isAuthenticated = this.isAuthenticated();
        
        if (!isAuthenticated) {
            // Redirect to login
            this.navigate('/login', true);
        } else {
            // Redirect to dashboard with error message
            this.navigate('/dashboard', true);
            // Store error message for display
            sessionStorage.setItem('routerError', `Access denied to ${attemptedRoute}`);
        }
    }

    handleNavigationError(error, route) {
        console.error(`Router: Navigation error for ${route}:`, error);
        
        // Try to recover by going to a safe route
        const safeRoute = this.isAuthenticated() ? '/dashboard' : '/login';
        
        // Use location.href as fallback
        window.location.href = this.routes[safeRoute];
    }

    isAuthenticated() {
        // Check if user is authenticated
        const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
        return !!token;
    }

    getUserRole() {
        // Get user role from stored user data
        try {
            const userData = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
            if (userData) {
                const user = JSON.parse(userData);
                return user.role;
            }
        } catch (error) {
            console.error('Router: Error getting user role:', error);
        }
        return null;
    }

    getCurrentRoute() {
        return this.currentRoute;
    }

    // Method to be called when authentication state changes
    onAuthenticationChange(isAuthenticated, userRole = null) {
        console.log(`Router: Authentication changed - isAuthenticated: ${isAuthenticated}, role: ${userRole}`);
        
        const currentRoute = this.getCurrentRoute() || window.location.pathname;
        
        if (!this.canAccessRoute(currentRoute)) {
            // Redirect to appropriate route
            if (isAuthenticated) {
                this.navigate('/dashboard');
            } else {
                this.navigate('/login');
            }
        }
    }

    // Utility method to get route for a page name (for backward compatibility)
    getRouteForPage(pageName) {
        const routeMap = {
            'home': '/',
            'about': '/about',
            'login': '/login',
            'dashboard': '/dashboard',
            'upload': '/upload',
            'evidence': '/evidence',
            'verify': '/verify',
            'profile': '/profile',
            'admin': '/admin'
        };
        
        return routeMap[pageName] || '/';
    }
}

// Initialize router when DOM is ready
if (typeof window !== 'undefined') {
    window.addEventListener('DOMContentLoaded', () => {
        if (!window.router) {
            window.router = new Router();
        }
    });
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Router;
}
