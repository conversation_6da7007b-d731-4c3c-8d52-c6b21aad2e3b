/* Public Navigation Styles */
/* Full-featured navigation for pre-login state */

.public-layout .navbar {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.public-layout .navbar.scrolled {
    padding: 0.5rem 0;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    backdrop-filter: blur(10px);
}

.public-layout .navbar.nav-hidden {
    transform: translateY(-100%);
}

.public-layout .nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Brand Styling */
.public-layout .nav-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: white;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.public-layout .nav-brand:hover {
    transform: scale(1.05);
}

.public-layout .brand-icon {
    font-size: 2.5rem;
    color: #64b5f6;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.public-layout .brand-text {
    display: flex;
    flex-direction: column;
}

.public-layout .brand-name {
    font-size: 1.5rem;
    font-weight: 700;
    letter-spacing: -0.5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.public-layout .brand-tagline {
    font-size: 0.75rem;
    opacity: 0.9;
    font-weight: 400;
    letter-spacing: 1px;
    text-transform: uppercase;
}

/* Navigation Menu */
.public-layout .nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.public-layout .nav-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem 1.5rem;
    color: white;
    text-decoration: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.public-layout .nav-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.public-layout .nav-item.active {
    background: rgba(255, 255, 255, 0.25);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.public-layout .nav-item i {
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
    color: #64b5f6;
}

.public-layout .nav-item span {
    font-size: 0.9rem;
    font-weight: 600;
    text-align: center;
}

.public-layout .nav-item-description {
    font-size: 0.7rem;
    opacity: 0.8;
    margin-top: 0.25rem;
    text-align: center;
    font-weight: 400;
}

/* CTA Button Styling */
.public-layout .nav-cta {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.public-layout .nav-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.public-layout .nav-cta:hover::before {
    left: 100%;
}

.public-layout .nav-cta:hover {
    background: linear-gradient(135deg, #ff5252 0%, #d84315 100%);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
}

/* Mobile Toggle */
.public-layout .nav-mobile-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
    gap: 4px;
}

.public-layout .nav-mobile-toggle span {
    width: 25px;
    height: 3px;
    background: white;
    border-radius: 2px;
    transition: all 0.3s ease;
}

/* Mobile Menu Overlay */
.public-layout .nav-mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    z-index: 9999;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    overflow-y: auto;
}

.public-layout .nav-mobile-overlay.active {
    transform: translateX(0);
}

.public-layout .nav-mobile-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.public-layout .nav-mobile-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.public-layout .nav-mobile-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

.public-layout .nav-mobile-menu {
    padding: 2rem;
}

.public-layout .nav-mobile-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    color: white;
    text-decoration: none;
    border-radius: 12px;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.05);
}

.public-layout .nav-mobile-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(10px);
}

.public-layout .nav-mobile-item i {
    font-size: 1.5rem;
    color: #64b5f6;
    width: 30px;
    text-align: center;
}

.public-layout .nav-mobile-item div {
    display: flex;
    flex-direction: column;
}

.public-layout .nav-mobile-item span {
    font-size: 1.1rem;
    font-weight: 600;
}

.public-layout .nav-mobile-item small {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-top: 0.25rem;
}

.public-layout .nav-mobile-cta {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.public-layout .nav-mobile-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.public-layout .nav-mobile-contact {
    margin-bottom: 1rem;
}

.public-layout .nav-mobile-contact p {
    color: white;
    margin: 0.5rem 0;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.public-layout .nav-mobile-contact i {
    color: #64b5f6;
    width: 20px;
}

.public-layout .security-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(100, 181, 246, 0.2);
    border-radius: 20px;
    color: white;
    font-size: 0.8rem;
    border: 1px solid rgba(100, 181, 246, 0.3);
}

.public-layout .security-badge i {
    color: #64b5f6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .public-layout .nav-container {
        padding: 0 1rem;
    }
    
    .public-layout .nav-menu {
        display: none;
    }
    
    .public-layout .nav-mobile-toggle {
        display: flex;
    }
    
    .public-layout .brand-name {
        font-size: 1.2rem;
    }
    
    .public-layout .brand-tagline {
        font-size: 0.7rem;
    }
    
    .public-layout .brand-icon {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .public-layout .nav-container {
        padding: 0 0.5rem;
    }
    
    .public-layout .brand-text {
        display: none;
    }
    
    .public-layout .nav-mobile-menu {
        padding: 1rem;
    }
    
    .public-layout .nav-mobile-footer {
        padding: 1rem;
    }
}

/* Animation for mobile toggle */
.nav-mobile-open .nav-mobile-toggle span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.nav-mobile-open .nav-mobile-toggle span:nth-child(2) {
    opacity: 0;
}

.nav-mobile-open .nav-mobile-toggle span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Prevent body scroll when mobile menu is open */
.nav-mobile-open {
    overflow: hidden;
}

/* Main content adjustment for fixed navbar */
.public-layout .main-content {
    margin-top: 100px;
}

@media (max-width: 768px) {
    .public-layout .main-content {
        margin-top: 80px;
    }
}
