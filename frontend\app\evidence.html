<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Evidence Repository - Evidence Protection System</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/auth-nav.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Meta tags for security -->
    <meta name="description" content="Evidence Repository - View and manage uploaded evidence">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Security headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/favicon.ico">
</head>
<body class="auth-layout nav-state-authenticated">
    <!-- Navigation Container -->
    <nav class="navbar auth-navbar" id="navbar">
        <div class="nav-container" id="nav-container">
            <!-- Navigation will be loaded here by NavigationManager -->
        </div>
    </nav>

    <!-- Admin Sidebar (for admin users) -->
    <div id="admin-sidebar-wrapper" class="admin-sidebar-wrapper" style="display: none;">
        <div class="admin-sidebar" id="admin-sidebar">
            <!-- Admin sidebar will be loaded here for admin users -->
        </div>
    </div>

    <!-- Main Content Area -->
    <main class="main-content auth-main" id="main-content">
        <div class="evidence-container">
            <!-- Page Header -->
            <div class="page-header">
                <div class="header-content">
                    <h1><i class="fas fa-archive"></i> Evidence Repository</h1>
                    <p>View and manage uploaded evidence</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-secondary" id="refresh-evidence">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                    <button class="btn btn-primary" id="upload-new-evidence" style="display: none;">
                        <i class="fas fa-plus"></i>
                        Upload Evidence
                    </button>
                </div>
            </div>
            
            <!-- Filters and Search -->
            <div class="evidence-filters">
                <div class="filter-section">
                    <div class="search-group">
                        <div class="search-input-container">
                            <input type="text" id="search-case" placeholder="Search by Case ID, description, or tags...">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                    
                    <div class="filter-group">
                        <select id="filter-verified">
                            <option value="">All Evidence</option>
                            <option value="true">Verified Only</option>
                            <option value="false">Unverified Only</option>
                        </select>
                        
                        <select id="filter-type">
                            <option value="">All Types</option>
                            <option value="photo">Photographs</option>
                            <option value="video">Videos</option>
                            <option value="audio">Audio</option>
                            <option value="document">Documents</option>
                            <option value="digital">Digital Evidence</option>
                        </select>
                        
                        <select id="filter-date">
                            <option value="">All Dates</option>
                            <option value="today">Today</option>
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                            <option value="year">This Year</option>
                        </select>
                        
                        <button class="btn btn-secondary" id="apply-filters">
                            <i class="fas fa-filter"></i>
                            Apply Filters
                        </button>
                        
                        <button class="btn btn-outline" id="clear-filters">
                            <i class="fas fa-times"></i>
                            Clear
                        </button>
                    </div>
                </div>
                
                <div class="view-controls">
                    <div class="view-toggle">
                        <button class="view-btn active" id="grid-view" title="Grid View">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="view-btn" id="list-view" title="List View">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                    
                    <div class="sort-controls">
                        <select id="sort-by">
                            <option value="date-desc">Newest First</option>
                            <option value="date-asc">Oldest First</option>
                            <option value="case-asc">Case ID A-Z</option>
                            <option value="case-desc">Case ID Z-A</option>
                            <option value="size-desc">Largest First</option>
                            <option value="size-asc">Smallest First</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Evidence Stats -->
            <div class="evidence-stats">
                <div class="stat-item">
                    <span class="stat-label">Total Evidence:</span>
                    <span class="stat-value" id="total-count">--</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Verified:</span>
                    <span class="stat-value" id="verified-count">--</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Pending:</span>
                    <span class="stat-value" id="pending-count">--</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Total Size:</span>
                    <span class="stat-value" id="total-size">--</span>
                </div>
            </div>
            
            <!-- Evidence Grid/List -->
            <div class="evidence-content">
                <div class="evidence-grid" id="evidence-grid">
                    <!-- Evidence cards will be populated here -->
                    <div class="loading-placeholder">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Loading evidence...</p>
                    </div>
                </div>
            </div>
            
            <!-- Pagination -->
            <div class="pagination" id="pagination" style="display: none;">
                <!-- Pagination controls will be populated here -->
            </div>
        </div>
    </main>

    <!-- Evidence Detail Modal -->
    <div id="evidence-modal" class="modal" style="display: none;">
        <div class="modal-content evidence-modal-content">
            <div class="modal-header">
                <h2 id="evidence-modal-title">Evidence Details</h2>
                <button class="modal-close" id="evidence-modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="evidence-modal-body">
                <!-- Evidence details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="download-evidence">
                    <i class="fas fa-download"></i>
                    Download
                </button>
                <button class="btn btn-primary" id="verify-evidence">
                    <i class="fas fa-certificate"></i>
                    Verify
                </button>
            </div>
        </div>
    </div>

    <!-- Toast Notification Container -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading evidence...</p>
        </div>
    </div>

    <!-- Core JavaScript -->
    <script src="../js/core/template-loader.js"></script>
    <script src="../js/core/nav-manager.js"></script>
    <script src="../js/core/router.js"></script>
    <script src="../js/core/auth-manager.js"></script>
    
    <!-- Utility Scripts -->
    <script src="../js/utils/utils.js"></script>
    <script src="../js/utils/api.js"></script>
    
    <!-- Application Scripts -->
    <script src="../js/app/app-core.js"></script>

    <!-- Page-specific JavaScript -->
    <script>
        let currentView = 'grid';
        let currentFilters = {};
        let currentSort = 'date-desc';
        let evidenceData = [];

        function initializePage() {
            console.log('Initializing evidence page');
            
            setupEventHandlers();
            setupViewControls();
            setupFilters();
            loadEvidenceData();
            updateRoleBasedVisibility();
        }

        function setupEventHandlers() {
            // Refresh button
            document.getElementById('refresh-evidence').addEventListener('click', loadEvidenceData);
            
            // Upload button
            const uploadBtn = document.getElementById('upload-new-evidence');
            if (uploadBtn) {
                uploadBtn.addEventListener('click', () => {
                    if (window.router) {
                        window.router.navigate('/upload');
                    } else {
                        window.location.href = 'upload.html';
                    }
                });
            }

            // Modal close
            document.getElementById('evidence-modal-close').addEventListener('click', closeEvidenceModal);
            
            // Modal actions
            document.getElementById('download-evidence').addEventListener('click', downloadEvidence);
            document.getElementById('verify-evidence').addEventListener('click', verifyEvidence);
        }

        function setupViewControls() {
            const gridViewBtn = document.getElementById('grid-view');
            const listViewBtn = document.getElementById('list-view');
            
            gridViewBtn.addEventListener('click', () => switchView('grid'));
            listViewBtn.addEventListener('click', () => switchView('list'));
        }

        function switchView(view) {
            currentView = view;
            
            const gridBtn = document.getElementById('grid-view');
            const listBtn = document.getElementById('list-view');
            const evidenceGrid = document.getElementById('evidence-grid');
            
            if (view === 'grid') {
                gridBtn.classList.add('active');
                listBtn.classList.remove('active');
                evidenceGrid.className = 'evidence-grid';
            } else {
                listBtn.classList.add('active');
                gridBtn.classList.remove('active');
                evidenceGrid.className = 'evidence-list';
            }
            
            renderEvidence();
        }

        function setupFilters() {
            // Search input
            const searchInput = document.getElementById('search-case');
            let searchTimeout;
            
            searchInput.addEventListener('input', () => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    applyFilters();
                }, 300);
            });

            // Filter controls
            document.getElementById('apply-filters').addEventListener('click', applyFilters);
            document.getElementById('clear-filters').addEventListener('click', clearFilters);
            
            // Sort control
            document.getElementById('sort-by').addEventListener('change', (e) => {
                currentSort = e.target.value;
                renderEvidence();
            });
        }

        function applyFilters() {
            currentFilters = {
                search: document.getElementById('search-case').value.trim(),
                verified: document.getElementById('filter-verified').value,
                type: document.getElementById('filter-type').value,
                date: document.getElementById('filter-date').value
            };
            
            renderEvidence();
        }

        function clearFilters() {
            document.getElementById('search-case').value = '';
            document.getElementById('filter-verified').value = '';
            document.getElementById('filter-type').value = '';
            document.getElementById('filter-date').value = '';
            
            currentFilters = {};
            renderEvidence();
        }

        async function loadEvidenceData() {
            try {
                showLoadingState();
                
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Mock evidence data
                evidenceData = [
                    {
                        id: 'EV001',
                        caseId: 'CASE-2024-001',
                        fileName: 'crime_scene_photo_001.jpg',
                        type: 'photo',
                        size: 2048576,
                        uploadDate: new Date('2024-01-15'),
                        verified: true,
                        description: 'Crime scene photograph showing evidence location',
                        tags: ['crime scene', 'evidence', 'photo'],
                        uploadedBy: 'Officer Johnson'
                    },
                    {
                        id: 'EV002',
                        caseId: 'CASE-2024-002',
                        fileName: 'security_footage.mp4',
                        type: 'video',
                        size: 15728640,
                        uploadDate: new Date('2024-01-14'),
                        verified: false,
                        description: 'Security camera footage from main entrance',
                        tags: ['security', 'footage', 'entrance'],
                        uploadedBy: 'Detective Smith'
                    },
                    {
                        id: 'EV003',
                        caseId: 'CASE-2024-001',
                        fileName: 'witness_statement.pdf',
                        type: 'document',
                        size: 524288,
                        uploadDate: new Date('2024-01-13'),
                        verified: true,
                        description: 'Written witness statement from primary witness',
                        tags: ['witness', 'statement', 'document'],
                        uploadedBy: 'Officer Davis'
                    }
                ];
                
                updateStats();
                renderEvidence();
                hideLoadingState();
                
            } catch (error) {
                console.error('Error loading evidence:', error);
                if (typeof showToast === 'function') {
                    showToast('Error loading evidence data', 'error');
                }
                hideLoadingState();
            }
        }

        function updateStats() {
            const total = evidenceData.length;
            const verified = evidenceData.filter(e => e.verified).length;
            const pending = total - verified;
            const totalSize = evidenceData.reduce((sum, e) => sum + e.size, 0);
            
            document.getElementById('total-count').textContent = total;
            document.getElementById('verified-count').textContent = verified;
            document.getElementById('pending-count').textContent = pending;
            document.getElementById('total-size').textContent = formatFileSize(totalSize);
        }

        function renderEvidence() {
            const evidenceGrid = document.getElementById('evidence-grid');
            let filteredData = filterEvidence(evidenceData);
            filteredData = sortEvidence(filteredData);
            
            if (filteredData.length === 0) {
                evidenceGrid.innerHTML = `
                    <div class="no-evidence">
                        <i class="fas fa-folder-open"></i>
                        <h3>No Evidence Found</h3>
                        <p>No evidence matches your current filters.</p>
                    </div>
                `;
                return;
            }
            
            const evidenceHTML = filteredData.map(evidence => {
                if (currentView === 'grid') {
                    return createEvidenceCard(evidence);
                } else {
                    return createEvidenceListItem(evidence);
                }
            }).join('');
            
            evidenceGrid.innerHTML = evidenceHTML;
            
            // Add click handlers
            evidenceGrid.querySelectorAll('.evidence-item').forEach(item => {
                item.addEventListener('click', () => {
                    const evidenceId = item.dataset.evidenceId;
                    showEvidenceDetails(evidenceId);
                });
            });
        }

        function filterEvidence(data) {
            return data.filter(evidence => {
                // Search filter
                if (currentFilters.search) {
                    const searchTerm = currentFilters.search.toLowerCase();
                    const searchableText = `${evidence.caseId} ${evidence.fileName} ${evidence.description} ${evidence.tags.join(' ')}`.toLowerCase();
                    if (!searchableText.includes(searchTerm)) return false;
                }
                
                // Verified filter
                if (currentFilters.verified !== '') {
                    if (currentFilters.verified === 'true' && !evidence.verified) return false;
                    if (currentFilters.verified === 'false' && evidence.verified) return false;
                }
                
                // Type filter
                if (currentFilters.type && evidence.type !== currentFilters.type) return false;
                
                // Date filter
                if (currentFilters.date) {
                    const now = new Date();
                    const evidenceDate = evidence.uploadDate;
                    
                    switch (currentFilters.date) {
                        case 'today':
                            if (evidenceDate.toDateString() !== now.toDateString()) return false;
                            break;
                        case 'week':
                            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                            if (evidenceDate < weekAgo) return false;
                            break;
                        case 'month':
                            if (evidenceDate.getMonth() !== now.getMonth() || evidenceDate.getFullYear() !== now.getFullYear()) return false;
                            break;
                        case 'year':
                            if (evidenceDate.getFullYear() !== now.getFullYear()) return false;
                            break;
                    }
                }
                
                return true;
            });
        }

        function sortEvidence(data) {
            return [...data].sort((a, b) => {
                switch (currentSort) {
                    case 'date-desc':
                        return b.uploadDate - a.uploadDate;
                    case 'date-asc':
                        return a.uploadDate - b.uploadDate;
                    case 'case-asc':
                        return a.caseId.localeCompare(b.caseId);
                    case 'case-desc':
                        return b.caseId.localeCompare(a.caseId);
                    case 'size-desc':
                        return b.size - a.size;
                    case 'size-asc':
                        return a.size - b.size;
                    default:
                        return 0;
                }
            });
        }

        function createEvidenceCard(evidence) {
            const typeIcon = getTypeIcon(evidence.type);
            const statusBadge = evidence.verified ? 
                '<span class="status-badge verified">Verified</span>' : 
                '<span class="status-badge pending">Pending</span>';
            
            return `
                <div class="evidence-card evidence-item" data-evidence-id="${evidence.id}">
                    <div class="evidence-icon">
                        <i class="${typeIcon}"></i>
                    </div>
                    <div class="evidence-info">
                        <h3 class="evidence-name">${evidence.fileName}</h3>
                        <p class="evidence-case">Case: ${evidence.caseId}</p>
                        <p class="evidence-description">${evidence.description}</p>
                        <div class="evidence-meta">
                            <span class="evidence-size">${formatFileSize(evidence.size)}</span>
                            <span class="evidence-date">${evidence.uploadDate.toLocaleDateString()}</span>
                        </div>
                        <div class="evidence-status">
                            ${statusBadge}
                        </div>
                    </div>
                </div>
            `;
        }

        function createEvidenceListItem(evidence) {
            const typeIcon = getTypeIcon(evidence.type);
            const statusBadge = evidence.verified ? 
                '<span class="status-badge verified">Verified</span>' : 
                '<span class="status-badge pending">Pending</span>';
            
            return `
                <div class="evidence-list-item evidence-item" data-evidence-id="${evidence.id}">
                    <div class="evidence-icon">
                        <i class="${typeIcon}"></i>
                    </div>
                    <div class="evidence-details">
                        <div class="evidence-main">
                            <h3 class="evidence-name">${evidence.fileName}</h3>
                            <p class="evidence-description">${evidence.description}</p>
                        </div>
                        <div class="evidence-meta">
                            <span class="evidence-case">Case: ${evidence.caseId}</span>
                            <span class="evidence-size">${formatFileSize(evidence.size)}</span>
                            <span class="evidence-date">${evidence.uploadDate.toLocaleDateString()}</span>
                            <span class="evidence-uploader">By: ${evidence.uploadedBy}</span>
                        </div>
                    </div>
                    <div class="evidence-actions">
                        ${statusBadge}
                        <button class="btn btn-sm btn-outline">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
            `;
        }

        function getTypeIcon(type) {
            const icons = {
                photo: 'fas fa-image',
                video: 'fas fa-video',
                audio: 'fas fa-volume-up',
                document: 'fas fa-file-alt',
                digital: 'fas fa-hdd'
            };
            return icons[type] || 'fas fa-file';
        }

        function showEvidenceDetails(evidenceId) {
            const evidence = evidenceData.find(e => e.id === evidenceId);
            if (!evidence) return;
            
            const modal = document.getElementById('evidence-modal');
            const modalTitle = document.getElementById('evidence-modal-title');
            const modalBody = document.getElementById('evidence-modal-body');
            
            modalTitle.textContent = evidence.fileName;
            modalBody.innerHTML = `
                <div class="evidence-details-content">
                    <div class="detail-section">
                        <h4>Basic Information</h4>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <label>File Name:</label>
                                <span>${evidence.fileName}</span>
                            </div>
                            <div class="detail-item">
                                <label>Case ID:</label>
                                <span>${evidence.caseId}</span>
                            </div>
                            <div class="detail-item">
                                <label>Type:</label>
                                <span>${evidence.type}</span>
                            </div>
                            <div class="detail-item">
                                <label>Size:</label>
                                <span>${formatFileSize(evidence.size)}</span>
                            </div>
                            <div class="detail-item">
                                <label>Upload Date:</label>
                                <span>${evidence.uploadDate.toLocaleString()}</span>
                            </div>
                            <div class="detail-item">
                                <label>Uploaded By:</label>
                                <span>${evidence.uploadedBy}</span>
                            </div>
                            <div class="detail-item">
                                <label>Status:</label>
                                <span class="status-badge ${evidence.verified ? 'verified' : 'pending'}">
                                    ${evidence.verified ? 'Verified' : 'Pending'}
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="detail-section">
                        <h4>Description</h4>
                        <p>${evidence.description}</p>
                    </div>
                    
                    <div class="detail-section">
                        <h4>Tags</h4>
                        <div class="tags">
                            ${evidence.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                        </div>
                    </div>
                </div>
            `;
            
            modal.style.display = 'block';
        }

        function closeEvidenceModal() {
            document.getElementById('evidence-modal').style.display = 'none';
        }

        function downloadEvidence() {
            if (typeof showToast === 'function') {
                showToast('Download feature coming soon!', 'info');
            }
        }

        function verifyEvidence() {
            if (window.router) {
                window.router.navigate('/verify');
            } else {
                window.location.href = 'verify.html';
            }
        }

        function updateRoleBasedVisibility() {
            if (window.authManager && window.authManager.canUploadEvidence()) {
                const uploadBtn = document.getElementById('upload-new-evidence');
                if (uploadBtn) {
                    uploadBtn.style.display = 'block';
                }
            }
        }

        function showLoadingState() {
            const evidenceGrid = document.getElementById('evidence-grid');
            evidenceGrid.innerHTML = `
                <div class="loading-placeholder">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading evidence...</p>
                </div>
            `;
        }

        function hideLoadingState() {
            // Loading state will be replaced by renderEvidence()
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // Check authentication first
                if (!window.authManager || !window.authManager.isAuthenticated()) {
                    if (window.router) {
                        window.router.navigate('/login');
                    } else {
                        window.location.href = '../public/login.html';
                    }
                    return;
                }

                // Initialize template loader and load common components
                if (window.templateLoader) {
                    await window.templateLoader.setupPage('authenticated', 'Evidence Repository - Evidence Protection System');
                }

                // Initialize page-specific functionality
                initializePage();

                // Hide loading overlay
                const loadingOverlay = document.getElementById('loading-overlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
            } catch (error) {
                console.error('Error initializing evidence page:', error);
                
                if (typeof showToast === 'function') {
                    showToast('Error loading page. Please refresh and try again.', 'error');
                }
            }
        });

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('evidence-modal');
            if (e.target === modal) {
                closeEvidenceModal();
            }
        });
    </script>
</body>
</html>
