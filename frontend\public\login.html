<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure Login - Evidence Protection System</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/public-nav.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Meta tags for SEO and security -->
    <meta name="description" content="Secure login to Evidence Protection System - Blockchain-based evidence management for law enforcement and legal professionals">
    <meta name="keywords" content="evidence protection, secure login, blockchain, law enforcement, legal">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Security headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/favicon.ico">
</head>
<body class="public-layout nav-state-public">
    <!-- Navigation Container -->
    <nav class="navbar public-navbar" id="navbar">
        <div class="nav-container" id="nav-container">
            <!-- Navigation will be loaded here by NavigationManager -->
        </div>
    </nav>

    <!-- Main Content Area -->
    <main class="main-content public-main">
        <!-- Login Page Content -->
        <div class="login-container">
            <div class="login-header">
                <div class="login-logo">
                    <i class="fas fa-shield-check"></i>
                </div>
                <h1>Evidence Protection System</h1>
                <p class="login-subtitle">Secure blockchain-based evidence management for law enforcement and legal professionals</p>
            </div>

            <div class="login-description">
                <div class="feature-grid">
                    <div class="feature-item">
                        <i class="fas fa-vault"></i>
                        <h3>Secure Storage</h3>
                        <p>Military-grade encryption protects all evidence files</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-cubes"></i>
                        <h3>Blockchain Verified</h3>
                        <p>Immutable blockchain records ensure evidence integrity</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-user-shield"></i>
                        <h3>Role-Based Access</h3>
                        <p>Controlled access for police, lawyers, and administrators</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-clipboard-check"></i>
                        <h3>Audit Trail</h3>
                        <p>Complete tracking of all evidence handling activities</p>
                    </div>
                </div>
            </div>

            <div class="login-form-container">
                <h2>Access Your Account</h2>
                <form class="login-form" id="login-form">
                    <div class="form-group">
                        <label for="username">Username or Email</label>
                        <input type="text" id="username" name="username" required placeholder="Enter your username" autocomplete="username">
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <div class="password-input-container">
                            <input type="password" id="password" name="password" required placeholder="Enter your password" autocomplete="current-password">
                            <button type="button" class="password-toggle" id="password-toggle">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <div class="form-group form-options">
                        <label class="checkbox-label">
                            <input type="checkbox" id="remember" name="remember">
                            <span class="checkmark"></span>
                            Remember me
                        </label>
                        <a href="#" class="forgot-password">Forgot password?</a>
                    </div>
                    <button type="submit" class="btn btn-primary" id="login-submit-btn">
                        <i class="fas fa-shield-check"></i>
                        Secure Login
                    </button>
                </form>
            </div>

            <div class="login-footer">
                <div class="security-notice">
                    <i class="fas fa-shield-exclamation"></i>
                    <p><strong>Authorized Personnel Only</strong> - This system is for official use by law enforcement and legal professionals. Unauthorized access is prohibited and monitored.</p>
                </div>
                
                <div class="login-help">
                    <h4>Need Help?</h4>
                    <div class="help-links">
                        <a href="#" class="help-link">
                            <i class="fas fa-headset"></i>
                            Contact Support
                        </a>
                        <a href="#" class="help-link">
                            <i class="fas fa-book"></i>
                            User Guide
                        </a>
                        <a href="#" class="help-link">
                            <i class="fas fa-question-circle"></i>
                            FAQ
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer Container -->
    <footer class="footer" id="footer">
        <div id="footer-container">
            <!-- Footer will be loaded here by TemplateLoader -->
        </div>
    </footer>

    <!-- Toast Notification Container -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Logging in...</p>
        </div>
    </div>

    <!-- Core JavaScript -->
    <script src="../js/core/template-loader.js"></script>
    <script src="../js/core/nav-manager.js"></script>
    <script src="../js/core/router.js"></script>
    <script src="../js/core/auth-manager.js"></script>
    
    <!-- Utility Scripts -->
    <script src="../js/utils/utils.js"></script>
    <script src="../js/utils/api.js"></script>
    
    <!-- Public Page Scripts -->
    <script src="../js/public/public-app.js"></script>

    <!-- Page-specific JavaScript -->
    <script>
        // Initialize login page
        function initializePage() {
            console.log('Initializing login page');
            
            // Setup password toggle
            setupPasswordToggle();
            
            // Setup form validation
            setupFormValidation();
            
            // Setup help links
            setupHelpLinks();
            
            // Check for redirect messages
            checkRedirectMessages();
        }

        function setupPasswordToggle() {
            const passwordField = document.getElementById('password');
            const passwordToggle = document.getElementById('password-toggle');
            
            if (passwordToggle && passwordField) {
                passwordToggle.addEventListener('click', () => {
                    const isPassword = passwordField.type === 'password';
                    passwordField.type = isPassword ? 'text' : 'password';
                    
                    const icon = passwordToggle.querySelector('i');
                    icon.className = isPassword ? 'fas fa-eye-slash' : 'fas fa-eye';
                });
            }
        }

        function setupFormValidation() {
            const form = document.getElementById('login-form');
            const usernameField = document.getElementById('username');
            const passwordField = document.getElementById('password');
            
            // Real-time validation
            if (usernameField) {
                usernameField.addEventListener('blur', () => {
                    validateUsername();
                });
                
                usernameField.addEventListener('input', () => {
                    clearFieldError(usernameField);
                });
            }
            
            if (passwordField) {
                passwordField.addEventListener('blur', () => {
                    validatePassword();
                });
                
                passwordField.addEventListener('input', () => {
                    clearFieldError(passwordField);
                });
            }
        }

        function validateUsername() {
            const usernameField = document.getElementById('username');
            const value = usernameField.value.trim();
            
            if (!value) {
                showFieldError(usernameField, 'Username or email is required');
                return false;
            }
            
            clearFieldError(usernameField);
            return true;
        }

        function validatePassword() {
            const passwordField = document.getElementById('password');
            const value = passwordField.value;
            
            if (!value) {
                showFieldError(passwordField, 'Password is required');
                return false;
            }
            
            if (value.length < 6) {
                showFieldError(passwordField, 'Password must be at least 6 characters');
                return false;
            }
            
            clearFieldError(passwordField);
            return true;
        }

        function showFieldError(field, message) {
            clearFieldError(field);
            
            field.classList.add('error');
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error';
            errorDiv.textContent = message;
            
            field.parentNode.appendChild(errorDiv);
        }

        function clearFieldError(field) {
            field.classList.remove('error');
            
            const existingError = field.parentNode.querySelector('.field-error');
            if (existingError) {
                existingError.remove();
            }
        }

        function setupHelpLinks() {
            const helpLinks = document.querySelectorAll('.help-link');
            
            helpLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    const linkText = link.textContent.trim();
                    
                    if (typeof showToast === 'function') {
                        showToast(`${linkText} - Feature coming soon!`, 'info');
                    }
                });
            });
            
            // Forgot password link
            const forgotPassword = document.querySelector('.forgot-password');
            if (forgotPassword) {
                forgotPassword.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    if (typeof showToast === 'function') {
                        showToast('Password reset feature coming soon. Please contact your administrator.', 'info');
                    }
                });
            }
        }

        function checkRedirectMessages() {
            // Check for error messages from router
            const routerError = sessionStorage.getItem('routerError');
            if (routerError) {
                sessionStorage.removeItem('routerError');
                
                if (typeof showToast === 'function') {
                    showToast(routerError, 'warning');
                }
            }
            
            // Check for logout message
            const logoutMessage = sessionStorage.getItem('logoutMessage');
            if (logoutMessage) {
                sessionStorage.removeItem('logoutMessage');
                
                if (typeof showToast === 'function') {
                    showToast(logoutMessage, 'info');
                }
            }
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // Initialize template loader and load common components
                if (window.templateLoader) {
                    await window.templateLoader.setupPage('public', 'Secure Login - Evidence Protection System');
                }

                // Initialize page-specific functionality
                initializePage();

                // Hide loading overlay
                const loadingOverlay = document.getElementById('loading-overlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
            } catch (error) {
                console.error('Error initializing login page:', error);
                
                if (typeof showToast === 'function') {
                    showToast('Error loading page. Please refresh and try again.', 'error');
                }
            }
        });
    </script>
</body>
</html>
