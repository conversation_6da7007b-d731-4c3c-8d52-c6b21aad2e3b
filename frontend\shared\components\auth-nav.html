<!-- Authenticated Navigation Component -->
<!-- Minimal navigation for post-login state -->

<div class="nav-brand minimal">
    <div class="brand-icon">
        <i class="fas fa-shield-check"></i>
    </div>
    <div class="brand-text">
        <span class="brand-name">EPS</span>
    </div>
</div>

<div class="nav-menu" id="nav-menu">
    <div class="nav-item" id="nav-dashboard" title="Dashboard">
        <i class="fas fa-chart-line"></i>
        <span>Dashboard</span>
    </div>
    
    <div class="nav-item" id="nav-upload" title="Upload Evidence" style="display: none;">
        <i class="fas fa-file-upload"></i>
        <span>Upload</span>
    </div>
    
    <div class="nav-item" id="nav-evidence" title="Evidence Repository">
        <i class="fas fa-archive"></i>
        <span>Evidence</span>
    </div>
    
    <div class="nav-item" id="nav-verify" title="Verify Evidence">
        <i class="fas fa-certificate"></i>
        <span>Verify</span>
    </div>
    
    <div class="nav-item" id="nav-admin" title="Admin Panel" style="display: none;">
        <i class="fas fa-shield-halved"></i>
        <span>Admin</span>
    </div>
</div>

<div class="nav-actions">
    <div class="nav-item nav-profile" id="nav-profile" title="User Profile">
        <i class="fas fa-user-circle"></i>
        <span class="nav-profile-text">Profile</span>
    </div>
    
    <div class="nav-item nav-logout" id="nav-logout" title="Logout">
        <i class="fas fa-door-open"></i>
        <span class="nav-logout-text">Logout</span>
    </div>
</div>

<div class="user-info" id="user-info">
    <div class="user-avatar">
        <i class="fas fa-user-circle"></i>
    </div>
    <div class="user-details">
        <span class="user-name" id="user-name">User</span>
        <span class="user-role" id="user-role">Role</span>
    </div>
</div>

<div class="nav-mobile-toggle" id="nav-mobile-toggle">
    <span></span>
    <span></span>
    <span></span>
</div>

<!-- Mobile Menu Overlay -->
<div class="nav-mobile-overlay" id="nav-mobile-overlay">
    <div class="nav-mobile-header">
        <div class="nav-brand">
            <i class="fas fa-shield-check"></i>
            <span>EPS</span>
        </div>
        <button class="nav-mobile-close" id="nav-mobile-close">
            <i class="fas fa-times"></i>
        </button>
    </div>
    
    <div class="nav-mobile-user">
        <div class="user-avatar">
            <i class="fas fa-user-circle"></i>
        </div>
        <div class="user-details">
            <span class="user-name" id="mobile-user-name">User</span>
            <span class="user-role" id="mobile-user-role">Role</span>
        </div>
    </div>
    
    <div class="nav-mobile-menu">
        <a href="#" class="nav-mobile-item" data-nav="dashboard">
            <i class="fas fa-chart-line"></i>
            <span>Dashboard</span>
        </a>
        
        <a href="#" class="nav-mobile-item" data-nav="upload" style="display: none;">
            <i class="fas fa-file-upload"></i>
            <span>Upload Evidence</span>
        </a>
        
        <a href="#" class="nav-mobile-item" data-nav="evidence">
            <i class="fas fa-archive"></i>
            <span>Evidence Repository</span>
        </a>
        
        <a href="#" class="nav-mobile-item" data-nav="verify">
            <i class="fas fa-certificate"></i>
            <span>Verify Evidence</span>
        </a>
        
        <a href="#" class="nav-mobile-item" data-nav="admin" style="display: none;">
            <i class="fas fa-shield-halved"></i>
            <span>Admin Panel</span>
        </a>
        
        <div class="nav-mobile-divider"></div>
        
        <a href="#" class="nav-mobile-item" data-nav="profile">
            <i class="fas fa-user-circle"></i>
            <span>Profile Settings</span>
        </a>
        
        <a href="#" class="nav-mobile-item nav-mobile-logout" data-nav="logout">
            <i class="fas fa-door-open"></i>
            <span>Logout</span>
        </a>
    </div>
</div>

<script>
// Authenticated Navigation JavaScript
(function() {
    'use strict';
    
    // Mobile menu toggle
    const mobileToggle = document.getElementById('nav-mobile-toggle');
    const mobileOverlay = document.getElementById('nav-mobile-overlay');
    const mobileClose = document.getElementById('nav-mobile-close');
    
    if (mobileToggle && mobileOverlay) {
        mobileToggle.addEventListener('click', () => {
            mobileOverlay.classList.add('active');
            document.body.classList.add('nav-mobile-open');
        });
    }
    
    if (mobileClose && mobileOverlay) {
        mobileClose.addEventListener('click', () => {
            mobileOverlay.classList.remove('active');
            document.body.classList.remove('nav-mobile-open');
        });
    }
    
    // Close mobile menu when clicking outside
    if (mobileOverlay) {
        mobileOverlay.addEventListener('click', (e) => {
            if (e.target === mobileOverlay) {
                mobileOverlay.classList.remove('active');
                document.body.classList.remove('nav-mobile-open');
            }
        });
    }
    
    // Handle mobile menu item clicks
    const mobileItems = document.querySelectorAll('.nav-mobile-item');
    mobileItems.forEach(item => {
        item.addEventListener('click', (e) => {
            e.preventDefault();
            const navTarget = item.getAttribute('data-nav');
            
            // Close mobile menu
            if (mobileOverlay) {
                mobileOverlay.classList.remove('active');
                document.body.classList.remove('nav-mobile-open');
            }
            
            // Handle navigation
            handleNavigation(navTarget);
        });
    });
    
    // Handle desktop navigation clicks
    const navItems = {
        'nav-dashboard': 'dashboard',
        'nav-upload': 'upload',
        'nav-evidence': 'evidence',
        'nav-verify': 'verify',
        'nav-admin': 'admin',
        'nav-profile': 'profile',
        'nav-logout': 'logout'
    };
    
    Object.keys(navItems).forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('click', () => {
                handleNavigation(navItems[id]);
            });
        }
    });
    
    function handleNavigation(target) {
        if (target === 'logout') {
            handleLogout();
            return;
        }
        
        if (window.router) {
            window.router.navigate(`/${target}`);
        } else {
            // Fallback navigation
            switch(target) {
                case 'dashboard':
                    window.location.href = 'dashboard.html';
                    break;
                case 'upload':
                    window.location.href = 'upload.html';
                    break;
                case 'evidence':
                    window.location.href = 'evidence.html';
                    break;
                case 'verify':
                    window.location.href = 'verify.html';
                    break;
                case 'profile':
                    window.location.href = 'profile.html';
                    break;
                case 'admin':
                    window.location.href = 'admin/index.html';
                    break;
            }
        }
    }
    
    function handleLogout() {
        if (window.authManager) {
            // Show confirmation dialog
            if (typeof showConfirmDialog === 'function') {
                showConfirmDialog(
                    'Confirm Logout',
                    'Are you sure you want to logout?',
                    () => {
                        window.authManager.logout();
                    }
                );
            } else {
                // Direct logout if no confirmation dialog available
                if (confirm('Are you sure you want to logout?')) {
                    window.authManager.logout();
                }
            }
        } else {
            // Fallback logout
            localStorage.removeItem('authToken');
            localStorage.removeItem('currentUser');
            sessionStorage.removeItem('authToken');
            sessionStorage.removeItem('currentUser');
            
            if (window.router) {
                window.router.navigate('/login');
            } else {
                window.location.href = '../public/login.html';
            }
        }
    }
    
    // Update user info in navigation
    function updateUserInfo() {
        if (!window.authManager || !window.authManager.isAuthenticated()) return;
        
        const user = window.authManager.getCurrentUser();
        if (!user) return;
        
        // Update desktop user info
        const userName = document.getElementById('user-name');
        const userRole = document.getElementById('user-role');
        
        if (userName) {
            userName.textContent = user.fullName || user.username || 'User';
        }
        
        if (userRole) {
            userRole.textContent = (user.role || 'user').toUpperCase();
            userRole.className = `user-role role-${user.role || 'user'}`;
        }
        
        // Update mobile user info
        const mobileUserName = document.getElementById('mobile-user-name');
        const mobileUserRole = document.getElementById('mobile-user-role');
        
        if (mobileUserName) {
            mobileUserName.textContent = user.fullName || user.username || 'User';
        }
        
        if (mobileUserRole) {
            mobileUserRole.textContent = (user.role || 'user').toUpperCase();
            mobileUserRole.className = `user-role role-${user.role || 'user'}`;
        }
    }
    
    // Update navigation based on user role
    function updateNavigationByRole() {
        if (!window.authManager || !window.authManager.isAuthenticated()) return;
        
        const userRole = window.authManager.getUserRole();
        
        // Upload navigation - only for police and admin
        const uploadNavs = document.querySelectorAll('#nav-upload, [data-nav="upload"]');
        uploadNavs.forEach(nav => {
            nav.style.display = (userRole === 'police' || userRole === 'admin') ? 'flex' : 'none';
        });
        
        // Admin navigation - only for admin
        const adminNavs = document.querySelectorAll('#nav-admin, [data-nav="admin"]');
        adminNavs.forEach(nav => {
            nav.style.display = userRole === 'admin' ? 'flex' : 'none';
        });
    }
    
    // Set active navigation item
    function setActiveNavItem() {
        const currentPath = window.location.pathname;
        const navItems = document.querySelectorAll('.nav-item');
        
        navItems.forEach(item => item.classList.remove('active'));
        
        if (currentPath.includes('dashboard')) {
            const dashboardNav = document.getElementById('nav-dashboard');
            if (dashboardNav) dashboardNav.classList.add('active');
        } else if (currentPath.includes('upload')) {
            const uploadNav = document.getElementById('nav-upload');
            if (uploadNav) uploadNav.classList.add('active');
        } else if (currentPath.includes('evidence')) {
            const evidenceNav = document.getElementById('nav-evidence');
            if (evidenceNav) evidenceNav.classList.add('active');
        } else if (currentPath.includes('verify')) {
            const verifyNav = document.getElementById('nav-verify');
            if (verifyNav) verifyNav.classList.add('active');
        } else if (currentPath.includes('admin')) {
            const adminNav = document.getElementById('nav-admin');
            if (adminNav) adminNav.classList.add('active');
        } else if (currentPath.includes('profile')) {
            const profileNav = document.getElementById('nav-profile');
            if (profileNav) profileNav.classList.add('active');
        }
    }
    
    // Initialize navigation
    function initializeAuthNav() {
        updateUserInfo();
        updateNavigationByRole();
        setActiveNavItem();
    }
    
    // Initialize when auth manager is ready
    if (window.authManager && window.authManager.isAuthenticated()) {
        initializeAuthNav();
    } else {
        // Wait for auth manager to be ready
        document.addEventListener('authManagerReady', initializeAuthNav);
    }
    
    // Listen for authentication changes
    if (window.authManager) {
        window.authManager.onLogin(initializeAuthNav);
    }
    
    // Handle window resize
    window.addEventListener('resize', () => {
        if (window.innerWidth > 768) {
            // Close mobile menu on desktop
            if (mobileOverlay) {
                mobileOverlay.classList.remove('active');
                document.body.classList.remove('nav-mobile-open');
            }
        }
    });
    
})();
</script>
