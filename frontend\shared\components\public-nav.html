<!-- Public Navigation Component -->
<!-- Full-featured navigation for pre-login state -->

<div class="nav-brand">
    <div class="brand-icon">
        <i class="fas fa-shield-check"></i>
    </div>
    <div class="brand-text">
        <span class="brand-name">Evidence Protection System</span>
        <span class="brand-tagline">Secure • Blockchain • Verified</span>
    </div>
</div>

<div class="nav-menu" id="nav-menu">
    <div class="nav-item" id="nav-home">
        <i class="fas fa-home"></i>
        <span>Home</span>
        <div class="nav-item-description">System Overview</div>
    </div>
    
    <div class="nav-item" id="nav-about">
        <i class="fas fa-info-circle"></i>
        <span>About Us</span>
        <div class="nav-item-description">Our Mission & Vision</div>
    </div>
    
    <div class="nav-item nav-cta" id="nav-login">
        <i class="fas fa-key"></i>
        <span>Secure Login</span>
        <div class="nav-item-description">Access Your Account</div>
    </div>
</div>

<div class="nav-mobile-toggle" id="nav-mobile-toggle">
    <span></span>
    <span></span>
    <span></span>
</div>

<!-- Mobile Menu Overlay -->
<div class="nav-mobile-overlay" id="nav-mobile-overlay">
    <div class="nav-mobile-header">
        <div class="nav-brand">
            <i class="fas fa-shield-check"></i>
            <span>Evidence Protection System</span>
        </div>
        <button class="nav-mobile-close" id="nav-mobile-close">
            <i class="fas fa-times"></i>
        </button>
    </div>
    
    <div class="nav-mobile-menu">
        <a href="#" class="nav-mobile-item" data-nav="home">
            <i class="fas fa-home"></i>
            <div>
                <span>Home</span>
                <small>System Overview</small>
            </div>
        </a>
        
        <a href="#" class="nav-mobile-item" data-nav="about">
            <i class="fas fa-info-circle"></i>
            <div>
                <span>About Us</span>
                <small>Our Mission & Vision</small>
            </div>
        </a>
        
        <a href="#" class="nav-mobile-item nav-mobile-cta" data-nav="login">
            <i class="fas fa-key"></i>
            <div>
                <span>Secure Login</span>
                <small>Access Your Account</small>
            </div>
        </a>
    </div>
    
    <div class="nav-mobile-footer">
        <div class="nav-mobile-contact">
            <p><i class="fas fa-phone"></i> +255-75-756-9733</p>
            <p><i class="fas fa-envelope"></i> <EMAIL></p>
        </div>
        
        <div class="nav-mobile-security">
            <div class="security-badge">
                <i class="fas fa-shield-check"></i>
                <span>Secure & Encrypted</span>
            </div>
        </div>
    </div>
</div>

<script>
// Public Navigation JavaScript
(function() {
    'use strict';
    
    // Mobile menu toggle
    const mobileToggle = document.getElementById('nav-mobile-toggle');
    const mobileOverlay = document.getElementById('nav-mobile-overlay');
    const mobileClose = document.getElementById('nav-mobile-close');
    
    if (mobileToggle && mobileOverlay) {
        mobileToggle.addEventListener('click', () => {
            mobileOverlay.classList.add('active');
            document.body.classList.add('nav-mobile-open');
        });
    }
    
    if (mobileClose && mobileOverlay) {
        mobileClose.addEventListener('click', () => {
            mobileOverlay.classList.remove('active');
            document.body.classList.remove('nav-mobile-open');
        });
    }
    
    // Close mobile menu when clicking outside
    if (mobileOverlay) {
        mobileOverlay.addEventListener('click', (e) => {
            if (e.target === mobileOverlay) {
                mobileOverlay.classList.remove('active');
                document.body.classList.remove('nav-mobile-open');
            }
        });
    }
    
    // Handle mobile menu item clicks
    const mobileItems = document.querySelectorAll('.nav-mobile-item');
    mobileItems.forEach(item => {
        item.addEventListener('click', (e) => {
            e.preventDefault();
            const navTarget = item.getAttribute('data-nav');
            
            // Close mobile menu
            if (mobileOverlay) {
                mobileOverlay.classList.remove('active');
                document.body.classList.remove('nav-mobile-open');
            }
            
            // Navigate
            if (window.router) {
                switch(navTarget) {
                    case 'home':
                        window.router.navigate('/');
                        break;
                    case 'about':
                        window.router.navigate('/about');
                        break;
                    case 'login':
                        window.router.navigate('/login');
                        break;
                }
            } else {
                // Fallback navigation
                switch(navTarget) {
                    case 'home':
                        window.location.href = 'index.html';
                        break;
                    case 'about':
                        window.location.href = 'about.html';
                        break;
                    case 'login':
                        window.location.href = 'login.html';
                        break;
                }
            }
        });
    });
    
    // Handle desktop navigation clicks
    const navHome = document.getElementById('nav-home');
    const navAbout = document.getElementById('nav-about');
    const navLogin = document.getElementById('nav-login');
    
    if (navHome) {
        navHome.addEventListener('click', () => {
            if (window.router) {
                window.router.navigate('/');
            } else {
                window.location.href = 'index.html';
            }
        });
    }
    
    if (navAbout) {
        navAbout.addEventListener('click', () => {
            if (window.router) {
                window.router.navigate('/about');
            } else {
                window.location.href = 'about.html';
            }
        });
    }
    
    if (navLogin) {
        navLogin.addEventListener('click', () => {
            if (window.router) {
                window.router.navigate('/login');
            } else {
                window.location.href = 'login.html';
            }
        });
    }
    
    // Add active state to current page
    function setActiveNavItem() {
        const currentPath = window.location.pathname;
        const navItems = document.querySelectorAll('.nav-item');
        
        navItems.forEach(item => item.classList.remove('active'));
        
        if (currentPath === '/' || currentPath.includes('index.html')) {
            if (navHome) navHome.classList.add('active');
        } else if (currentPath.includes('about.html')) {
            if (navAbout) navAbout.classList.add('active');
        } else if (currentPath.includes('login.html')) {
            if (navLogin) navLogin.classList.add('active');
        }
    }
    
    // Set active item on load
    setActiveNavItem();
    
    // Handle scroll effects for navbar
    let lastScrollTop = 0;
    const navbar = document.querySelector('.navbar');
    
    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (navbar) {
            if (scrollTop > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
            
            // Hide/show navbar on scroll (optional)
            if (scrollTop > lastScrollTop && scrollTop > 200) {
                navbar.classList.add('nav-hidden');
            } else {
                navbar.classList.remove('nav-hidden');
            }
        }
        
        lastScrollTop = scrollTop;
    });
    
    // Handle window resize
    window.addEventListener('resize', () => {
        if (window.innerWidth > 768) {
            // Close mobile menu on desktop
            if (mobileOverlay) {
                mobileOverlay.classList.remove('active');
                document.body.classList.remove('nav-mobile-open');
            }
        }
    });
    
    // Add hover effects for nav items
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('mouseenter', () => {
            item.classList.add('hovered');
        });
        
        item.addEventListener('mouseleave', () => {
            item.classList.remove('hovered');
        });
    });
    
})();
</script>
