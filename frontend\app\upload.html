<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Evidence - Evidence Protection System</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/auth-nav.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Meta tags for security -->
    <meta name="description" content="Upload Evidence - Secure evidence upload to blockchain">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Security headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/favicon.ico">
</head>
<body class="auth-layout nav-state-authenticated">
    <!-- Navigation Container -->
    <nav class="navbar auth-navbar" id="navbar">
        <div class="nav-container" id="nav-container">
            <!-- Navigation will be loaded here by NavigationManager -->
        </div>
    </nav>

    <!-- Admin Sidebar (for admin users) -->
    <div id="admin-sidebar-wrapper" class="admin-sidebar-wrapper" style="display: none;">
        <div class="admin-sidebar" id="admin-sidebar">
            <!-- Admin sidebar will be loaded here for admin users -->
        </div>
    </div>

    <!-- Main Content Area -->
    <main class="main-content auth-main" id="main-content">
        <div class="upload-container">
            <!-- Page Header -->
            <div class="page-header">
                <h1><i class="fas fa-file-upload"></i> Upload Evidence</h1>
                <p>Securely upload digital evidence to the blockchain</p>
            </div>
            
            <!-- Upload Form -->
            <form class="upload-form" id="upload-form" enctype="multipart/form-data">
                <div class="form-section">
                    <h3>Case Information</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="case-id">Case ID *</label>
                            <input type="text" id="case-id" name="caseId" required placeholder="Enter case ID">
                            <small>Unique identifier for the case</small>
                        </div>
                        <div class="form-group">
                            <label for="case-title">Case Title</label>
                            <input type="text" id="case-title" name="caseTitle" placeholder="Brief case description">
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h3>Evidence File</h3>
                    <div class="form-group">
                        <label for="evidence-file">Evidence File *</label>
                        <div class="file-upload-area" id="file-upload-area">
                            <input type="file" id="evidence-file" name="evidenceFile" required accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt">
                            <div class="upload-placeholder">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p>Drag and drop your file here or <span class="upload-link">browse</span></p>
                                <small>Max file size: 50MB. Supported formats: Images, Videos, Audio, Documents</small>
                            </div>
                        </div>
                        <div class="file-info" id="file-info" style="display: none;">
                            <div class="file-details">
                                <i class="fas fa-file"></i>
                                <div class="file-text">
                                    <span class="file-name" id="file-name"></span>
                                    <span class="file-size" id="file-size"></span>
                                </div>
                                <button type="button" class="remove-file" id="remove-file">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h3>Evidence Details</h3>
                    <div class="form-group">
                        <label for="description">Description *</label>
                        <textarea id="description" name="description" rows="4" required 
                                  placeholder="Provide a detailed description of the evidence..."></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="evidence-type">Evidence Type</label>
                            <select id="evidence-type" name="evidenceType">
                                <option value="">Select type</option>
                                <option value="photo">Photograph</option>
                                <option value="video">Video Recording</option>
                                <option value="audio">Audio Recording</option>
                                <option value="document">Document</option>
                                <option value="digital">Digital Evidence</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="location">Location</label>
                            <input type="text" id="location" name="location" placeholder="Where was this evidence collected?">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="tags">Tags (optional)</label>
                        <input type="text" id="tags" name="tags" 
                               placeholder="Enter tags separated by commas (e.g., weapon, fingerprint, DNA)">
                        <small>Tags help categorize and search for evidence</small>
                    </div>
                </div>

                <div class="form-section">
                    <h3>Chain of Custody</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="collected-by">Collected By</label>
                            <input type="text" id="collected-by" name="collectedBy" placeholder="Officer/Agent name">
                        </div>
                        <div class="form-group">
                            <label for="collection-date">Collection Date</label>
                            <input type="datetime-local" id="collection-date" name="collectionDate">
                        </div>
                    </div>
                </div>
                
                <!-- File Preview -->
                <div class="file-preview" id="file-preview" style="display: none;">
                    <h3>File Preview</h3>
                    <div class="preview-content" id="preview-content"></div>
                </div>
                
                <!-- Upload Progress -->
                <div class="upload-progress" id="upload-progress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div class="progress-text">
                        <span id="progress-percentage">0%</span>
                        <span id="progress-status">Preparing upload...</span>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" id="reset-form">
                        <i class="fas fa-undo"></i>
                        Reset Form
                    </button>
                    <button type="submit" class="btn btn-primary" id="upload-submit">
                        <i class="fas fa-file-upload"></i>
                        Upload Evidence
                    </button>
                </div>
            </form>
        </div>
    </main>

    <!-- Toast Notification Container -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Processing upload...</p>
        </div>
    </div>

    <!-- Core JavaScript -->
    <script src="../js/core/template-loader.js"></script>
    <script src="../js/core/nav-manager.js"></script>
    <script src="../js/core/router.js"></script>
    <script src="../js/core/auth-manager.js"></script>
    
    <!-- Utility Scripts -->
    <script src="../js/utils/utils.js"></script>
    <script src="../js/utils/api.js"></script>
    
    <!-- Application Scripts -->
    <script src="../js/app/app-core.js"></script>

    <!-- Page-specific JavaScript -->
    <script>
        let selectedFile = null;

        function initializePage() {
            console.log('Initializing upload page');
            
            // Check upload permissions
            if (!window.authManager || !window.authManager.canUploadEvidence()) {
                if (typeof showToast === 'function') {
                    showToast('You do not have permission to upload evidence', 'error');
                }
                
                if (window.router) {
                    window.router.navigate('/dashboard');
                } else {
                    window.location.href = 'dashboard.html';
                }
                return;
            }
            
            setupFileUpload();
            setupFormHandlers();
            setupFormValidation();
            prefillUserInfo();
        }

        function setupFileUpload() {
            const fileInput = document.getElementById('evidence-file');
            const uploadArea = document.getElementById('file-upload-area');
            const fileInfo = document.getElementById('file-info');
            const removeFileBtn = document.getElementById('remove-file');

            // File input change
            fileInput.addEventListener('change', handleFileSelect);

            // Drag and drop
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('drag-over');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('drag-over');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('drag-over');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    handleFileSelect({ target: fileInput });
                }
            });

            // Remove file
            removeFileBtn.addEventListener('click', () => {
                clearFileSelection();
            });

            // Click to browse
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            // Validate file size (50MB limit)
            if (file.size > 50 * 1024 * 1024) {
                if (typeof showToast === 'function') {
                    showToast('File size exceeds 50MB limit', 'error');
                }
                clearFileSelection();
                return;
            }

            selectedFile = file;
            displayFileInfo(file);
            generatePreview(file);
            
            // Auto-detect evidence type
            autoDetectEvidenceType(file);
        }

        function displayFileInfo(file) {
            const fileInfo = document.getElementById('file-info');
            const fileName = document.getElementById('file-name');
            const fileSize = document.getElementById('file-size');
            const uploadArea = document.getElementById('file-upload-area');

            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            
            uploadArea.style.display = 'none';
            fileInfo.style.display = 'block';
        }

        function clearFileSelection() {
            const fileInput = document.getElementById('evidence-file');
            const fileInfo = document.getElementById('file-info');
            const uploadArea = document.getElementById('file-upload-area');
            const preview = document.getElementById('file-preview');

            fileInput.value = '';
            selectedFile = null;
            
            fileInfo.style.display = 'none';
            uploadArea.style.display = 'block';
            preview.style.display = 'none';
        }

        function generatePreview(file) {
            const preview = document.getElementById('file-preview');
            const previewContent = document.getElementById('preview-content');
            
            if (file.type.startsWith('image/')) {
                const img = document.createElement('img');
                img.src = URL.createObjectURL(file);
                img.style.maxWidth = '300px';
                img.style.maxHeight = '200px';
                previewContent.innerHTML = '';
                previewContent.appendChild(img);
                preview.style.display = 'block';
            } else {
                preview.style.display = 'none';
            }
        }

        function autoDetectEvidenceType(file) {
            const typeSelect = document.getElementById('evidence-type');
            
            if (file.type.startsWith('image/')) {
                typeSelect.value = 'photo';
            } else if (file.type.startsWith('video/')) {
                typeSelect.value = 'video';
            } else if (file.type.startsWith('audio/')) {
                typeSelect.value = 'audio';
            } else if (file.type === 'application/pdf' || file.type.includes('document')) {
                typeSelect.value = 'document';
            }
        }

        function setupFormHandlers() {
            const form = document.getElementById('upload-form');
            const resetBtn = document.getElementById('reset-form');

            form.addEventListener('submit', handleFormSubmit);
            resetBtn.addEventListener('click', resetForm);
        }

        async function handleFormSubmit(event) {
            event.preventDefault();
            
            if (!selectedFile) {
                if (typeof showToast === 'function') {
                    showToast('Please select a file to upload', 'error');
                }
                return;
            }

            const formData = new FormData(event.target);
            
            try {
                showUploadProgress();
                
                // Simulate upload progress
                await simulateUpload(formData);
                
                if (typeof showToast === 'function') {
                    showToast('Evidence uploaded successfully!', 'success');
                }
                
                // Reset form after successful upload
                setTimeout(() => {
                    resetForm();
                }, 2000);
                
            } catch (error) {
                console.error('Upload error:', error);
                if (typeof showToast === 'function') {
                    showToast('Upload failed. Please try again.', 'error');
                }
            } finally {
                hideUploadProgress();
            }
        }

        function showUploadProgress() {
            const progress = document.getElementById('upload-progress');
            const submitBtn = document.getElementById('upload-submit');
            
            progress.style.display = 'block';
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
        }

        function hideUploadProgress() {
            const progress = document.getElementById('upload-progress');
            const submitBtn = document.getElementById('upload-submit');
            
            progress.style.display = 'none';
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-file-upload"></i> Upload Evidence';
        }

        async function simulateUpload(formData) {
            const progressFill = document.getElementById('progress-fill');
            const progressPercentage = document.getElementById('progress-percentage');
            const progressStatus = document.getElementById('progress-status');
            
            const steps = [
                { percent: 20, status: 'Validating file...' },
                { percent: 40, status: 'Encrypting data...' },
                { percent: 60, status: 'Uploading to server...' },
                { percent: 80, status: 'Creating blockchain record...' },
                { percent: 100, status: 'Upload complete!' }
            ];
            
            for (const step of steps) {
                await new Promise(resolve => setTimeout(resolve, 800));
                progressFill.style.width = step.percent + '%';
                progressPercentage.textContent = step.percent + '%';
                progressStatus.textContent = step.status;
            }
        }

        function resetForm() {
            const form = document.getElementById('upload-form');
            form.reset();
            clearFileSelection();
            
            if (typeof showToast === 'function') {
                showToast('Form reset', 'info');
            }
        }

        function setupFormValidation() {
            const requiredFields = document.querySelectorAll('[required]');
            
            requiredFields.forEach(field => {
                field.addEventListener('blur', () => validateField(field));
                field.addEventListener('input', () => clearFieldError(field));
            });
        }

        function validateField(field) {
            const value = field.value.trim();
            
            if (!value) {
                showFieldError(field, 'This field is required');
                return false;
            }
            
            clearFieldError(field);
            return true;
        }

        function showFieldError(field, message) {
            clearFieldError(field);
            
            field.classList.add('error');
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error';
            errorDiv.textContent = message;
            
            field.parentNode.appendChild(errorDiv);
        }

        function clearFieldError(field) {
            field.classList.remove('error');
            
            const existingError = field.parentNode.querySelector('.field-error');
            if (existingError) {
                existingError.remove();
            }
        }

        function prefillUserInfo() {
            if (window.authManager && window.authManager.isAuthenticated()) {
                const user = window.authManager.getCurrentUser();
                if (user) {
                    const collectedByField = document.getElementById('collected-by');
                    if (collectedByField && !collectedByField.value) {
                        collectedByField.value = user.fullName || user.username;
                    }
                }
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // Check authentication first
                if (!window.authManager || !window.authManager.isAuthenticated()) {
                    if (window.router) {
                        window.router.navigate('/login');
                    } else {
                        window.location.href = '../public/login.html';
                    }
                    return;
                }

                // Initialize template loader and load common components
                if (window.templateLoader) {
                    await window.templateLoader.setupPage('authenticated', 'Upload Evidence - Evidence Protection System');
                }

                // Initialize page-specific functionality
                initializePage();

                // Hide loading overlay
                const loadingOverlay = document.getElementById('loading-overlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
            } catch (error) {
                console.error('Error initializing upload page:', error);
                
                if (typeof showToast === 'function') {
                    showToast('Error loading page. Please refresh and try again.', 'error');
                }
            }
        });
    </script>
</body>
</html>
