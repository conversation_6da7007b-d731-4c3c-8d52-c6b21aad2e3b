// Public Application Controller
// Handles functionality for public (pre-login) pages

class PublicApp {
    constructor() {
        this.currentPage = null;
        this.isInitialized = false;
        
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        this.setupEventListeners();
        this.setupFormHandlers();
        this.checkAuthenticationRedirect();
        this.initializePage();
        
        this.isInitialized = true;
    }

    setupEventListeners() {
        // Handle contact form submissions
        document.addEventListener('submit', (event) => {
            if (event.target.id === 'contact-form') {
                event.preventDefault();
                this.handleContactForm(event);
            }
        });

        // Handle newsletter signup
        document.addEventListener('submit', (event) => {
            if (event.target.id === 'newsletter-form') {
                event.preventDefault();
                this.handleNewsletterSignup(event);
            }
        });

        // Handle scroll effects
        window.addEventListener('scroll', () => {
            this.handleScrollEffects();
        });

        // Handle resize events
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    setupFormHandlers() {
        // Login form handler
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // Demo request form handler
        const demoForm = document.getElementById('demo-form');
        if (demoForm) {
            demoForm.addEventListener('submit', (e) => this.handleDemoRequest(e));
        }
    }

    checkAuthenticationRedirect() {
        // Check if user is already authenticated
        if (window.authManager && window.authManager.isAuthenticated()) {
            console.log('PublicApp: User already authenticated, redirecting to dashboard');
            
            if (window.router) {
                window.router.navigate('/dashboard');
            } else {
                window.location.href = '../app/dashboard.html';
            }
        }
    }

    initializePage() {
        // Determine current page and initialize accordingly
        const path = window.location.pathname;
        
        if (path.includes('login.html')) {
            this.currentPage = 'login';
            this.initializeLoginPage();
        } else if (path.includes('about.html')) {
            this.currentPage = 'about';
            this.initializeAboutPage();
        } else {
            this.currentPage = 'home';
            this.initializeHomePage();
        }
    }

    initializeHomePage() {
        console.log('PublicApp: Initializing home page');
        
        // Setup hero animations
        this.setupHeroAnimations();
        
        // Setup feature cards
        this.setupFeatureCards();
        
        // Setup statistics counter
        this.setupStatsCounter();
        
        // Setup testimonials carousel (if exists)
        this.setupTestimonialsCarousel();
    }

    initializeAboutPage() {
        console.log('PublicApp: Initializing about page');
        
        // Setup timeline animations
        this.setupTimelineAnimations();
        
        // Setup team member cards
        this.setupTeamCards();
        
        // Setup mission statement animations
        this.setupMissionAnimations();
    }

    initializeLoginPage() {
        console.log('PublicApp: Initializing login page');
        
        // Focus on username field
        const usernameField = document.getElementById('username');
        if (usernameField) {
            setTimeout(() => usernameField.focus(), 100);
        }
        
        // Setup form validation
        this.setupLoginValidation();
        
        // Setup demo data buttons (if in development)
        this.setupDemoButtons();
    }

    async handleLogin(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        const credentials = {
            username: formData.get('username'),
            password: formData.get('password')
        };
        
        const rememberMe = formData.get('remember') === 'on';
        
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging in...';
        submitBtn.disabled = true;
        
        try {
            if (!window.authManager) {
                throw new Error('Authentication manager not available');
            }
            
            const result = await window.authManager.login(credentials, rememberMe);
            
            if (result.success) {
                // Login successful - authManager will handle navigation
                if (typeof showToast === 'function') {
                    showToast(`Welcome back, ${result.user.fullName || result.user.username}!`, 'success');
                }
            } else {
                // Login failed
                if (typeof showToast === 'function') {
                    showToast(result.error || 'Login failed', 'error');
                }
                
                // Reset form
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                
                // Focus back to username field
                const usernameField = document.getElementById('username');
                if (usernameField) {
                    usernameField.focus();
                    usernameField.select();
                }
            }
        } catch (error) {
            console.error('PublicApp: Login error:', error);
            
            if (typeof showToast === 'function') {
                showToast('Login failed. Please try again.', 'error');
            }
            
            // Reset form
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    setupLoginValidation() {
        const form = document.getElementById('login-form');
        if (!form) return;
        
        const usernameField = document.getElementById('username');
        const passwordField = document.getElementById('password');
        
        // Real-time validation
        if (usernameField) {
            usernameField.addEventListener('input', () => {
                this.validateField(usernameField, 'Username is required');
            });
        }
        
        if (passwordField) {
            passwordField.addEventListener('input', () => {
                this.validateField(passwordField, 'Password is required');
            });
        }
    }

    validateField(field, message) {
        const isValid = field.value.trim() !== '';
        
        field.classList.toggle('invalid', !isValid);
        field.classList.toggle('valid', isValid);
        
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
        
        // Add error message if invalid
        if (!isValid && field === document.activeElement) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error';
            errorDiv.textContent = message;
            field.parentNode.appendChild(errorDiv);
        }
        
        return isValid;
    }

    setupDemoButtons() {
        // Add demo login buttons for development/testing
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            const loginContainer = document.querySelector('.login-form-container');
            if (loginContainer) {
                const demoSection = document.createElement('div');
                demoSection.className = 'demo-section';
                demoSection.innerHTML = `
                    <div class="demo-header">
                        <h4>Demo Accounts</h4>
                        <p>Quick login for testing purposes</p>
                    </div>
                    <div class="demo-buttons">
                        <button type="button" class="btn btn-outline" onclick="window.publicApp.fillDemoCredentials('admin')">
                            <i class="fas fa-user-shield"></i> Admin Demo
                        </button>
                        <button type="button" class="btn btn-outline" onclick="window.publicApp.fillDemoCredentials('police')">
                            <i class="fas fa-badge"></i> Police Demo
                        </button>
                        <button type="button" class="btn btn-outline" onclick="window.publicApp.fillDemoCredentials('lawyer')">
                            <i class="fas fa-balance-scale"></i> Lawyer Demo
                        </button>
                    </div>
                `;
                
                loginContainer.appendChild(demoSection);
            }
        }
    }

    fillDemoCredentials(role) {
        const usernameField = document.getElementById('username');
        const passwordField = document.getElementById('password');
        
        if (usernameField && passwordField) {
            const credentials = {
                admin: { username: 'admin', password: 'admin123' },
                police: { username: 'officer1', password: 'police123' },
                lawyer: { username: 'lawyer1', password: 'lawyer123' }
            };
            
            const creds = credentials[role];
            if (creds) {
                usernameField.value = creds.username;
                passwordField.value = creds.password;
                
                // Trigger validation
                usernameField.dispatchEvent(new Event('input'));
                passwordField.dispatchEvent(new Event('input'));
            }
        }
    }

    setupHeroAnimations() {
        // Animate hero elements on scroll
        const heroElements = document.querySelectorAll('.hero-content > *');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.classList.add('animate-in');
                    }, index * 200);
                }
            });
        }, { threshold: 0.1 });
        
        heroElements.forEach(el => observer.observe(el));
    }

    setupFeatureCards() {
        const featureCards = document.querySelectorAll('.feature-card');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, { threshold: 0.1 });
        
        featureCards.forEach(card => observer.observe(card));
    }

    setupStatsCounter() {
        const statNumbers = document.querySelectorAll('.stat-number');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateCounter(entry.target);
                }
            });
        }, { threshold: 0.5 });
        
        statNumbers.forEach(stat => observer.observe(stat));
    }

    animateCounter(element) {
        const target = element.textContent;
        const isPercentage = target.includes('%');
        const numericValue = parseFloat(target.replace(/[^\d.]/g, ''));
        
        let current = 0;
        const increment = numericValue / 50; // 50 steps
        const timer = setInterval(() => {
            current += increment;
            if (current >= numericValue) {
                current = numericValue;
                clearInterval(timer);
            }
            
            element.textContent = isPercentage ? 
                `${Math.round(current)}%` : 
                Math.round(current).toLocaleString();
        }, 40);
    }

    setupTimelineAnimations() {
        // Animate timeline items
        const timelineItems = document.querySelectorAll('.timeline-item');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, { threshold: 0.3 });
        
        timelineItems.forEach(item => observer.observe(item));
    }

    setupTeamCards() {
        // Add hover effects to team cards
        const teamCards = document.querySelectorAll('.team-card');
        
        teamCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.classList.add('hovered');
            });
            
            card.addEventListener('mouseleave', () => {
                card.classList.remove('hovered');
            });
        });
    }

    setupMissionAnimations() {
        const missionCards = document.querySelectorAll('.mission-card');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.classList.add('animate-in');
                    }, index * 300);
                }
            });
        }, { threshold: 0.2 });
        
        missionCards.forEach(card => observer.observe(card));
    }

    setupTestimonialsCarousel() {
        // Setup testimonials carousel if it exists
        const carousel = document.querySelector('.testimonials-carousel');
        if (!carousel) return;
        
        // Implement carousel functionality
        // This is a placeholder for testimonials carousel
    }

    handleContactForm(event) {
        const form = event.target;
        const formData = new FormData(form);
        
        // Show success message
        if (typeof showToast === 'function') {
            showToast('Thank you for your message. We will get back to you soon!', 'success');
        }
        
        // Reset form
        form.reset();
    }

    handleNewsletterSignup(event) {
        const form = event.target;
        const email = new FormData(form).get('email');
        
        // Show success message
        if (typeof showToast === 'function') {
            showToast('Thank you for subscribing to our newsletter!', 'success');
        }
        
        // Reset form
        form.reset();
    }

    handleDemoRequest(event) {
        const form = event.target;
        const formData = new FormData(form);
        
        // Show success message
        if (typeof showToast === 'function') {
            showToast('Demo request submitted successfully. We will contact you soon!', 'success');
        }
        
        // Reset form
        form.reset();
    }

    handleScrollEffects() {
        // Add scroll-based effects
        const scrollTop = window.pageYOffset;
        
        // Parallax effect for hero background
        const heroSection = document.querySelector('.hero-section');
        if (heroSection) {
            heroSection.style.transform = `translateY(${scrollTop * 0.5}px)`;
        }
    }

    handleResize() {
        // Handle responsive adjustments
        // This can be extended based on specific needs
    }

    getCurrentPage() {
        return this.currentPage;
    }

    isReady() {
        return this.isInitialized;
    }
}

// Initialize public app when DOM is ready
if (typeof window !== 'undefined') {
    window.addEventListener('DOMContentLoaded', () => {
        if (!window.publicApp) {
            window.publicApp = new PublicApp();
        }
    });
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PublicApp;
}
