// Authentication Manager for Multi-File Architecture
// Handles authentication state and navigation transitions

class AuthManager {
    constructor() {
        this.currentUser = null;
        this.isLoggedIn = false;
        this.loginCallbacks = [];
        this.logoutCallbacks = [];
        
        this.init();
    }

    init() {
        // Check for existing authentication on page load
        this.checkExistingAuth();
        
        // Setup event listeners
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Listen for storage changes (for multi-tab sync)
        window.addEventListener('storage', (e) => {
            if (e.key === 'authToken' || e.key === 'currentUser') {
                this.handleStorageChange(e);
            }
        });

        // Listen for beforeunload to cleanup
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }

    async checkExistingAuth() {
        const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
        
        if (token) {
            try {
                // Validate token with server
                const response = await fetch('/api/auth/validate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    await this.setAuthenticatedState(data.user, token);
                } else {
                    // Token is invalid, clear it
                    this.clearAuthData();
                }
            } catch (error) {
                console.error('AuthManager: Error validating token:', error);
                this.clearAuthData();
            }
        }
    }

    async login(credentials, rememberMe = false) {
        try {
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(credentials)
            });

            const data = await response.json();

            if (response.ok && data.success) {
                // Store authentication data
                const storage = rememberMe ? localStorage : sessionStorage;
                storage.setItem('authToken', data.token);
                storage.setItem('currentUser', JSON.stringify(data.user));

                // Set authenticated state
                await this.setAuthenticatedState(data.user, data.token);

                // Navigate to dashboard
                if (window.router) {
                    window.router.navigate('/dashboard');
                } else {
                    window.location.href = 'app/dashboard.html';
                }

                return { success: true, user: data.user };
            } else {
                return { success: false, error: data.message || 'Login failed' };
            }
        } catch (error) {
            console.error('AuthManager: Login error:', error);
            return { success: false, error: 'Network error - please try again' };
        }
    }

    async logout() {
        try {
            // Notify server of logout
            const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
            if (token) {
                await fetch('/api/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
            }
        } catch (error) {
            console.error('AuthManager: Logout error:', error);
        }

        // Clear authentication state
        await this.clearAuthenticatedState();

        // Navigate to login
        if (window.router) {
            window.router.navigate('/login');
        } else {
            window.location.href = 'public/login.html';
        }
    }

    async setAuthenticatedState(user, token) {
        this.currentUser = user;
        this.isLoggedIn = true;

        // Update API token
        if (window.api) {
            window.api.setToken(token);
        }

        // Switch navigation to authenticated mode
        if (window.navManager) {
            await window.navManager.switchToAuthenticatedMode(user);
        }

        // Notify callbacks
        this.loginCallbacks.forEach(callback => {
            try {
                callback(user);
            } catch (error) {
                console.error('AuthManager: Error in login callback:', error);
            }
        });

        console.log('AuthManager: User authenticated:', user.username);
    }

    async clearAuthenticatedState() {
        const wasLoggedIn = this.isLoggedIn;
        
        this.currentUser = null;
        this.isLoggedIn = false;

        // Clear stored data
        this.clearAuthData();

        // Clear API token
        if (window.api) {
            window.api.setToken(null);
        }

        // Switch navigation to public mode
        if (window.navManager) {
            await window.navManager.switchToPublicMode();
        }

        // Notify callbacks only if user was actually logged in
        if (wasLoggedIn) {
            this.logoutCallbacks.forEach(callback => {
                try {
                    callback();
                } catch (error) {
                    console.error('AuthManager: Error in logout callback:', error);
                }
            });
        }

        console.log('AuthManager: User logged out');
    }

    clearAuthData() {
        localStorage.removeItem('authToken');
        localStorage.removeItem('currentUser');
        sessionStorage.removeItem('authToken');
        sessionStorage.removeItem('currentUser');
    }

    handleStorageChange(event) {
        if (event.key === 'authToken') {
            if (!event.newValue && this.isLoggedIn) {
                // Token was removed in another tab, logout here too
                this.clearAuthenticatedState();
            } else if (event.newValue && !this.isLoggedIn) {
                // Token was added in another tab, check and login here too
                this.checkExistingAuth();
            }
        }
    }

    // Permission checking methods
    canUploadEvidence() {
        return this.isLoggedIn && (this.currentUser?.role === 'police' || this.currentUser?.role === 'admin');
    }

    canAccessAdmin() {
        return this.isLoggedIn && this.currentUser?.role === 'admin';
    }

    canViewEvidence() {
        return this.isLoggedIn;
    }

    canVerifyEvidence() {
        return this.isLoggedIn;
    }

    // Callback management
    onLogin(callback) {
        this.loginCallbacks.push(callback);
    }

    onLogout(callback) {
        this.logoutCallbacks.push(callback);
    }

    removeLoginCallback(callback) {
        const index = this.loginCallbacks.indexOf(callback);
        if (index > -1) {
            this.loginCallbacks.splice(index, 1);
        }
    }

    removeLogoutCallback(callback) {
        const index = this.logoutCallbacks.indexOf(callback);
        if (index > -1) {
            this.logoutCallbacks.splice(index, 1);
        }
    }

    // Utility methods
    getCurrentUser() {
        return this.currentUser;
    }

    getAuthToken() {
        return localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
    }

    isAuthenticated() {
        return this.isLoggedIn;
    }

    getUserRole() {
        return this.currentUser?.role || null;
    }

    // Session management
    async refreshToken() {
        const token = this.getAuthToken();
        if (!token) return false;

        try {
            const response = await fetch('/api/auth/refresh', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                
                // Update stored token
                const storage = localStorage.getItem('authToken') ? localStorage : sessionStorage;
                storage.setItem('authToken', data.token);
                
                // Update API token
                if (window.api) {
                    window.api.setToken(data.token);
                }

                return true;
            } else {
                // Refresh failed, logout
                await this.logout();
                return false;
            }
        } catch (error) {
            console.error('AuthManager: Token refresh error:', error);
            return false;
        }
    }

    // Setup automatic token refresh
    setupTokenRefresh() {
        // Refresh token every 30 minutes
        setInterval(() => {
            if (this.isLoggedIn) {
                this.refreshToken();
            }
        }, 30 * 60 * 1000);
    }

    cleanup() {
        // Clear any timers or cleanup resources
        this.loginCallbacks = [];
        this.logoutCallbacks = [];
    }
}

// Initialize auth manager when DOM is ready
if (typeof window !== 'undefined') {
    window.addEventListener('DOMContentLoaded', () => {
        if (!window.authManager) {
            window.authManager = new AuthManager();
            window.authManager.setupTokenRefresh();
        }
    });
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}
