<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}} - Evidence Protection System</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/auth-nav.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Meta tags for security -->
    <meta name="description" content="{{description}}">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Security headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com;">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/favicon.ico">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="../css/styles.css" as="style">
    <link rel="preload" href="../css/auth-nav.css" as="style">
    <link rel="preload" href="../js/core/router.js" as="script">
</head>
<body class="auth-layout nav-state-authenticated">
    <!-- Minimal Navigation Container -->
    <nav class="navbar auth-navbar" id="navbar">
        <div class="nav-container" id="nav-container">
            <!-- Navigation will be loaded here by NavigationManager -->
        </div>
    </nav>

    <!-- Admin Sidebar (for admin users) -->
    <div id="admin-sidebar-wrapper" class="admin-sidebar-wrapper" style="display: none;">
        <div class="admin-sidebar" id="admin-sidebar">
            <!-- Admin sidebar will be loaded here for admin users -->
        </div>
    </div>

    <!-- Main Content Area -->
    <main class="main-content auth-main" id="main-content">
        {{content}}
    </main>

    <!-- Toast Notification Container -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Modal Container -->
    <div id="modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">Modal Title</h2>
                <button class="modal-close" id="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Modal content will be inserted here -->
            </div>
            <div class="modal-footer" id="modal-footer">
                <!-- Modal footer buttons will be inserted here -->
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Confirmation Dialog -->
    <div id="confirm-dialog" class="modal" style="display: none;">
        <div class="modal-content confirm-dialog">
            <div class="modal-header">
                <h3 id="confirm-title">Confirm Action</h3>
            </div>
            <div class="modal-body">
                <p id="confirm-message">Are you sure you want to proceed?</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="confirm-cancel">Cancel</button>
                <button class="btn btn-primary" id="confirm-ok">Confirm</button>
            </div>
        </div>
    </div>

    <!-- Core JavaScript -->
    <script src="../js/core/template-loader.js"></script>
    <script src="../js/core/nav-manager.js"></script>
    <script src="../js/core/router.js"></script>
    <script src="../js/core/auth-manager.js"></script>
    
    <!-- Utility Scripts -->
    <script src="../js/utils/utils.js"></script>
    <script src="../js/utils/api.js"></script>
    
    <!-- Application Scripts -->
    <script src="../js/app/app-core.js"></script>
    
    <!-- Page-specific Scripts -->
    {{page_scripts}}

    <!-- Initialize Application -->
    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // Check authentication first
                if (!window.authManager || !window.authManager.isAuthenticated()) {
                    console.warn('User not authenticated, redirecting to login');
                    if (window.router) {
                        window.router.navigate('/login');
                    } else {
                        window.location.href = '../public/login.html';
                    }
                    return;
                }

                // Initialize template loader and load common components
                if (window.templateLoader) {
                    await window.templateLoader.setupPage('authenticated', '{{title}} - Evidence Protection System');
                }

                // Setup admin sidebar if user is admin
                if (window.authManager.canAccessAdmin()) {
                    await setupAdminSidebar();
                }

                // Initialize page-specific functionality
                if (typeof initializePage === 'function') {
                    initializePage();
                }

                // Setup session timeout warning
                setupSessionTimeout();

                // Hide loading overlay
                const loadingOverlay = document.getElementById('loading-overlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
            } catch (error) {
                console.error('Error initializing authenticated page:', error);
                
                // Show error message
                if (typeof showToast === 'function') {
                    showToast('Error loading page. Please refresh and try again.', 'error');
                }
            }
        });

        // Setup admin sidebar
        async function setupAdminSidebar() {
            const sidebarWrapper = document.getElementById('admin-sidebar-wrapper');
            if (sidebarWrapper && window.templateLoader) {
                try {
                    await window.templateLoader.injectComponent('admin-sidebar', '../shared/components/admin-sidebar.html');
                    sidebarWrapper.style.display = 'block';
                    document.body.classList.add('has-admin-sidebar');
                } catch (error) {
                    console.error('Error loading admin sidebar:', error);
                }
            }
        }

        // Setup session timeout warning
        function setupSessionTimeout() {
            let warningShown = false;
            
            // Check session every 5 minutes
            setInterval(() => {
                if (window.authManager && window.authManager.isAuthenticated()) {
                    // Check if token is close to expiring (implement based on your token structure)
                    // For now, just refresh the token
                    window.authManager.refreshToken();
                } else if (!warningShown) {
                    warningShown = true;
                    if (typeof showToast === 'function') {
                        showToast('Session expired. Please log in again.', 'warning');
                    }
                    
                    setTimeout(() => {
                        if (window.router) {
                            window.router.navigate('/login');
                        } else {
                            window.location.href = '../public/login.html';
                        }
                    }, 3000);
                }
            }, 5 * 60 * 1000); // 5 minutes
        }

        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                // Page became visible, check authentication
                if (!window.authManager || !window.authManager.isAuthenticated()) {
                    if (window.router) {
                        window.router.navigate('/login');
                    } else {
                        window.location.href = '../public/login.html';
                    }
                }
            }
        });

        // Handle online/offline status
        window.addEventListener('online', () => {
            if (typeof showToast === 'function') {
                showToast('Connection restored', 'success');
            }
        });

        window.addEventListener('offline', () => {
            if (typeof showToast === 'function') {
                showToast('Connection lost - some features may not work', 'warning');
            }
        });

        // Global error handler
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            
            if (typeof showToast === 'function') {
                showToast('An unexpected error occurred', 'error');
            }
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            
            // Check if it's an authentication error
            if (event.reason && event.reason.message && event.reason.message.includes('401')) {
                if (window.authManager) {
                    window.authManager.logout();
                }
            } else if (typeof showToast === 'function') {
                showToast('An unexpected error occurred', 'error');
            }
        });

        // Setup keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            // Ctrl+Shift+L for logout
            if (event.ctrlKey && event.shiftKey && event.key === 'L') {
                event.preventDefault();
                if (window.authManager) {
                    window.authManager.logout();
                }
            }
            
            // Escape to close modals
            if (event.key === 'Escape') {
                const modal = document.getElementById('modal');
                const confirmDialog = document.getElementById('confirm-dialog');
                
                if (modal && modal.style.display !== 'none') {
                    if (typeof hideModal === 'function') {
                        hideModal();
                    }
                }
                
                if (confirmDialog && confirmDialog.style.display !== 'none') {
                    confirmDialog.style.display = 'none';
                }
            }
        });
    </script>
</body>
</html>
