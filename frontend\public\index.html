<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Evidence Protection System - Secure Blockchain Evidence Management</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/public-nav.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Meta tags for SEO -->
    <meta name="description" content="Evidence Protection System - Next-generation blockchain-based evidence management for law enforcement and legal professionals. Secure, verified, and tamper-proof.">
    <meta name="keywords" content="evidence protection, blockchain, law enforcement, legal, security, evidence management, digital forensics">
    <meta name="author" content="Evidence Protection System">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph tags -->
    <meta property="og:title" content="Evidence Protection System - Secure Blockchain Evidence Management">
    <meta property="og:description" content="Next-generation blockchain-based evidence management for law enforcement and legal professionals">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://evidenceprotection.com">
    
    <!-- Security headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/favicon.ico">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="../css/styles.css" as="style">
    <link rel="preload" href="../js/core/router.js" as="script">
</head>
<body class="public-layout nav-state-public">
    <!-- Navigation Container -->
    <nav class="navbar public-navbar" id="navbar">
        <div class="nav-container" id="nav-container">
            <!-- Navigation will be loaded here by NavigationManager -->
        </div>
    </nav>

    <!-- Main Content Area -->
    <main class="main-content public-main">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">Evidence Protection System</h1>
                <p class="hero-subtitle">Next-generation blockchain-based evidence management for law enforcement and legal professionals</p>
                <div class="hero-buttons">
                    <button class="btn btn-primary btn-large" id="access-system-btn">
                        <i class="fas fa-key"></i>
                        Access System
                    </button>
                    <button class="btn btn-secondary btn-large" id="learn-more-btn">
                        <i class="fas fa-info-circle"></i>
                        Learn More
                    </button>
                </div>
                <div class="hero-stats">
                    <div class="stat-item">
                        <div class="stat-number">99.9%</div>
                        <div class="stat-label">Uptime</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">256-bit</div>
                        <div class="stat-label">Encryption</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">Monitoring</div>
                    </div>
                </div>
            </div>
            <div class="hero-image">
                <div class="hero-icon">
                    <i class="fas fa-balance-scale"></i>
                </div>
                <div class="hero-visual">
                    <div class="blockchain-visual">
                        <div class="block"></div>
                        <div class="block"></div>
                        <div class="block"></div>
                        <div class="connection"></div>
                        <div class="connection"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="features-section">
            <div class="section-header">
                <h2>Why Choose Our Evidence Protection System?</h2>
                <p>Advanced technology meets legal requirements for the most secure evidence management platform available</p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-vault"></i>
                    </div>
                    <h3>Military-Grade Security</h3>
                    <p>Advanced encryption and security protocols protect sensitive evidence data from unauthorized access and tampering.</p>
                    <div class="feature-details">
                        <span class="detail-badge">AES-256</span>
                        <span class="detail-badge">Zero Trust</span>
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-cubes"></i>
                    </div>
                    <h3>Blockchain Integrity</h3>
                    <p>Immutable blockchain technology ensures evidence integrity and provides cryptographic proof of authenticity.</p>
                    <div class="feature-details">
                        <span class="detail-badge">Immutable</span>
                        <span class="detail-badge">Verified</span>
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <h3>Role-Based Access</h3>
                    <p>Granular permission system ensures only authorized personnel can access specific evidence based on their role.</p>
                    <div class="feature-details">
                        <span class="detail-badge">RBAC</span>
                        <span class="detail-badge">Secure</span>
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    <h3>Complete Audit Trail</h3>
                    <p>Every action is logged and tracked, providing a complete chain of custody for legal proceedings.</p>
                    <div class="feature-details">
                        <span class="detail-badge">Tracked</span>
                        <span class="detail-badge">Compliant</span>
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <h3>Secure Infrastructure</h3>
                    <p>Enterprise-grade infrastructure ensures evidence is always accessible while maintaining the highest security standards.</p>
                    <div class="feature-details">
                        <span class="detail-badge">Cloud</span>
                        <span class="detail-badge">Scalable</span>
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-screen"></i>
                    </div>
                    <h3>Mobile Responsive</h3>
                    <p>Access the system from any device with our responsive design that works on desktop, tablet, and mobile.</p>
                    <div class="feature-details">
                        <span class="detail-badge">Responsive</span>
                        <span class="detail-badge">Cross-platform</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Trust Section -->
        <section class="trust-section">
            <div class="section-header">
                <h2>Trusted by Law Enforcement Worldwide</h2>
                <p>Join thousands of professionals who rely on our platform for their most critical evidence</p>
            </div>
            
            <div class="trust-stats">
                <div class="trust-stat">
                    <div class="stat-number">1000+</div>
                    <div class="stat-label">Active Users</div>
                </div>
                <div class="trust-stat">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">Agencies</div>
                </div>
                <div class="trust-stat">
                    <div class="stat-number">100K+</div>
                    <div class="stat-label">Evidence Files</div>
                </div>
                <div class="trust-stat">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">Verified</div>
                </div>
            </div>
            
            <div class="certifications">
                <h3>Certifications & Compliance</h3>
                <div class="cert-grid">
                    <div class="cert-item">
                        <i class="fas fa-certificate"></i>
                        <span>SOC 2 Type II</span>
                    </div>
                    <div class="cert-item">
                        <i class="fas fa-award"></i>
                        <span>ISO 27001</span>
                    </div>
                    <div class="cert-item">
                        <i class="fas fa-user-shield"></i>
                        <span>GDPR Compliant</span>
                    </div>
                    <div class="cert-item">
                        <i class="fas fa-balance-scale"></i>
                        <span>Legal Standards</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="cta-section">
            <div class="cta-content">
                <h2>Ready to Secure Your Evidence?</h2>
                <p>Join the future of evidence management with blockchain technology</p>
                <div class="cta-buttons">
                    <button class="btn btn-primary btn-large" id="get-started-btn">
                        <i class="fas fa-rocket"></i>
                        Get Started Today
                    </button>
                    <button class="btn btn-outline btn-large" id="request-demo-btn">
                        <i class="fas fa-play"></i>
                        Request Demo
                    </button>
                </div>
                <div class="cta-contact">
                    <p>Questions? Contact us at <a href="tel:+255757569733">+255-75-756-9733</a> or <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer Container -->
    <footer class="footer" id="footer">
        <div id="footer-container">
            <!-- Footer will be loaded here by TemplateLoader -->
        </div>
    </footer>

    <!-- Toast Notification Container -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Core JavaScript -->
    <script src="../js/core/template-loader.js"></script>
    <script src="../js/core/nav-manager.js"></script>
    <script src="../js/core/router.js"></script>
    <script src="../js/core/auth-manager.js"></script>
    
    <!-- Utility Scripts -->
    <script src="../js/utils/utils.js"></script>
    <script src="../js/utils/api.js"></script>
    
    <!-- Public Page Scripts -->
    <script src="../js/public/public-app.js"></script>

    <!-- Page-specific JavaScript -->
    <script>
        // Initialize home page
        function initializePage() {
            console.log('Initializing home page');
            
            // Setup button handlers
            setupButtonHandlers();
            
            // Setup animations
            setupAnimations();
            
            // Setup scroll effects
            setupScrollEffects();
        }

        function setupButtonHandlers() {
            // Access System button
            const accessBtn = document.getElementById('access-system-btn');
            if (accessBtn) {
                accessBtn.addEventListener('click', () => {
                    if (window.router) {
                        window.router.navigate('/login');
                    } else {
                        window.location.href = 'login.html';
                    }
                });
            }

            // Learn More button
            const learnMoreBtn = document.getElementById('learn-more-btn');
            if (learnMoreBtn) {
                learnMoreBtn.addEventListener('click', () => {
                    if (window.router) {
                        window.router.navigate('/about');
                    } else {
                        window.location.href = 'about.html';
                    }
                });
            }

            // Get Started button
            const getStartedBtn = document.getElementById('get-started-btn');
            if (getStartedBtn) {
                getStartedBtn.addEventListener('click', () => {
                    if (window.router) {
                        window.router.navigate('/login');
                    } else {
                        window.location.href = 'login.html';
                    }
                });
            }

            // Request Demo button
            const requestDemoBtn = document.getElementById('request-demo-btn');
            if (requestDemoBtn) {
                requestDemoBtn.addEventListener('click', () => {
                    if (typeof showToast === 'function') {
                        showToast('Demo request feature coming soon! Please contact us directly.', 'info');
                    }
                });
            }
        }

        function setupAnimations() {
            // Animate elements on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            }, observerOptions);

            // Observe feature cards
            document.querySelectorAll('.feature-card').forEach(card => {
                observer.observe(card);
            });

            // Observe trust stats
            document.querySelectorAll('.trust-stat').forEach(stat => {
                observer.observe(stat);
            });

            // Animate hero elements
            setTimeout(() => {
                document.querySelector('.hero-title')?.classList.add('animate-in');
            }, 200);
            
            setTimeout(() => {
                document.querySelector('.hero-subtitle')?.classList.add('animate-in');
            }, 400);
            
            setTimeout(() => {
                document.querySelector('.hero-buttons')?.classList.add('animate-in');
            }, 600);
        }

        function setupScrollEffects() {
            // Parallax effect for hero
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const heroImage = document.querySelector('.hero-image');
                
                if (heroImage) {
                    heroImage.style.transform = `translateY(${scrolled * 0.3}px)`;
                }
            });

            // Animate blockchain visual
            animateBlockchain();
        }

        function animateBlockchain() {
            const blocks = document.querySelectorAll('.block');
            const connections = document.querySelectorAll('.connection');
            
            // Animate blocks
            blocks.forEach((block, index) => {
                setTimeout(() => {
                    block.classList.add('active');
                }, index * 500);
            });
            
            // Animate connections
            connections.forEach((connection, index) => {
                setTimeout(() => {
                    connection.classList.add('active');
                }, (index + 1) * 500 + 250);
            });
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // Initialize template loader and load common components
                if (window.templateLoader) {
                    await window.templateLoader.setupPage('public', 'Evidence Protection System - Secure Blockchain Evidence Management');
                }

                // Initialize page-specific functionality
                initializePage();

                // Hide loading overlay
                const loadingOverlay = document.getElementById('loading-overlay');
                if (loadingOverlay) {
                    setTimeout(() => {
                        loadingOverlay.style.display = 'none';
                    }, 500);
                }
            } catch (error) {
                console.error('Error initializing home page:', error);
                
                if (typeof showToast === 'function') {
                    showToast('Error loading page. Please refresh and try again.', 'error');
                }
            }
        });
    </script>
</body>
</html>
