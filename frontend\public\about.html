<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Us - Evidence Protection System</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/public-nav.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Meta tags -->
    <meta name="description" content="Learn about Evidence Protection System - Leading the future of secure, blockchain-based evidence management for law enforcement and legal professionals worldwide">
    <meta name="keywords" content="evidence protection, about us, blockchain, law enforcement, legal, security">
    <meta name="robots" content="index, follow">
    
    <!-- Security headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/favicon.ico">
</head>
<body class="public-layout nav-state-public">
    <!-- Navigation Container -->
    <nav class="navbar public-navbar" id="navbar">
        <div class="nav-container" id="nav-container">
            <!-- Navigation will be loaded here by NavigationManager -->
        </div>
    </nav>

    <!-- Main Content Area -->
    <main class="main-content public-main">
        <!-- About Header -->
        <section class="about-header">
            <div class="about-hero">
                <h1>About Evidence Protection System</h1>
                <p>Leading the future of secure, blockchain-based evidence management for law enforcement and legal professionals worldwide</p>
            </div>
        </section>

        <!-- Mission Section -->
        <section class="mission-section">
            <div class="mission-grid">
                <div class="mission-card">
                    <div class="mission-icon">
                        <i class="fas fa-bullseye"></i>
                    </div>
                    <h2>Our Mission</h2>
                    <p>To revolutionize evidence management through cutting-edge blockchain technology, ensuring the highest levels of security, integrity, and accessibility for law enforcement and legal professionals worldwide.</p>
                </div>

                <div class="mission-card">
                    <div class="mission-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h2>Our Vision</h2>
                    <p>A world where digital evidence is completely secure, tamper-proof, and instantly verifiable, enabling justice systems to operate with unprecedented transparency and trust.</p>
                </div>

                <div class="mission-card">
                    <div class="mission-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h2>Our Values</h2>
                    <p>Security, integrity, innovation, and justice. We believe that technology should serve justice, protect the innocent, and ensure that truth prevails in every legal proceeding.</p>
                </div>
            </div>
        </section>

        <!-- Story Section -->
        <section class="story-section">
            <div class="story-content">
                <div class="story-text">
                    <h2>Our Story</h2>
                    <p>Founded in 2024, Evidence Protection System emerged from a critical need in the justice system: ensuring the absolute integrity and security of digital evidence. Our team of cybersecurity experts, blockchain developers, and legal professionals came together with a shared vision.</p>

                    <p>We recognized that traditional evidence management systems were vulnerable to tampering, loss, and unauthorized access. With the increasing digitization of evidence and the growing sophistication of cyber threats, law enforcement and legal professionals needed a revolutionary solution.</p>

                    <p>Our breakthrough came with the integration of blockchain technology, creating an immutable ledger for evidence tracking. Combined with military-grade encryption and role-based access controls, we've created the most secure evidence management platform available today.</p>

                    <p>Today, we're proud to serve law enforcement agencies, legal firms, and judicial institutions worldwide, protecting the integrity of justice through technology.</p>
                </div>
                <div class="story-stats">
                    <div class="stat-highlight">
                        <div class="stat-number">100%</div>
                        <div class="stat-text">Evidence Integrity Guaranteed</div>
                    </div>
                    <div class="stat-highlight">
                        <div class="stat-number">24/7</div>
                        <div class="stat-text">Security Monitoring</div>
                    </div>
                    <div class="stat-highlight">
                        <div class="stat-number">256-bit</div>
                        <div class="stat-text">Military-Grade Encryption</div>
                    </div>
                    <div class="stat-highlight">
                        <div class="stat-number">99.9%</div>
                        <div class="stat-text">System Uptime</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Why Choose Us Section -->
        <section class="why-choose-section">
            <h2>Why Choose Us?</h2>
            <div class="reasons-grid">
                <div class="reason-card">
                    <i class="fas fa-award"></i>
                    <h3>Industry Leading</h3>
                    <p>Recognized as the most advanced evidence protection platform by cybersecurity experts and legal professionals.</p>
                </div>
                <div class="reason-card">
                    <i class="fas fa-users-cog"></i>
                    <h3>Expert Team</h3>
                    <p>Our team combines decades of experience in cybersecurity, blockchain technology, and legal systems.</p>
                </div>
                <div class="reason-card">
                    <i class="fas fa-certificate"></i>
                    <h3>Certified Secure</h3>
                    <p>SOC 2 compliant, ISO 27001 certified, and GDPR ready. We meet the highest international security standards.</p>
                </div>
                <div class="reason-card">
                    <i class="fas fa-handshake"></i>
                    <h3>Trusted Partner</h3>
                    <p>Trusted by law enforcement agencies and legal firms worldwide for their most sensitive evidence.</p>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="about-cta">
            <div class="cta-content">
                <h2>Ready to Secure Your Evidence?</h2>
                <p>Join thousands of law enforcement professionals who trust our platform with their most critical evidence.</p>
                <div class="cta-buttons">
                    <button class="btn btn-primary" id="access-system-btn">
                        <i class="fas fa-sign-in-alt"></i>
                        Access System
                    </button>
                    <div class="contact-info-inline">
                        <p><i class="fas fa-phone"></i> +255-75-756-9733</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer Container -->
    <footer class="footer" id="footer">
        <div id="footer-container">
            <!-- Footer will be loaded here by TemplateLoader -->
        </div>
    </footer>

    <!-- Toast Notification Container -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Core JavaScript -->
    <script src="../js/core/template-loader.js"></script>
    <script src="../js/core/nav-manager.js"></script>
    <script src="../js/core/router.js"></script>
    <script src="../js/core/auth-manager.js"></script>
    
    <!-- Utility Scripts -->
    <script src="../js/utils/utils.js"></script>
    <script src="../js/utils/api.js"></script>
    
    <!-- Public Page Scripts -->
    <script src="../js/public/public-app.js"></script>

    <!-- Page-specific JavaScript -->
    <script>
        function initializePage() {
            console.log('Initializing about page');
            
            // Setup button handlers
            const accessBtn = document.getElementById('access-system-btn');
            if (accessBtn) {
                accessBtn.addEventListener('click', () => {
                    if (window.router) {
                        window.router.navigate('/login');
                    } else {
                        window.location.href = 'login.html';
                    }
                });
            }
            
            // Setup animations
            setupAnimations();
        }

        function setupAnimations() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            }, { threshold: 0.1 });

            document.querySelectorAll('.mission-card, .reason-card, .stat-highlight').forEach(el => {
                observer.observe(el);
            });
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                if (window.templateLoader) {
                    await window.templateLoader.setupPage('public', 'About Us - Evidence Protection System');
                }

                initializePage();

                const loadingOverlay = document.getElementById('loading-overlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
            } catch (error) {
                console.error('Error initializing about page:', error);
                if (typeof showToast === 'function') {
                    showToast('Error loading page. Please refresh and try again.', 'error');
                }
            }
        });
    </script>
</body>
</html>
