<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}} - Evidence Protection System</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/public-nav.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Meta tags for SEO and security -->
    <meta name="description" content="{{description}}">
    <meta name="keywords" content="evidence protection, blockchain, law enforcement, legal, security">
    <meta name="author" content="Evidence Protection System">
    <meta name="robots" content="index, follow">
    
    <!-- Security headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/favicon.ico">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="../css/styles.css" as="style">
    <link rel="preload" href="../js/core/router.js" as="script">
</head>
<body class="public-layout nav-state-public">
    <!-- Navigation Container -->
    <nav class="navbar public-navbar" id="navbar">
        <div class="nav-container" id="nav-container">
            <!-- Navigation will be loaded here by NavigationManager -->
        </div>
    </nav>

    <!-- Main Content Area -->
    <main class="main-content public-main">
        {{content}}
    </main>

    <!-- Footer Container -->
    <footer class="footer" id="footer">
        <div id="footer-container">
            <!-- Footer will be loaded here by TemplateLoader -->
        </div>
    </footer>

    <!-- Toast Notification Container -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Modal Container -->
    <div id="modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">Modal Title</h2>
                <button class="modal-close" id="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Modal content will be inserted here -->
            </div>
            <div class="modal-footer" id="modal-footer">
                <!-- Modal footer buttons will be inserted here -->
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Core JavaScript -->
    <script src="../js/core/template-loader.js"></script>
    <script src="../js/core/nav-manager.js"></script>
    <script src="../js/core/router.js"></script>
    <script src="../js/core/auth-manager.js"></script>
    
    <!-- Utility Scripts -->
    <script src="../js/utils/utils.js"></script>
    <script src="../js/utils/api.js"></script>
    
    <!-- Public Page Scripts -->
    <script src="../js/public/public-app.js"></script>
    
    <!-- Page-specific Scripts -->
    {{page_scripts}}

    <!-- Initialize Application -->
    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // Initialize template loader and load common components
                if (window.templateLoader) {
                    await window.templateLoader.setupPage('public', '{{title}} - Evidence Protection System');
                }

                // Initialize page-specific functionality
                if (typeof initializePage === 'function') {
                    initializePage();
                }

                // Hide loading overlay
                const loadingOverlay = document.getElementById('loading-overlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
            } catch (error) {
                console.error('Error initializing public page:', error);
                
                // Show error message
                if (typeof showToast === 'function') {
                    showToast('Error loading page. Please refresh and try again.', 'error');
                }
            }
        });

        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                // Page became visible, check for updates
                if (window.authManager && window.authManager.isAuthenticated()) {
                    // Redirect authenticated users to dashboard
                    if (window.router) {
                        window.router.navigate('/dashboard');
                    } else {
                        window.location.href = '../app/dashboard.html';
                    }
                }
            }
        });

        // Handle online/offline status
        window.addEventListener('online', () => {
            if (typeof showToast === 'function') {
                showToast('Connection restored', 'success');
            }
        });

        window.addEventListener('offline', () => {
            if (typeof showToast === 'function') {
                showToast('Connection lost - working offline', 'warning');
            }
        });

        // Global error handler
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            
            if (typeof showToast === 'function') {
                showToast('An unexpected error occurred', 'error');
            }
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            
            if (typeof showToast === 'function') {
                showToast('An unexpected error occurred', 'error');
            }
        });
    </script>
</body>
</html>
